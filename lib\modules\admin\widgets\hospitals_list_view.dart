import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:fila_app/modules/admin/controllers/admin_controller.dart';
import 'package:fila_app/theme/theme.dart';
import 'package:fila_app/theme/constants.dart';
import 'package:fila_app/modules/admin/widgets/hospital_card.dart';

class HospitalsListView extends StatelessWidget {
  final AdminController controller = Get.find<AdminController>();
  final List<dynamic>? hospitals;

  HospitalsListView({
    super.key,
    this.hospitals,
  });

  Future<void> _showResetCredentialsDialog(
    BuildContext context,
    String hospitalId,
    String hospitalName,
  ) async {
    final confirmReset = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Gerar Nova Credencial'),
        content: const Text(
            'Esta ação irá gerar uma nova senha para o usuário deste hospital. A nova senha será exibida para você anotar. Deseja prosseguir?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancelar'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: FilledButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
            ),
            child: const Text('Confirmar'),
          ),
        ],
      ),
    );

    if (confirmReset != true || !context.mounted) return;

    // Mostrar indicador de progresso
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Gerando nova credencial...'),
          ],
        ),
      ),
    );

    try {
      // Primeiro tenta buscar o email diretamente da tabela User
      final email = await controller.buscarEmailDiretoTabelaUser(hospitalId);

      String? emailToUse = email;
      if (context.mounted) {
        Navigator.of(context).pop(); // Fechar o diálogo de carregamento
      }

      // Se não encontrou email, pedir para o admin digitar
      if (emailToUse == null || emailToUse.isEmpty) {
        if (!context.mounted) return;

        emailToUse = await _askForEmailDialog(context, hospitalName);
        if (emailToUse == null) return; // Admin cancelou a operação
      }

      if (!context.mounted) return;

      // Mostrar indicador de progresso novamente
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Finalizando geração de credencial...'),
            ],
          ),
        ),
      );

      // Gerar nova credencial
      final credentials = await controller.gerarNovaCredencial(hospitalId);

      if (!context.mounted) return;
      Navigator.of(context).pop(); // Fechar o diálogo de carregamento

      if (credentials != null) {
        if (!context.mounted) return;
        _showCredentialsDialog(
          context,
          credentials['nome'] as String,
          credentials['email'] as String,
          credentials['senha'] as String,
        );
      } else {
        if (!context.mounted) return;
        _showSnackbar(
          context,
          'Erro ao gerar credenciais: ${controller.errorMessage.value}',
          isError: true,
        );
      }
    } catch (e) {
      if (!context.mounted) return;
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop(); // Fechar qualquer diálogo aberto
      }
      _showSnackbar(
        context,
        'Erro ao gerar credenciais: $e',
        isError: true,
      );
    }
  }

  Future<String?> _askForEmailDialog(
      BuildContext context, String hospitalName) async {
    final controller = TextEditingController();
    controller.text =
        '${hospitalName.toLowerCase().replaceAll(' ', '_')}@seuhospital.com';

    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Email para $hospitalName'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
                'Não foi encontrado um email associado a este hospital. Por favor, digite um email para criar as credenciais:'),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              decoration: const InputDecoration(
                labelText: 'Email',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          FilledButton(
            onPressed: () {
              if (controller.text.isNotEmpty) {
                Navigator.of(context).pop(controller.text);
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Por favor, digite um email válido'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            style: FilledButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
            ),
            child: const Text('Confirmar'),
          ),
        ],
      ),
    );
  }

  void _showCredentialsDialog(
    BuildContext context,
    String name,
    String email,
    String password,
  ) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.security, color: AppTheme.primaryColor),
            const SizedBox(width: 8),
            Expanded(child: Text('Credenciais para $name')),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'As credenciais foram geradas com sucesso:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              _buildCredentialItem(
                icon: Icons.email,
                label: 'Email:',
                value: email,
                context: context,
              ),
              const SizedBox(height: 12),
              _buildCredentialItem(
                icon: Icons.password,
                label: 'Senha:',
                value: password,
                context: context,
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.warning, color: Colors.red, size: 20),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Por favor, anote ou copie essas informações. Elas não serão exibidas novamente.',
                        style: TextStyle(
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () async {
              await controller.copyToClipboard(
                'Hospital: $name\nEmail: $email\nSenha: $password',
              );
              if (context.mounted) {
                _showSnackbar(context, 'Todas as credenciais copiadas');
              }
            },
            child: const Text('Copiar Tudo'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(),
            style: FilledButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
            ),
            child: const Text('Entendi'),
          ),
        ],
      ),
    );
  }

  Widget _buildCredentialItem({
    required IconData icon,
    required String label,
    required String value,
    required BuildContext context,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          Icon(icon,
              size: 18, color: icon == Icons.email ? Colors.blue : Colors.red),
          const SizedBox(width: 8),
          Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          Expanded(
            child: SelectableText(
              value,
              style: const TextStyle(fontSize: 16),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.copy, size: 18),
            onPressed: () {
              controller.copyToClipboard(value);
              _showSnackbar(context, '$label copiado');
            },
            tooltip: 'Copiar $label',
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
        ],
      ),
    );
  }

  Future<void> _confirmDeleteHospital(
      BuildContext context, ParseObject hospital) async {
    final hospitalName = hospital.get<String>('nome') ?? 'Hospital sem nome';

    final confirmDelete = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.warning, color: Colors.red),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Confirmar Exclusão',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Colors.red,
                    ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tem certeza que deseja excluir o consultório "$hospitalName"?',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            Text('Esta ação:', style: Theme.of(context).textTheme.bodyMedium),
            const SizedBox(height: 8),
            _buildWarningItem(
                context, 'Excluirá permanentemente este consultório'),
            _buildWarningItem(
                context, 'Excluirá o usuário associado ao consultório'),
            _buildWarningItem(context, 'NÃO pode ser desfeita'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red),
              ),
              child: const Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.red, size: 20),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Certifique-se de que não existem médicos ou secretárias vinculados a este consultório antes de excluí-lo.',
                      style: TextStyle(color: Colors.red),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancelar'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: FilledButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('Excluir Definitivamente'),
          ),
        ],
      ),
    );

    if (confirmDelete != true || !context.mounted) return;

    // Mostrar indicador de progresso
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Excluindo consultório...'),
          ],
        ),
      ),
    );

    try {
      final result = await controller.excluirHospital(hospital.objectId!);

      if (!context.mounted) return;
      Navigator.of(context).pop(); // Fechar o diálogo de carregamento

      if (result) {
        _showSnackbar(context, 'Consultório excluído com sucesso');
      } else {
        _showSnackbar(
          context,
          'Erro: ${controller.errorMessage.value}',
          isError: true,
        );
      }
    } catch (e) {
      if (!context.mounted) return;
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop(); // Fechar o diálogo de carregamento
      }
      _showSnackbar(
        context,
        'Erro ao excluir consultório: $e',
        isError: true,
      );
    }
  }

  Widget _buildWarningItem(BuildContext context, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          const Icon(Icons.remove_circle, color: Colors.red, size: 16),
          const SizedBox(width: 8),
          Expanded(
              child: Text(text, style: Theme.of(context).textTheme.bodyMedium)),
        ],
      ),
    );
  }

  Future<void> _updateHospitalStatus(
    BuildContext context,
    String hospitalId,
    bool newStatus,
  ) async {
    final confirmUpdate = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Confirmar Alteração'),
        content: Text(
          newStatus
              ? 'Deseja ativar este hospital?'
              : 'Deseja desativar este hospital?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancelar'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: FilledButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
            ),
            child: const Text('Confirmar'),
          ),
        ],
      ),
    );

    if (confirmUpdate != true || !context.mounted) return;

    // ✅ ATUALIZAÇÃO OTIMÍSTICA: Atualizar a UI imediatamente
    // Encontrar o hospital na lista e atualizar seu status localmente
    final List<dynamic> hospitalsToShow = hospitals ?? controller.hospitais;
    final hospitalIndex =
        hospitalsToShow.indexWhere((h) => h.objectId == hospitalId);

    if (hospitalIndex >= 0) {
      final originalStatus =
          hospitalsToShow[hospitalIndex].get<bool>('ativo') ?? false;

      // Atualizar o status localmente para feedback visual imediato
      hospitalsToShow[hospitalIndex].set('ativo', newStatus);

      // ✅ FORÇAR ATUALIZAÇÃO IMEDIATA DAS MÉTRICAS DO DASHBOARD
      // Usar o método estático para atualizar as métricas otimisticamente
      AdminController.updateDashboardMetricsOptimistically(
        originalStatus: originalStatus,
        newStatus: newStatus,
      );

      // Forçar atualização da UI
      controller.update();

      // Mostrar feedback de sucesso imediato
      _showSnackbar(
        context,
        newStatus ? 'Ativando hospital...' : 'Desativando hospital...',
      );

      // Agora fazer a requisição para o servidor
      try {
        final result =
            await controller.atualizarStatusHospital(hospitalId, newStatus);

        if (!context.mounted) return;

        if (result) {
          // Sucesso - mostrar confirmação final
          _showSnackbar(
            context,
            newStatus
                ? 'Hospital ativado com sucesso!'
                : 'Hospital desativado com sucesso!',
          );
        } else {
          // Falha - reverter a mudança no hospital e nas métricas
          hospitalsToShow[hospitalIndex].set('ativo', originalStatus);

          // Reverter as métricas
          AdminController.revertDashboardMetrics(
            originalStatus: originalStatus,
            newStatus: newStatus,
          );

          controller.update();

          _showSnackbar(
            context,
            'Erro ao alterar status: ${controller.errorMessage.value}',
            isError: true,
          );
        }
      } catch (e) {
        // Erro na requisição - reverter a mudança no hospital e nas métricas
        if (hospitalIndex >= 0 && hospitalIndex < hospitalsToShow.length) {
          hospitalsToShow[hospitalIndex].set('ativo', originalStatus);

          // Reverter as métricas
          AdminController.revertDashboardMetrics(
            originalStatus: originalStatus,
            newStatus: newStatus,
          );

          controller.update();
        }

        if (context.mounted) {
          _showSnackbar(
            context,
            'Erro de conexão. Tente novamente.',
            isError: true,
          );
        }
      }
    }
  }

  void _showSnackbar(BuildContext context, String message,
      {bool isError = false}) {
    ScaffoldMessenger.of(context).hideCurrentSnackBar();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isError ? Icons.error : Icons.check_circle,
              color: Colors.white,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
              ),
            ),
          ],
        ),
        backgroundColor: isError ? Colors.red.shade700 : Colors.green.shade700,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSpacing.borderRadius),
        ),
        margin: const EdgeInsets.all(8),
        action: SnackBarAction(
          label: 'OK',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final List<dynamic> hospitalsToShow = hospitals ?? controller.hospitais;

    return Obx(() {
      if (controller.isLoading) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                valueColor:
                    AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
              ),
              const SizedBox(height: 16),
              const Text('Carregando consultórios...',
                  style: TextStyle(fontSize: 16)),
            ],
          ),
        );
      }

      if (hospitalsToShow.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.local_hospital_outlined,
                size: 80,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 20),
              Text(
                'Nenhum consultório cadastrado',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Colors.grey[600],
                      fontSize: 18,
                    ),
              ),
              const SizedBox(height: 24),
              FilledButton.icon(
                onPressed: controller.carregarHospitais,
                icon: const Icon(Icons.refresh),
                label: const Text('Atualizar'),
                style: FilledButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
            ],
          ),
        );
      }

      return RefreshIndicator(
        onRefresh: controller.carregarHospitais,
        color: AppTheme.primaryColor,
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.screenMargin),
          child: LayoutBuilder(
            builder: (context, constraints) {
              return constraints.maxWidth > 600
                  ? GridView.builder(
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: 1.3,
                        crossAxisSpacing: AppSpacing.md,
                        mainAxisSpacing: AppSpacing.md,
                      ),
                      itemCount: hospitalsToShow.length,
                      itemBuilder: (context, index) {
                        final hospital = hospitalsToShow[index];
                        return HospitalCard(
                          hospital: hospital,
                          onEdit: () =>
                              controller.prepararEdicaoHospital(hospital),
                          onDelete: () =>
                              _confirmDeleteHospital(context, hospital),
                          onResetCredentials: () => _showResetCredentialsDialog(
                            context,
                            hospital.objectId!,
                            hospital.get<String>('nome') ?? 'Hospital',
                          ),
                          onStatusChange: (newStatus) => _updateHospitalStatus(
                            context,
                            hospital.objectId!,
                            newStatus,
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      itemCount: hospitalsToShow.length,
                      itemBuilder: (context, index) {
                        final hospital = hospitalsToShow[index];
                        return Padding(
                          padding: const EdgeInsets.only(bottom: AppSpacing.md),
                          child: HospitalCard(
                            hospital: hospital,
                            onEdit: () =>
                                controller.prepararEdicaoHospital(hospital),
                            onDelete: () =>
                                _confirmDeleteHospital(context, hospital),
                            onResetCredentials: () =>
                                _showResetCredentialsDialog(
                              context,
                              hospital.objectId!,
                              hospital.get<String>('nome') ?? 'Hospital',
                            ),
                            onStatusChange: (newStatus) =>
                                _updateHospitalStatus(
                              context,
                              hospital.objectId!,
                              newStatus,
                            ),
                          ),
                        );
                      },
                    );
            },
          ),
        ),
      );
    });
  }
}
