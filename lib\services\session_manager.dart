import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'dart:async';
import 'dart:convert';

/// 🔐 SessionManager - Gerenciador de Sessão Robusto
///
/// Funcionalidades:
/// - Persistência automática de sessão
/// - Auto-refresh de tokens
/// - Detecção de inatividade
/// - Renovação silenciosa de sessão
/// - Suporte a todos os tipos de usuário
class SessionManager extends GetxController {
  // ========================================
  // 🔧 CONSTANTES E CONFIGURAÇÕES
  // ========================================

  static const String _sessionKey = 'user_session_data';
  static const String _lastActiveKey = 'last_active_time';
  static const String _autoLoginKey = 'auto_login_enabled';
  static const Duration _sessionTimeout = Duration(days: 30); // 30 dias
  static const Duration _refreshInterval =
      Duration(hours: 1); // Refresh a cada hora
  static const Duration _inactivityTimeout =
      Duration(days: 7); // 7 dias de inatividade

  // ========================================
  // 🚀 ESTADO REATIVO
  // ========================================

  final RxBool isSessionActive = false.obs;
  final RxBool isAutoLoginEnabled = true.obs;
  final RxBool isRefreshingSession = false.obs;
  final RxString currentUserType = ''.obs;
  final RxString currentUserId = ''.obs;
  final Rx<DateTime?> lastActiveTime = Rx<DateTime?>(null);
  final RxMap<String, dynamic> sessionData = <String, dynamic>{}.obs;

  // ========================================
  // ⚙️ TIMERS E CONTROLADORES
  // ========================================

  Timer? _refreshTimer;
  Timer? _inactivityTimer;

  // ========================================
  // 🚀 LIFECYCLE
  // ========================================

  @override
  void onInit() {
    super.onInit();
    _initializeSessionManager();
  }

  @override
  void onClose() {
    _refreshTimer?.cancel();
    _inactivityTimer?.cancel();
    super.onClose();
  }

  /// Inicialização do gerenciador de sessão
  Future<void> _initializeSessionManager() async {
    try {
      debugPrint('🔐 Inicializando SessionManager...');

      // Carregar configurações
      await _loadSessionPreferences();

      // Verificar sessão existente
      await _checkExistingSession();

      // Configurar timers
      _setupRefreshTimer();
      _setupInactivityTimer();

      debugPrint('✅ SessionManager inicializado com sucesso');
    } catch (e) {
      debugPrint('❌ Erro ao inicializar SessionManager: $e');
    }
  }

  // ========================================
  // 💾 PERSISTÊNCIA DE SESSÃO
  // ========================================

  /// Salvar sessão após login bem-sucedido
  Future<bool> saveSession({
    required ParseUser user,
    required String userType,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      debugPrint('💾 Salvando sessão para usuário: ${user.objectId}');

      final sessionInfo = {
        'userId': user.objectId,
        'username': user.username,
        'email': user.emailAddress ?? user.get<String>('email'),
        'userType': userType,
        'sessionToken': user.sessionToken,
        'createdAt': DateTime.now().toIso8601String(),
        'lastRefresh': DateTime.now().toIso8601String(),
        'additionalData': additionalData ?? {},
      };

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_sessionKey, json.encode(sessionInfo));
      await prefs.setBool(_autoLoginKey, isAutoLoginEnabled.value);
      await _updateLastActiveTime();

      // Atualizar estado
      sessionData.value = sessionInfo;
      currentUserId.value = user.objectId ?? '';
      currentUserType.value = userType;
      isSessionActive.value = true;

      debugPrint('✅ Sessão salva com sucesso');
      return true;
    } catch (e) {
      debugPrint('❌ Erro ao salvar sessão: $e');
      return false;
    }
  }

  /// Carregar sessão existente
  Future<Map<String, dynamic>?> loadSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sessionJson = prefs.getString(_sessionKey);

      if (sessionJson == null) return null;

      final sessionInfo = json.decode(sessionJson) as Map<String, dynamic>;

      // Verificar se a sessão não expirou
      final createdAt = DateTime.parse(sessionInfo['createdAt']);
      final now = DateTime.now();

      if (now.difference(createdAt) > _sessionTimeout) {
        debugPrint('⚠️ Sessão expirada, removendo...');
        await clearSession();
        return null;
      }

      // Verificar inatividade
      final lastActive = await _getLastActiveTime();
      if (lastActive != null &&
          now.difference(lastActive) > _inactivityTimeout) {
        debugPrint('⚠️ Usuário inativo há muito tempo, limpando sessão...');
        await clearSession();
        return null;
      }

      return sessionInfo;
    } catch (e) {
      debugPrint('❌ Erro ao carregar sessão: $e');
      return null;
    }
  }

  /// Limpar sessão
  Future<void> clearSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_sessionKey);
      await prefs.remove(_lastActiveKey);

      // Resetar estado
      sessionData.clear();
      currentUserId.value = '';
      currentUserType.value = '';
      isSessionActive.value = false;
      lastActiveTime.value = null;

      debugPrint('🧹 Sessão limpa com sucesso');
    } catch (e) {
      debugPrint('❌ Erro ao limpar sessão: $e');
    }
  }

  // ========================================
  // 🔄 AUTO-LOGIN E VALIDAÇÃO
  // ========================================

  /// Tentar auto-login baseado na sessão salva
  Future<Map<String, dynamic>> attemptAutoLogin() async {
    try {
      debugPrint('🔄 Tentando auto-login...');

      if (!isAutoLoginEnabled.value) {
        return {'success': false, 'reason': 'Auto-login desabilitado'};
      }

      final sessionInfo = await loadSession();
      if (sessionInfo == null) {
        return {'success': false, 'reason': 'Nenhuma sessão válida encontrada'};
      }

      // Verificar se o usuário ainda existe no Parse Server
      final isValid = await _validateSessionWithServer(sessionInfo);
      if (!isValid) {
        await clearSession();
        return {'success': false, 'reason': 'Sessão inválida no servidor'};
      }

      // Atualizar estado local
      sessionData.value = sessionInfo;
      currentUserId.value = sessionInfo['userId'] ?? '';
      currentUserType.value = sessionInfo['userType'] ?? '';
      isSessionActive.value = true;
      await _updateLastActiveTime();

      debugPrint('✅ Auto-login realizado com sucesso');

      return {
        'success': true,
        'user': sessionInfo,
        'userType': sessionInfo['userType'],
        'route': _getRouteForUserType(sessionInfo['userType']),
      };
    } catch (e) {
      debugPrint('❌ Erro no auto-login: $e');
      return {'success': false, 'reason': 'Erro técnico: $e'};
    }
  }

  /// Validar sessão com o servidor
  Future<bool> _validateSessionWithServer(
      Map<String, dynamic> sessionInfo) async {
    try {
      isRefreshingSession.value = true;

      final userId = sessionInfo['userId'];
      final sessionToken = sessionInfo['sessionToken'];

      if (userId == null || sessionToken == null) return false;

      // Criar usuário temporário para validação
      final tempUser = ParseUser.forQuery()..objectId = userId;

      // Tentar obter dados atualizados
      final response = await tempUser.getUpdatedUser();

      if (response.success) {
        // Atualizar token se necessário
        if (tempUser.sessionToken != sessionToken) {
          sessionInfo['sessionToken'] = tempUser.sessionToken;
          sessionInfo['lastRefresh'] = DateTime.now().toIso8601String();

          final prefs = await SharedPreferences.getInstance();
          await prefs.setString(_sessionKey, json.encode(sessionInfo));
        }

        return true;
      }

      return false;
    } catch (e) {
      debugPrint('❌ Erro ao validar sessão: $e');
      return false;
    } finally {
      isRefreshingSession.value = false;
    }
  }

  // ========================================
  // ⏰ GESTÃO DE ATIVIDADE
  // ========================================

  /// Atualizar timestamp de última atividade
  Future<void> updateActivity() async {
    if (isSessionActive.value) {
      await _updateLastActiveTime();
    }
  }

  /// Registrar atividade do usuário
  Future<void> _updateLastActiveTime() async {
    try {
      final now = DateTime.now();
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastActiveKey, now.toIso8601String());
      lastActiveTime.value = now;
    } catch (e) {
      debugPrint('❌ Erro ao atualizar última atividade: $e');
    }
  }

  /// Obter timestamp da última atividade
  Future<DateTime?> _getLastActiveTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timeString = prefs.getString(_lastActiveKey);
      return timeString != null ? DateTime.parse(timeString) : null;
    } catch (e) {
      return null;
    }
  }

  // ========================================
  // 🔄 REFRESH AUTOMÁTICO
  // ========================================

  /// Configurar timer de refresh automático
  void _setupRefreshTimer() {
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(_refreshInterval, (_) {
      if (isSessionActive.value && !isRefreshingSession.value) {
        _refreshSessionSilently();
      }
    });
  }

  /// Refresh silencioso da sessão
  Future<void> _refreshSessionSilently() async {
    try {
      final sessionInfo = sessionData.value;
      if (sessionInfo.isEmpty) return;

      final isValid = await _validateSessionWithServer(sessionInfo);
      if (!isValid) {
        debugPrint(
            '⚠️ Sessão inválida detectada no refresh, fazendo logout...');
        await logout();
      }
    } catch (e) {
      debugPrint('❌ Erro no refresh silencioso: $e');
    }
  }

  // ========================================
  // ⏱️ TIMER DE INATIVIDADE
  // ========================================

  /// Configurar timer de inatividade
  void _setupInactivityTimer() {
    _inactivityTimer?.cancel();
    _inactivityTimer = Timer.periodic(const Duration(hours: 1), (_) {
      _checkInactivity();
    });
  }

  /// Verificar inatividade
  Future<void> _checkInactivity() async {
    try {
      if (!isSessionActive.value) return;

      final lastActive = await _getLastActiveTime();
      if (lastActive == null) return;

      final inactiveTime = DateTime.now().difference(lastActive);

      if (inactiveTime > _inactivityTimeout) {
        debugPrint(
            '⚠️ Usuário inativo há ${inactiveTime.inDays} dias, fazendo logout automático...');
        await logout();
      }
    } catch (e) {
      debugPrint('❌ Erro ao verificar inatividade: $e');
    }
  }

  // ========================================
  // 🚪 LOGOUT
  // ========================================

  /// Fazer logout completo
  Future<void> logout() async {
    try {
      debugPrint('🚪 Realizando logout...');

      // Fazer logout no Parse Server
      final currentUser = await ParseUser.currentUser() as ParseUser?;
      if (currentUser != null) {
        await currentUser.logout();
      }

      // Limpar sessão local
      await clearSession();

      // Cancelar timers
      _refreshTimer?.cancel();
      _inactivityTimer?.cancel();

      debugPrint('✅ Logout realizado com sucesso');
    } catch (e) {
      debugPrint('❌ Erro no logout: $e');
    }
  }

  // ========================================
  // ⚙️ MÉTODOS AUXILIARES
  // ========================================

  /// Carregar preferências de sessão
  Future<void> _loadSessionPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      isAutoLoginEnabled.value = prefs.getBool(_autoLoginKey) ?? true;
    } catch (e) {
      debugPrint('❌ Erro ao carregar preferências: $e');
    }
  }

  /// Verificar sessão existente na inicialização
  Future<void> _checkExistingSession() async {
    final sessionInfo = await loadSession();
    if (sessionInfo != null) {
      sessionData.value = sessionInfo;
      currentUserId.value = sessionInfo['userId'] ?? '';
      currentUserType.value = sessionInfo['userType'] ?? '';
      isSessionActive.value = true;
    }
  }

  /// Obter rota baseada no tipo de usuário
  String _getRouteForUserType(String userType) {
    switch (userType) {
      case 'medico':
        return '/medico';
      case 'consultorio':
        return '/hospital';
      case 'secretaria':
        return '/home_secretaria';
      case 'admin':
        return '/admin_hospitais';
      default:
        return '/login';
    }
  }

  // ========================================
  // 📱 CONFIGURAÇÕES DO USUÁRIO
  // ========================================

  /// Ativar/desativar auto-login
  Future<void> setAutoLoginEnabled(bool enabled) async {
    try {
      isAutoLoginEnabled.value = enabled;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_autoLoginKey, enabled);

      debugPrint('⚙️ Auto-login ${enabled ? 'ativado' : 'desativado'}');
    } catch (e) {
      debugPrint('❌ Erro ao configurar auto-login: $e');
    }
  }

  /// Obter informações da sessão atual
  Map<String, dynamic> getSessionInfo() {
    return {
      'isActive': isSessionActive.value,
      'userId': currentUserId.value,
      'userType': currentUserType.value,
      'lastActive': lastActiveTime.value?.toIso8601String(),
      'autoLoginEnabled': isAutoLoginEnabled.value,
      'isRefreshing': isRefreshingSession.value,
    };
  }

  /// Verificar se a sessão está próxima do vencimento
  bool isSessionNearExpiry() {
    final sessionInfo = sessionData.value;
    if (sessionInfo.isEmpty) return false;

    final createdAt = DateTime.parse(sessionInfo['createdAt']);
    final now = DateTime.now();
    final timeLeft = _sessionTimeout - now.difference(createdAt);

    return timeLeft < const Duration(days: 3); // Avisar quando restam 3 dias
  }

  /// Forçar refresh da sessão
  Future<bool> forceRefreshSession() async {
    if (!isSessionActive.value) return false;

    final sessionInfo = sessionData.value;
    if (sessionInfo.isEmpty) return false;

    return await _validateSessionWithServer(sessionInfo);
  }
}
