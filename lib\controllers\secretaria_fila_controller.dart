/* lib/controllers/secretaria_fila_controller.dart */
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import '../utils/whatsapp_utils.dart';
import '../utils/date_utils.dart'; // ✅ IMPORT DO NOVO SISTEMA DE TIMEZONE

// Sistema de exceções recomendado
class AppException implements Exception {
  final String message;
  final dynamic cause;

  AppException(this.message, [this.cause]);

  @override
  String toString() =>
      'AppException: $message${cause != null ? '\nCause: $cause' : ''}';
}

class NetworkException extends AppException {
  NetworkException(super.message, [super.cause]);
}

class AuthException extends AppException {
  AuthException(super.message, [super.cause]);
}

class SecretariaFilaController extends GetxController {
  final RxList<ParseObject> solicitacoesPendentes = <ParseObject>[].obs;
  final RxList<ParseObject> pacientesNaFila = <ParseObject>[].obs;
  final RxList<ParseObject> filas = <ParseObject>[].obs;
  final RxBool isLoading = false.obs;
  final RxString error = ''.obs;
  final RxString successMessage = ''.obs;

  ParseObject? currentSecretaria;
  ParseObject? currentHospital;
  Timer? refreshTimer;

  // LiveQuery
  LiveQuery? liveQuery;
  Subscription? filaSubscription;
  Subscription? solicitacaoSubscription;
  Subscription? filaUpdateSubscription;
  Timer? reconnectTimer;

  @override
  void onInit() {
    super.onInit();
    carregarDadosSecretaria();
    // Substituímos o polling por LiveQuery
    // _iniciarAtualizacaoAutomatica();
  }

  @override
  void onClose() {
    refreshTimer?.cancel();
    _cancelarLiveQuery();
    reconnectTimer?.cancel();
    super.onClose();
  }

  // Inicializar LiveQuery para atualizações em tempo real
  Future<void> _iniciarLiveQuery() async {
    try {
      debugPrint('Inicializando LiveQuery para secretária');

      // Cancelar qualquer inscrição existente
      _cancelarLiveQuery();

      // Verificar se temos o hospital atual
      if (currentHospital == null) {
        debugPrint('Hospital não encontrado, não é possível iniciar LiveQuery');
        return;
      }

      // Inicializar LiveQuery
      liveQuery = LiveQuery();

      // 1. Inscrever-se para atualizações de FilaUpdate
      await _inscreverFilaUpdate();

      // 2. Inscrever-se para atualizações diretas de Fila
      await _inscreverFila();

      // 3. Inscrever-se para atualizações de FilaSolicitacao
      await _inscreverSolicitacao();

      debugPrint('LiveQuery inicializado com sucesso para secretária');
    } catch (e) {
      debugPrint('Erro ao inicializar LiveQuery: $e');
      // Tentar novamente após um atraso
      _agendarReconexao();
    }
  }

  // Inscrever-se para atualizações de FilaUpdate
  Future<void> _inscreverFilaUpdate() async {
    try {
      final query = QueryBuilder<ParseObject>(ParseObject('FilaUpdate'));

      // Filtrar por consultório
      query.whereEqualTo('consultorioId', currentHospital!.objectId);

      // Inscrever-se para atualizações
      filaUpdateSubscription = await liveQuery!.client.subscribe(query);

      // Configurar handlers para eventos
      filaUpdateSubscription!.on(LiveQueryEvent.create, (value) {
        if (value is ParseObject) {
          debugPrint('LiveQuery: Nova atualização de fila recebida');
          _processarAtualizacaoFila(value);
        }
      });

      debugPrint('Inscrito para atualizações de FilaUpdate');
    } catch (e) {
      debugPrint('Erro ao inscrever-se para FilaUpdate: $e');
    }
  }

  // Inscrever-se para atualizações diretas de Fila
  Future<void> _inscreverFila() async {
    try {
      final query = QueryBuilder<ParseObject>(ParseObject('Fila'));

      // Filtrar por consultório
      query.whereEqualTo('consultorio', currentHospital!.toPointer());

      // Inscrever-se para atualizações
      filaSubscription = await liveQuery!.client.subscribe(query);

      // Configurar handlers para eventos
      filaSubscription!.on(LiveQueryEvent.create, (value) {
        if (value is ParseObject) {
          debugPrint('LiveQuery: Nova fila criada');
          carregarFilasPacientes();
        }
      });

      filaSubscription!.on(LiveQueryEvent.update, (value) {
        if (value is ParseObject) {
          debugPrint('LiveQuery: Fila atualizada');
          carregarFilasPacientes();
        }
      });

      filaSubscription!.on(LiveQueryEvent.delete, (value) {
        if (value is ParseObject) {
          debugPrint('LiveQuery: Fila removida');
          carregarFilasPacientes();
        }
      });

      debugPrint('Inscrito para atualizações diretas de Fila');
    } catch (e) {
      debugPrint('Erro ao inscrever-se para Fila: $e');
    }
  }

  // Inscrever-se para atualizações de FilaSolicitacao
  Future<void> _inscreverSolicitacao() async {
    try {
      final query = QueryBuilder<ParseObject>(ParseObject('FilaSolicitacao'));

      // Filtrar por consultório - usando objectId ao invés de toPointer()
      query.whereEqualTo('hospitalId', currentHospital!.objectId);

      // Inscrever-se para atualizações
      solicitacaoSubscription = await liveQuery!.client.subscribe(query);

      // Configurar handlers para eventos
      solicitacaoSubscription!.on(LiveQueryEvent.create, (value) {
        if (value is ParseObject) {
          debugPrint('LiveQuery: Nova solicitação criada');
          carregarSolicitacoesPendentes();
        }
      });

      solicitacaoSubscription!.on(LiveQueryEvent.update, (value) {
        if (value is ParseObject) {
          debugPrint('LiveQuery: Solicitação atualizada');
          carregarSolicitacoesPendentes();
        }
      });

      solicitacaoSubscription!.on(LiveQueryEvent.delete, (value) {
        if (value is ParseObject) {
          debugPrint('LiveQuery: Solicitação removida');
          carregarSolicitacoesPendentes();
        }
      });

      debugPrint('Inscrito para atualizações de FilaSolicitacao');
    } catch (e) {
      debugPrint('Erro ao inscrever-se para FilaSolicitacao: $e');
    }
  }

  // Processar atualização de fila
  void _processarAtualizacaoFila(ParseObject update) {
    try {
      final filaId = update.get<String>('filaId');
      final consultorioId = update.get<String>('consultorioId');
      final acao = update.get<String>('acao');

      debugPrint(
          'Atualização recebida: fila=$filaId, consultório=$consultorioId, ação=$acao');

      // Verificar se é relevante para o consultório atual
      if (currentHospital != null &&
          consultorioId == currentHospital!.objectId) {
        // Recarregar as listas
        carregarFilasPacientes();
        carregarSolicitacoesPendentes();
      }
    } catch (e) {
      debugPrint('Erro ao processar atualização de fila: $e');
    }
  }

  // Cancelar inscrições do LiveQuery
  void _cancelarLiveQuery() {
    try {
      debugPrint('Cancelando inscrições do LiveQuery');

      if (filaUpdateSubscription != null) {
        liveQuery?.client.unSubscribe(filaUpdateSubscription!);
        filaUpdateSubscription = null;
      }

      if (filaSubscription != null) {
        liveQuery?.client.unSubscribe(filaSubscription!);
        filaSubscription = null;
      }

      if (solicitacaoSubscription != null) {
        liveQuery?.client.unSubscribe(solicitacaoSubscription!);
        solicitacaoSubscription = null;
      }
    } catch (e) {
      debugPrint('Erro ao cancelar LiveQuery: $e');
    }
  }

  // Agendar reconexão
  void _agendarReconexao() {
    reconnectTimer?.cancel();
    reconnectTimer = Timer(const Duration(seconds: 5), () {
      _iniciarLiveQuery();
    });
  }

  Future<void> carregarDadosSecretaria() async {
    try {
      isLoading.value = true;
      error.value = '';

      final currentUser = await ParseUser.currentUser() as ParseUser?;
      if (currentUser == null) throw AuthException('Usuário não encontrado');

      final querySecretaria =
          QueryBuilder<ParseObject>(ParseObject('Secretaria'))
            ..whereEqualTo('user_secretaria', currentUser.toPointer())
            ..includeObject(['consultorio']);

      final secretariaResponse = await querySecretaria.query();

      if (!secretariaResponse.success ||
          secretariaResponse.results == null ||
          secretariaResponse.results!.isEmpty) {
        throw AppException('Perfil de secretária não encontrado');
      }

      currentSecretaria = secretariaResponse.results!.first;
      currentHospital = currentSecretaria!.get<ParseObject>('consultorio');

      if (currentHospital == null) {
        throw AppException('Hospital não encontrado');
      }

      await carregarSolicitacoesPendentes();
      await carregarFilasPacientes();

      // Inicializar LiveQuery para atualizações em tempo real
      await _iniciarLiveQuery();
    } on AuthException catch (e) {
      error.value = e.message;
    } on AppException catch (e) {
      error.value = e.message;
    } catch (e) {
      error.value = 'Erro desconhecido: $e';
      debugPrint('Erro ao carregar dados da secretária: $e');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> carregarSolicitacoesPendentes() async {
    if (currentHospital == null) return;

    try {
      final querySolicitacoes =
          QueryBuilder<ParseObject>(ParseObject('FilaSolicitacao'))
            ..whereEqualTo('hospitalId', currentHospital)
            ..whereEqualTo('status', 'pendente')
            ..orderByAscending('createdAt');

      final response = await querySolicitacoes.query();

      if (response.success && response.results != null) {
        solicitacoesPendentes.value = response.results!.cast<ParseObject>();
      } else {
        solicitacoesPendentes.clear();
      }
    } catch (e) {
      debugPrint('Erro ao carregar solicitações pendentes: $e');
    }
  }

  Future<void> carregarFilasPacientes() async {
    if (currentHospital == null) return;

    try {
      final queryFilas = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('consultorio', currentHospital)
        ..whereContainedIn('status', ['aguardando', 'em_atendimento'])
        ..orderByAscending('data_entrada')
        ..includeObject(['medico']);

      final response = await queryFilas.query();

      if (response.success && response.results != null) {
        pacientesNaFila.value = response.results!.cast<ParseObject>();
      } else {
        pacientesNaFila.clear();
      }
    } catch (e) {
      debugPrint('Erro ao carregar pacientes nas filas: $e');
    }
  }

  Future<bool> aprovarSolicitacao(ParseObject solicitacao,
      {required String nome, required String telefone}) async {
    try {
      isLoading.value = true;
      error.value = '';

      if (currentHospital == null) {
        throw AppException('Hospital não encontrado');
      }

      final medicoId = solicitacao.get<ParseObject>('medicoId')?.objectId;
      if (medicoId == null) {
        throw AppException('ID do médico não encontrado na solicitação');
      }

      // Utilizar a Cloud Function para adicionar o paciente à fila
      final params = <String, dynamic>{
        'solicitacaoId': solicitacao.objectId,
        'medicoId': medicoId,
        'consultorioId': currentHospital?.objectId,
        'secretariaId': currentSecretaria?.objectId,
        'nome': nome,
        'telefone': telefone,
      };

      debugPrint(
          'Chamando Cloud Function para adicionar paciente na fila: $params');

      // Registrar operação para debugging
      final log = ParseObject('LogDebug')
        ..set('tipo', 'approveRequest')
        ..set('dados', params.toString())
        ..set('data', DateTime.now());
      await log.save();

      final response = await ParseCloudFunction('adicionarPacienteNaFila')
          .execute(parameters: params);

      if (!response.success) {
        String errorMsg =
            response.error?.message ?? 'Erro ao adicionar paciente na fila';

        // Registrar erro para diagnóstico
        final errorLog = ParseObject('LogErro')
          ..set('tipo', 'approveRequestError')
          ..set('erro', errorMsg)
          ..set('dados', params.toString())
          ..set('data', DateTime.now());
        await errorLog.save();

        if (errorMsg.contains('Permission denied')) {
          errorMsg +=
              ' (Erro de permissão - verifique as configurações de acesso no Back4App)';
        }

        throw AppException(errorMsg);
      }

      // Recarregar listas após operação bem-sucedida
      await carregarSolicitacoesPendentes();
      await carregarFilasPacientes();

      successMessage.value = 'Paciente adicionado à fila com sucesso!';
      return true;
    } on AppException catch (e) {
      error.value = e.message;
    } catch (e) {
      error.value = 'Erro desconhecido: $e';
      debugPrint('Erro ao aprovar solicitação: $e');
    } finally {
      isLoading.value = false;
    }
    return false;
  }

  Future<bool> recusarSolicitacao(ParseObject solicitacao) async {
    try {
      isLoading.value = true;
      error.value = '';

      if (currentHospital == null) {
        throw AppException('Hospital não encontrado');
      }

      final medicoId = solicitacao.get<ParseObject>('medicoId')?.objectId;

      // Usar a Cloud Function para recusar a solicitação
      final params = <String, dynamic>{
        'solicitacaoId': solicitacao.objectId,
        'consultorioId': currentHospital?.objectId,
      };

      // Adicionar medicoId se disponível
      if (medicoId != null) {
        params['medicoId'] = medicoId;
      }

      debugPrint('Chamando Cloud Function para recusar solicitação: $params');

      // Registrar operação para debugging
      final log = ParseObject('LogDebug')
        ..set('tipo', 'rejectRequest')
        ..set('dados', params.toString())
        ..set('data', DateTime.now());
      await log.save();

      final response = await ParseCloudFunction('recusarSolicitacao')
          .execute(parameters: params);

      if (!response.success) {
        String errorMsg =
            response.error?.message ?? 'Erro ao recusar solicitação';

        // Registrar erro para diagnóstico
        final errorLog = ParseObject('LogErro')
          ..set('tipo', 'rejectRequestError')
          ..set('erro', errorMsg)
          ..set('dados', params.toString())
          ..set('data', DateTime.now());
        await errorLog.save();

        if (errorMsg.contains('Permission denied')) {
          errorMsg +=
              ' (Erro de permissão - verifique as configurações de acesso no Back4App)';
        }

        throw AppException(errorMsg);
      }

      await carregarSolicitacoesPendentes();
      successMessage.value = 'Solicitação recusada com sucesso';
      return true;
    } on AppException catch (e) {
      error.value = e.message;
    } catch (e) {
      error.value = 'Erro desconhecido: $e';
      debugPrint('Erro ao recusar solicitação: $e');
    } finally {
      isLoading.value = false;
    }
    return false;
  }

  Future<bool> iniciarAtendimento(ParseObject paciente) async {
    try {
      isLoading.value = true;
      error.value = '';

      paciente
        ..set('status', 'em_atendimento')
        ..set('data_inicio_atendimento',
            BrazilTimeZone.createForParse(BrazilTimeZone.now()));

      final response = await paciente.save();

      if (response.success) {
        await carregarFilasPacientes();
        successMessage.value = 'Atendimento iniciado com sucesso';
        return true;
      } else {
        throw AppException('Falha ao iniciar atendimento');
      }
    } on AppException catch (e) {
      error.value = e.message;
    } catch (e) {
      error.value = 'Erro desconhecido: $e';
      debugPrint('Erro ao iniciar atendimento: $e');
    } finally {
      isLoading.value = false;
    }
    return false;
  }

  Future<bool> finalizarAtendimento(ParseObject paciente) async {
    try {
      isLoading.value = true;
      error.value = '';

      // ✅ ATUALIZAR STATUS DA FILA
      paciente
        ..set('status', 'atendido')
        ..set('data_fim_atendimento',
            BrazilTimeZone.createForParse(BrazilTimeZone.now()));

      final response = await paciente.save();

      if (response.success) {
        // ✅ ATUALIZAR CAMPO em_fila DO PACIENTE
        final idPaciente = paciente.get<String>('idPaciente');
        final deviceId = paciente.get<String>('deviceId');
        final telefone = paciente.get<String>('telefone');

        if (idPaciente != null || deviceId != null || telefone != null) {
          await _atualizarStatusPacienteAposAtendimento(
              idPaciente, deviceId, telefone);
        }

        await carregarFilasPacientes();
        successMessage.value = 'Atendimento finalizado com sucesso';
        return true;
      } else {
        throw AppException('Falha ao finalizar atendimento');
      }
    } on AppException catch (e) {
      error.value = e.message;
    } catch (e) {
      error.value = 'Erro desconhecido: $e';
      debugPrint('Erro ao finalizar atendimento: $e');
    } finally {
      isLoading.value = false;
    }
    return false;
  }

  /// ✅ NOVO MÉTODO: Atualizar status do paciente após finalizar atendimento
  Future<void> _atualizarStatusPacienteAposAtendimento(
      String? idPaciente, String? deviceId, String? telefone) async {
    try {
      debugPrint('🏥 Atualizando status em_fila após finalizar atendimento...');

      // 1. Tentar por deviceId (mais confiável)
      if (deviceId != null && deviceId.isNotEmpty) {
        final pacienteQuery = QueryBuilder<ParseObject>(ParseObject('Paciente'))
          ..whereEqualTo('deviceId', deviceId);

        final pacienteResponse = await pacienteQuery.query();

        if (pacienteResponse.success &&
            pacienteResponse.results?.isNotEmpty == true) {
          final pacienteObj = pacienteResponse.results!.first;
          pacienteObj.set('em_fila', false);
          pacienteObj.set('ultima_fila', DateTime.now());
          pacienteObj.set('status_fila', 'atendido');

          await pacienteObj.save();
          debugPrint('✅ Paciente atualizado por deviceId: $deviceId');
          return;
        }
      }

      // 2. Tentar por idPaciente
      if (idPaciente != null && idPaciente.isNotEmpty) {
        final pacienteQuery = QueryBuilder<ParseObject>(ParseObject('Paciente'))
          ..whereEqualTo('userId', idPaciente);

        final pacienteResponse = await pacienteQuery.query();

        if (pacienteResponse.success &&
            pacienteResponse.results?.isNotEmpty == true) {
          final pacienteObj = pacienteResponse.results!.first;
          pacienteObj.set('em_fila', false);
          pacienteObj.set('ultima_fila', DateTime.now());
          pacienteObj.set('status_fila', 'atendido');

          await pacienteObj.save();
          debugPrint('✅ Paciente atualizado por userId: $idPaciente');
          return;
        }
      }

      // 3. Tentar por telefone (última tentativa)
      if (telefone != null && telefone.isNotEmpty) {
        final cleanPhone = telefone.replaceAll(RegExp(r'[^0-9]'), '');

        final pacienteQuery = QueryBuilder<ParseObject>(ParseObject('Paciente'))
          ..whereEqualTo('telefone', cleanPhone);

        final pacienteResponse = await pacienteQuery.query();

        if (pacienteResponse.success &&
            pacienteResponse.results?.isNotEmpty == true) {
          final pacienteObj = pacienteResponse.results!.first;
          pacienteObj.set('em_fila', false);
          pacienteObj.set('ultima_fila', DateTime.now());
          pacienteObj.set('status_fila', 'atendido');

          await pacienteObj.save();
          debugPrint('✅ Paciente atualizado por telefone: $cleanPhone');
          return;
        }
      }

      debugPrint(
          '⚠️ Não foi possível encontrar o paciente para atualizar status');
    } catch (e) {
      debugPrint('❌ Erro ao atualizar status do paciente: $e');
    }
  }

  Future<bool> removerPaciente(ParseObject paciente) async {
    try {
      isLoading.value = true;
      error.value = '';

      // Registrar a saída do paciente
      final filaSaida = ParseObject('FilaSaida')
        ..set('fila_id', paciente)
        ..set('motivo_saida', 'removido_secretaria')
        ..set('created_at', DateTime.now());

      // Configurar ACL pública para FilaSaida para evitar erros de permissão
      final aclSaida = ParseACL();
      aclSaida.setPublicReadAccess(allowed: true);
      aclSaida.setPublicWriteAccess(allowed: true);
      filaSaida.setACL(aclSaida);

      await filaSaida.save();

      // Atualizar o status do paciente na fila
      paciente.set('status', 'removido');
      paciente.set('data_saida', DateTime.now());

      // Configurar ACL pública para o objeto Fila para evitar erros de permissão
      final aclFila = ParseACL();
      aclFila.setPublicReadAccess(allowed: true);
      aclFila.setPublicWriteAccess(allowed: true);
      paciente.setACL(aclFila);

      final response = await paciente.save();

      if (response.success) {
        final posicaoRemovida = paciente.get<int>('posicao') ?? 0;
        final medicoId = paciente.get<ParseObject>('medico')?.objectId;
        final idPaciente = paciente.get<String>('idPaciente');

        // Atualizar status do paciente para não estar mais na fila
        if (idPaciente != null && idPaciente.isNotEmpty) {
          debugPrint(
              'Atualizando status em_fila para usuário/paciente com deviceId: $idPaciente');

          try {
            // 1. Atualizar na classe Paciente (implementação principal)
            final usuarioQuery =
                QueryBuilder<ParseObject>(ParseObject('Paciente'))
                  ..whereEqualTo('deviceId', idPaciente);

            final usuarioResponse = await usuarioQuery.query();

            if (usuarioResponse.success &&
                usuarioResponse.results != null &&
                usuarioResponse.results!.isNotEmpty) {
              final usuario = usuarioResponse.results!.first;
              usuario.set('em_fila', false);
              usuario.set('ultima_fila', DateTime.now());

              // Configurar ACL pública para o usuário
              final aclUsuario = ParseACL();
              aclUsuario.setPublicReadAccess(allowed: true);
              aclUsuario.setPublicWriteAccess(allowed: true);
              usuario.setACL(aclUsuario);

              await usuario.save();
              debugPrint('Status do paciente atualizado para em_fila=false');
            } else {
              debugPrint(
                  'Paciente não encontrado com deviceId: $idPaciente. Tentando com userId...');

              // 2. Retrocompatibilidade: buscar por userId
              final pacienteQuery =
                  QueryBuilder<ParseObject>(ParseObject('Paciente'))
                    ..whereEqualTo('userId', idPaciente);

              final pacienteResponse = await pacienteQuery.query();

              if (pacienteResponse.success &&
                  pacienteResponse.results != null &&
                  pacienteResponse.results!.isNotEmpty) {
                final pacienteObj = pacienteResponse.results!.first;
                pacienteObj.set('em_fila', false);
                pacienteObj.set('ultima_fila', DateTime.now());

                // Configurar ACL pública para o paciente
                final aclPaciente = ParseACL();
                aclPaciente.setPublicReadAccess(allowed: true);
                aclPaciente.setPublicWriteAccess(allowed: true);
                pacienteObj.setACL(aclPaciente);

                await pacienteObj.save();
                debugPrint('Status do paciente atualizado para em_fila=false');
              } else {
                debugPrint(
                    'Nenhum registro encontrado para o deviceId: $idPaciente');
              }
            }
          } catch (e) {
            debugPrint('Erro ao atualizar status do paciente: $e');
            // Não interrompe o fluxo principal
          }
        } else {
          debugPrint(
              'Alerta: ID do paciente não encontrado, não foi possível atualizar status em_fila');
        }

        // Reajustar posições na fila
        if (medicoId != null && posicaoRemovida > 0) {
          await _reajustarPosicoes(medicoId, posicaoRemovida);
        }

        await carregarFilasPacientes();
        successMessage.value = 'Paciente removido com sucesso';
        return true;
      } else {
        throw AppException('Falha ao remover paciente');
      }
    } on AppException catch (e) {
      error.value = e.message;
    } catch (e) {
      error.value = 'Erro desconhecido: $e';
      debugPrint('Erro ao remover paciente: $e');
    } finally {
      isLoading.value = false;
    }
    return false;
  }

  Future<void> _reajustarPosicoes(String medicoId, int posicaoRemovida) async {
    try {
      final queryPacientes = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('medico', ParseObject('Medico')..objectId = medicoId)
        ..whereEqualTo('consultorio', currentHospital)
        ..whereEqualTo('status', 'aguardando')
        ..whereGreaterThan('posicao', posicaoRemovida)
        ..orderByAscending('posicao');

      final response = await queryPacientes.query();

      if (response.success && response.results != null) {
        for (var paciente in response.results!) {
          final posicaoAtual = paciente.get<int>('posicao') ?? 0;
          paciente.set('posicao', posicaoAtual - 1);
          await paciente.save();
        }
      }
    } catch (e) {
      debugPrint('Erro ao reajustar posições: $e');
    }
  }

  Future<bool> reordenarFila(
      String medicoId, List<ParseObject> novaOrdem) async {
    try {
      isLoading.value = true;
      error.value = '';

      for (int i = 0; i < novaOrdem.length; i++) {
        final paciente = novaOrdem[i];
        paciente.set('posicao', i + 1);
        await paciente.save();
      }

      await carregarFilasPacientes();
      successMessage.value = 'Fila reordenada com sucesso';
      return true;
    } on AppException catch (e) {
      error.value = e.message;
    } catch (e) {
      error.value = 'Erro desconhecido: $e';
      debugPrint('Erro ao reordenar fila: $e');
    } finally {
      isLoading.value = false;
    }
    return false;
  }

  void abrirWhatsApp(String telefone) async {
    try {
      final sucesso = await WhatsAppUtils.abrirWhatsApp(telefone);
      if (!sucesso) {
        error.value = 'Não foi possível abrir o WhatsApp';
      }
    } catch (e) {
      error.value = 'Erro ao abrir WhatsApp: $e';
      debugPrint('Erro ao abrir WhatsApp: $e');
    }
  }

  String formatarDataHora(DateTime? dataHora) {
    if (dataHora == null) return 'Data não disponível';
    // ✅ USAR SISTEMA DE TIMEZONE DO BRASIL
    return BrazilDateFormat.formatDateTime(dataHora);
  }

  List<String> obterMedicosComFila() {
    final medicosIds = <String>{};

    for (var paciente in pacientesNaFila) {
      final medicoObj = paciente.get<ParseObject>('medico');
      if (medicoObj != null && medicoObj.objectId != null) {
        medicosIds.add(medicoObj.objectId!);
      }
    }

    return medicosIds.toList();
  }

  List<ParseObject> obterPacientesPorMedico(String medicoId) {
    return pacientesNaFila.where((paciente) {
      final medico = paciente.get<ParseObject>('medico');
      return medico != null && medico.objectId == medicoId;
    }).toList();
  }

  Map<String, dynamic> obterDetalhesMedico(String medicoId) {
    for (var paciente in pacientesNaFila) {
      final medico = paciente.get<ParseObject>('medico');
      if (medico != null && medico.objectId == medicoId) {
        return {
          'id': medicoId,
          'nome': medico.get<String>('nome') ?? 'Médico',
          'especialidade': medico.get<String>('especialidade') ??
              'Especialidade não informada',
        };
      }
    }
    return {
      'id': medicoId,
      'nome': 'Médico não encontrado',
      'especialidade': '',
    };
  }

  Future<bool> aceitarSolicitacao(ParseObject solicitacao) async {
    try {
      isLoading.value = true;
      error.value = '';

      final userPaciente = solicitacao.get<ParseUser>('pacienteUser');
      String nome = '';
      String telefone = '';

      if (userPaciente != null) {
        final queryPaciente = QueryBuilder<ParseObject>(ParseObject('Paciente'))
          ..whereEqualTo('user', userPaciente.toPointer());

        final responsePaciente = await queryPaciente.query();

        if (responsePaciente.success &&
            responsePaciente.results != null &&
            responsePaciente.results!.isNotEmpty) {
          final paciente = responsePaciente.results!.first as ParseObject;
          nome = paciente.get<String>('nome') ?? '';
          telefone = paciente.get<String>('telefone') ?? '';
        }
      }

      if (nome.isEmpty) {
        nome = solicitacao.get<String>('nomePaciente') ?? 'Sem nome';
      }

      if (telefone.isEmpty) {
        telefone = solicitacao.get<String>('telefonePaciente') ?? '';
      }

      solicitacao.set('status', 'aprovado');
      await solicitacao.save();

      final medicoId = solicitacao.get<String>('medicoId');
      if (medicoId == null) {
        throw AppException('ID do médico não encontrado na solicitação');
      }

      final queryUltimaPosicao = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('medico', ParseObject('Medico')..objectId = medicoId)
        ..whereEqualTo('consultorio', currentHospital)
        ..whereContainedIn('status', ['aguardando', 'em_atendimento'])
        ..orderByDescending('posicao');

      final posicaoResponse = await queryUltimaPosicao.query();
      final proximaPosicao = (posicaoResponse.results?.isEmpty ?? true)
          ? 1
          : (posicaoResponse.results!.first.get<int>('posicao') ?? 0) + 1;

      final novaFila = ParseObject('Fila')
        ..set('medico', ParseObject('Medico')..objectId = medicoId)
        ..set('consultorio', currentHospital)
        ..set('nome_paciente', nome)
        ..set('telefone_paciente', telefone)
        ..set(
            'data_entrada', BrazilTimeZone.createForParse(BrazilTimeZone.now()))
        ..set('posicao', proximaPosicao)
        ..set('status', 'aguardando');

      if (userPaciente != null) {
        novaFila.set('paciente', userPaciente.toPointer());
      }

      final result = await novaFila.save();

      if (!result.success) {
        solicitacao.set('status', 'pendente');
        await solicitacao.save();
        throw AppException(
            result.error?.message ?? 'Erro ao criar entrada na fila');
      }

      solicitacoesPendentes.remove(solicitacao);
      await carregarFilasPacientes();

      return true;
    } on AppException catch (e) {
      error.value = e.message;
    } catch (e) {
      error.value = 'Erro desconhecido: $e';
      debugPrint('Erro ao aceitar solicitação: $e');
    } finally {
      isLoading.value = false;
    }
    return false;
  }

  // Utility method to debug the relationship between hospital and doctors
  Future<Map<String, dynamic>>
      diagnosticarRelacionamentosMedicoHospital() async {
    try {
      if (currentHospital == null) {
        return {'success': false, 'error': 'Hospital não carregado'};
      }

      final hospitalId = currentHospital!.objectId!;

      // Log the raw medicos_vinculados array
      final medicosArray = currentHospital!.get<List?>('medicos_vinculados');
      final medicosInfo = [];

      if (medicosArray != null) {
        for (var medico in medicosArray) {
          if (medico is Map<String, dynamic>) {
            medicosInfo.add({'tipo': 'Map', 'conteudo': medico});
          } else if (medico is ParseObject) {
            medicosInfo.add({
              'tipo': 'ParseObject',
              'objectId': medico.objectId,
              'className': medico.parseClassName
            });
          } else {
            medicosInfo.add({
              'tipo': medico.runtimeType.toString(),
              'conteudo': medico.toString()
            });
          }
        }
      }

      // Call cloud function to check on server side
      final params = <String, dynamic>{'hospitalId': hospitalId};

      final result =
          await ParseCloudFunction('debugHospitalMedicoRelationships')
              .execute(parameters: params);

      return {
        'success': true,
        'medicosArray': medicosInfo,
        'arrayTamanho': medicosInfo.length,
        'cloudResult': result.result,
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }
}
