import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:fila_app/utils/api_cache_manager.dart';
import 'package:fila_app/utils/batch_operation_manager.dart';
import '../utils/date_utils.dart';

class FilaMedicoController extends GetxController {
  final RxList<ParseObject> pacientesNaFila = <ParseObject>[].obs;
  final RxBool isLoading = false.obs;
  final RxString error = ''.obs;
  final RxString successMessage = ''.obs;
  final Rx<MetricasAtendimento> metricas = MetricasAtendimento().obs;

  ParseObject? currentMedico;
  Timer? refreshTimer;
  String? _consultorioId;

  // Managers para otimização de requisições
  final ApiCacheManager _cacheManager = ApiCacheManager();
  final BatchOperationManager _batchManager = BatchOperationManager();

  // Controle de atualização
  DateTime _lastUpdate = DateTime.now().subtract(const Duration(minutes: 5));
  final int _minUpdateIntervalMs = 5000; // 5 segundos entre atualizações

  @override
  void onInit() {
    super.onInit();
    _inicializarCacheManager();
    _carregarDadosMedico();
    _iniciarAtualizacaoAutomatica();
  }

  @override
  void onClose() {
    refreshTimer?.cancel();
    super.onClose();
  }

  Future<void> _inicializarCacheManager() async {
    await _cacheManager.initialize();
  }

  void _iniciarAtualizacaoAutomatica() {
    refreshTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      if (!isLoading.value) {
        carregarFilaPacientes(forceRefresh: false);
      }
    });
  }

  Future<void> _carregarDadosMedico() async {
    try {
      isLoading.value = true;
      error.value = '';

      final currentUser = await ParseUser.currentUser() as ParseUser?;
      if (currentUser == null) {
        error.value = 'Usuário não encontrado';
        return;
      }

      // Obter dados do médico com cache
      final cacheKey = 'medico_${currentUser.objectId}';
      final medicoData = await _cacheManager.executeWithCache(
        endpoint: cacheKey,
        fetchFunction: () async {
          final queryMedico = QueryBuilder<ParseObject>(ParseObject('Medico'))
            ..whereEqualTo('user_medico', currentUser.toPointer());
          final medicoResponse = await queryMedico.query();

          if (!medicoResponse.success ||
              medicoResponse.results == null ||
              medicoResponse.results!.isEmpty) {
            throw Exception('Perfil de médico não encontrado');
          }

          return medicoResponse.results!.first;
        },
        maxAge: const Duration(minutes: 30), // Cache de médico pode durar mais
      );

      currentMedico = medicoData;

      // Obter consultório vinculado
      await _obterConsultorio();

      // Carregar dados iniciais
      await carregarFilaPacientes(forceRefresh: true);
      await obterMetricas();
    } catch (e) {
      error.value = 'Erro ao carregar dados do médico: $e';
      debugPrint('Erro ao carregar dados do médico: $e');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> _obterConsultorio() async {
    if (currentMedico == null) return;

    try {
      // Usar relação de MedicoConsultorio para encontrar consultório vinculado
      // Com cache para otimizar
      final medicoId = currentMedico!.objectId;
      final cacheKey = 'consultorio_medico_$medicoId';

      final consultorioData = await _cacheManager.executeWithCache(
        endpoint: cacheKey,
        fetchFunction: () async {
          final query =
              QueryBuilder<ParseObject>(ParseObject('MedicoConsultorio'))
                ..whereEqualTo('medicoConsultorio_medico',
                    ParseObject('Medico')..objectId = medicoId)
                ..includeObject(['medicoConsultorio_consultorio']);

          final response = await query.query();

          if (!response.success ||
              response.results == null ||
              response.results!.isEmpty) {
            throw Exception('Consultório não encontrado para este médico');
          }

          final consultorioPointer =
              response.results!.first.get('medicoConsultorio_consultorio');
          if (consultorioPointer == null) {
            throw Exception('Relação de consultório inválida');
          }

          return consultorioPointer;
        },
        maxAge: const Duration(hours: 1), // Relação raramente muda
      );

      _consultorioId = consultorioData.objectId;
    } catch (e) {
      debugPrint('Erro ao obter consultório: $e');
      // Tentar método alternativo direto
      try {
        final queryDireto =
            QueryBuilder<ParseObject>(ParseObject('consultorio'))
              ..whereEqualTo('medicos_vinculados', currentMedico!.objectId);
        final response = await queryDireto.query();
        if (response.success &&
            response.results != null &&
            response.results!.isNotEmpty) {
          _consultorioId = response.results!.first.objectId;
        }
      } catch (e) {
        debugPrint('Erro ao buscar consultório alternativo: $e');
      }
    }
  }

  Future<void> carregarFilaPacientes({bool forceRefresh = false}) async {
    if (currentMedico == null) return;

    // Verificar intervalo mínimo entre atualizações
    final agora = DateTime.now();
    if (!forceRefresh &&
        agora.difference(_lastUpdate).inMilliseconds < _minUpdateIntervalMs) {
      debugPrint('Ignorando atualização - muito cedo');
      return;
    }

    try {
      isLoading.value = true;
      error.value = '';

      final medicoId = currentMedico!.objectId!;

      if (_consultorioId != null) {
        // Usar novo endpoint otimizado que retorna todos os dados necessários
        try {
          final dashboardData = await _batchManager.getDashboardData(
            medicoId: medicoId,
            consultorioId: _consultorioId!,
            maxAge: const Duration(seconds: 30),
            forceRefresh: forceRefresh,
          );

          // Processar dados da fila
          if (dashboardData.containsKey('fila')) {
            final filaData = dashboardData['fila'] as List;
            // Converter para ParseObject
            final filaObjects = filaData.map((item) {
              final parseObj = ParseObject('Fila');
              final data = item as Map<String, dynamic>;
              data.forEach((key, value) {
                parseObj.set(key, value);
              });
              return parseObj;
            }).toList();

            pacientesNaFila.value = filaObjects;
          }

          // Se tiver métricas, processá-las
          if (dashboardData.containsKey('metricas') &&
              dashboardData['metricas'] != null) {
            _processarMetricas(dashboardData['metricas']);
          }

          _lastUpdate = agora;
          return;
        } catch (batchError) {
          debugPrint('Erro ao usar endpoint otimizado: $batchError');
          // Continuar com método tradicional como fallback
        }
      }

      // Método tradicional (fallback)
      final queryFila = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('medico', ParseObject('Medico')..objectId = medicoId)
        ..whereContainedIn('status', ['aguardando', 'em_atendimento'])
        ..orderByAscending('posicao');

      final response = await queryFila.query();

      if (response.success && response.results != null) {
        pacientesNaFila.value = response.results!.cast<ParseObject>();
        _lastUpdate = agora;
      } else {
        pacientesNaFila.clear();
        error.value = 'Não foi possível carregar a fila';
      }
    } catch (e) {
      error.value = 'Erro ao carregar fila: $e';
      debugPrint('Erro ao carregar fila de pacientes: $e');
    } finally {
      isLoading.value = false;
    }
  }

  void _processarMetricas(Map<String, dynamic> metricasData) {
    final novasMetricas = MetricasAtendimento();

    novasMetricas.totalAtendimentos = metricasData['total_pacientes'] ?? 0;
    novasMetricas.pacientesAtendidos = metricasData['pacientes_atendidos'] ?? 0;
    novasMetricas.tempoMedioAtendimento =
        metricasData['tempo_medio_atendimento'] ?? 0;
    novasMetricas.tempoMedioEspera = metricasData['tempo_medio_espera'] ?? 0;

    metricas.value = novasMetricas;
  }

  Future<bool> iniciarAtendimento(ParseObject paciente) async {
    try {
      isLoading.value = true;
      error.value = '';

      paciente.set('status', 'em_atendimento');
      paciente.set('data_inicio_atendimento',
          BrazilTimeZone.createForParse(BrazilTimeZone.now()));

      final response = await paciente.save();
      if (response.success) {
        // Invalidar caches relacionados
        if (_consultorioId != null) {
          await _cacheManager.invalidateCache(
              'dashboard:${currentMedico!.objectId}:$_consultorioId', null);
        }

        await carregarFilaPacientes(forceRefresh: true);
        successMessage.value = 'Atendimento iniciado com sucesso';
        return true;
      } else {
        error.value = 'Falha ao iniciar atendimento';
        return false;
      }
    } catch (e) {
      error.value = 'Erro ao iniciar atendimento: $e';
      debugPrint('Erro ao iniciar atendimento: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<bool> finalizarAtendimento(ParseObject paciente) async {
    try {
      isLoading.value = true;
      error.value = '';

      // Registrar tempo de atendimento para métricas
      final dataEntrada = paciente.get<DateTime>('data_entrada');
      if (dataEntrada != null) {
        final dataEntradaBrasil =
            BrazilTimeZone.parseObjectDateToBrazil(dataEntrada);
        if (dataEntradaBrasil != null) {
          final duracao =
              BrazilTimeZone.now().difference(dataEntradaBrasil).inMinutes;
          await _atualizarMetricasAtendimento(duracao);
        }
      }

      // Remover paciente da fila
      paciente.set('status', 'atendido');
      paciente.set('data_fim_atendimento',
          BrazilTimeZone.createForParse(BrazilTimeZone.now()));

      final response = await paciente.save();
      if (response.success) {
        // Invalidar caches relacionados
        if (_consultorioId != null) {
          await _cacheManager.invalidateCache(
              'dashboard:${currentMedico!.objectId}:$_consultorioId', null);
        }

        await carregarFilaPacientes(forceRefresh: true);
        successMessage.value = 'Atendimento finalizado com sucesso';
        return true;
      } else {
        error.value = 'Falha ao finalizar atendimento';
        return false;
      }
    } catch (e) {
      error.value = 'Erro ao finalizar atendimento: $e';
      debugPrint('Erro ao finalizar atendimento: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> _atualizarMetricasAtendimento(int duracao) async {
    if (currentMedico == null) return;

    try {
      final medicoId = currentMedico!.objectId;
      if (medicoId == null) return;

      final query = QueryBuilder<ParseObject>(
          ParseObject('MetricasAtendimento'))
        ..whereEqualTo('medico', ParseObject('Medico')..objectId = medicoId);

      final result = await query.query();

      ParseObject metricasObj;
      if (result.success &&
          result.results != null &&
          result.results!.isNotEmpty) {
        metricasObj = result.results!.first as ParseObject;
        final totalAtendimentos =
            metricasObj.get<int>('total_atendimentos') ?? 0;
        final tempoTotalAtendimento =
            metricasObj.get<int>('tempo_total_atendimento') ?? 0;

        metricasObj.set('total_atendimentos', totalAtendimentos + 1);
        metricasObj.set(
            'tempo_total_atendimento', tempoTotalAtendimento + duracao);
      } else {
        metricasObj = ParseObject('MetricasAtendimento')
          ..set('medico', ParseObject('Medico')..objectId = medicoId)
          ..set('total_atendimentos', 1)
          ..set('tempo_total_atendimento', duracao);
      }

      await metricasObj.save();

      // Invalidar cache de métricas
      if (_consultorioId != null) {
        await _cacheManager.invalidateCache(
            'dashboard:$medicoId:$_consultorioId', null);
      }
    } catch (e) {
      debugPrint('Erro ao atualizar métricas: $e');
    }
  }

  Future<void> obterMetricas() async {
    if (currentMedico == null) return;

    try {
      final medicoId = currentMedico!.objectId;
      if (medicoId == null) return;

      // Se temos um consultorioId, as métricas já foram carregadas no dashboard
      if (_consultorioId != null) {
        // As métricas já foram carregadas no carregarFilaPacientes
        return;
      }

      // Método tradicional como fallback
      final hoje = DateTime.now();
      hoje.subtract(const Duration(days: 1)); // Pegar métricas do último dia

      final cacheKey = 'metricas_$medicoId';

      final metricasData = await _cacheManager.executeWithCache(
        endpoint: cacheKey,
        params: {'date': hoje.toIso8601String()},
        fetchFunction: () async {
          final query =
              QueryBuilder<ParseObject>(ParseObject('MetricasAtendimento'))
                ..whereEqualTo(
                    'medico_id', ParseObject('Medico')..objectId = medicoId)
                ..orderByDescending('updatedAt')
                ..setLimit(1);

          final response = await query.query();

          if (response.success &&
              response.results != null &&
              response.results!.isNotEmpty) {
            return response.results!.first;
          } else {
            return ParseObject('MetricasAtendimento')
              ..set('total_atendimentos', 0)
              ..set('tempo_total_atendimento', 0);
          }
        },
        maxAge: const Duration(minutes: 15),
      );

      final novasMetricas = MetricasAtendimento();

      novasMetricas.totalAtendimentos =
          metricasData.get<int>('total_atendimentos') ?? 0;
      novasMetricas.tempoTotalAtendimento =
          metricasData.get<int>('tempo_total_atendimento') ?? 0;

      if (novasMetricas.totalAtendimentos > 0) {
        novasMetricas.tempoMedioAtendimento =
            (novasMetricas.tempoTotalAtendimento /
                    novasMetricas.totalAtendimentos)
                .round();
      }

      metricas.value = novasMetricas;
    } catch (e) {
      debugPrint('Erro ao obter métricas: $e');
    }
  }

  Future<bool> enviarMensagemIndividual(
      ParseObject paciente, String mensagem) async {
    try {
      isLoading.value = true;
      error.value = '';

      final pacienteId = paciente.get<String>('idPaciente');
      if (pacienteId == null) {
        error.value = 'ID do paciente não encontrado';
        return false;
      }

      final medicoNome = currentMedico?.get<String>('nome') ?? 'Médico';

      final mensagemObj = ParseObject('MensagemFila')
        ..set('fila_id', paciente)
        ..set('mensagem', mensagem)
        ..set('remetente', 'medico')
        ..set('nome_remetente', medicoNome)
        ..set('id_paciente', pacienteId)
        ..set('data_envio', DateTime.now());

      final response = await mensagemObj.save();
      if (response.success) {
        // Invalidar cache de mensagens
        if (_consultorioId != null) {
          await _cacheManager.invalidateCache(
              'mensagens:$_consultorioId', null);
        }

        successMessage.value = 'Mensagem enviada com sucesso';
        return true;
      } else {
        error.value = 'Falha ao enviar mensagem';
        return false;
      }
    } catch (e) {
      error.value = 'Erro ao enviar mensagem: $e';
      debugPrint('Erro ao enviar mensagem individual: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<bool> enviarMensagemTodos(String mensagem) async {
    try {
      isLoading.value = true;
      error.value = '';

      if (currentMedico == null || _consultorioId == null) {
        error.value = 'Dados do médico não carregados';
        return false;
      }

      final medicoNome = currentMedico?.get<String>('nome') ?? 'Médico';
      final medicoId = currentMedico!.objectId!;

      // Criar mensagem para broadcast
      final mensagemObj = ParseObject('MensagemFila')
        ..set('medico', ParseObject('Medico')..objectId = medicoId)
        ..set('consultorio',
            ParseObject('consultorio')..objectId = _consultorioId)
        ..set('mensagem', mensagem)
        ..set('remetente', 'medico')
        ..set('nome_remetente', medicoNome)
        ..set('broadcast', true)
        ..set('data_envio', DateTime.now());

      final response = await mensagemObj.save();
      if (response.success) {
        // Invalidar cache de mensagens
        await _cacheManager.invalidateCache('mensagens:$_consultorioId', null);

        successMessage.value = 'Mensagem enviada para todos os pacientes';
        return true;
      } else {
        error.value = 'Falha ao enviar mensagem para todos';
        return false;
      }
    } catch (e) {
      error.value = 'Erro ao enviar mensagem: $e';
      debugPrint('Erro ao enviar mensagem para todos: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  double calcularEficienciaAtendimento() {
    // A eficiência é calculada com base em diversos fatores
    // Por exemplo: relação entre tempo de atendimento e tempo de espera,
    // número de pacientes atendidos vs. capacidade ideal, etc.

    // Implementação simplificada:
    if (metricas.value.tempoMedioAtendimento <= 0) return 0.0;

    // Consideramos eficiente se o tempo médio de atendimento for menor que 15 minutos
    // e o tempo de espera for menor que 30 minutos
    double eficienciaAtendimento = 15 / metricas.value.tempoMedioAtendimento;
    double eficienciaEspera = metricas.value.tempoMedioEspera > 0
        ? 30 / metricas.value.tempoMedioEspera
        : 1.0;

    // Limitar entre 0 e 1
    eficienciaAtendimento =
        eficienciaAtendimento > 1 ? 1 : eficienciaAtendimento;
    eficienciaEspera = eficienciaEspera > 1 ? 1 : eficienciaEspera;

    // Média ponderada das eficiências (70% atendimento, 30% espera)
    return (eficienciaAtendimento * 0.7 + eficienciaEspera * 0.3);
  }
}

class MetricasAtendimento {
  int totalAtendimentos = 0;
  int pacientesAtendidos = 0;
  int tempoTotalAtendimento = 0;
  int tempoMedioAtendimento = 0;
  int tempoMedioEspera = 0;
}
