import 'dart:async';
import 'package:flutter/material.dart';

/// Utilitário para implementar debouncing
/// Evita execuções excessivas de funções em campos de busca
class Debouncer {
  final int milliseconds;
  Timer? _timer;

  Debouncer({required this.milliseconds});

  /// Executa a função após o delay especificado
  /// Cancela execuções anteriores se chamado novamente
  void run(VoidCallback action) {
    _timer?.cancel();
    _timer = Timer(Duration(milliseconds: milliseconds), action);
  }

  /// Cancela qualquer execução pendente
  void cancel() {
    _timer?.cancel();
  }

  /// Verifica se há uma execução pendente
  bool get isActive => _timer?.isActive ?? false;
}

/// Extensão para usar debouncing em TextEditingController
extension DebouncedTextController on TextEditingController {
  void addDebouncedListener({
    required VoidCallback onChanged,
    int delay = 500,
  }) {
    final debouncer = Debouncer(milliseconds: delay);
    addListener(() {
      debouncer.run(onChanged);
    });
  }
}
