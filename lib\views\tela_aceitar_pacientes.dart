import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fila_app/controllers/secretaria_controller.dart';
import 'package:fila_app/views/gradient_background.dart';
import 'package:fila_app/widgets/app_header.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'dart:convert';
import 'dart:typed_data';
import 'dart:math' as math;
import 'dart:async';
import 'package:intl/intl.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/pdf.dart';
import 'package:printing/printing.dart';
import 'package:fila_app/models/fila.dart';
import '../utils/date_utils.dart';

class TelaAceitarPacientes extends StatefulWidget {
  const TelaAceitarPacientes({super.key});

  @override
  State<TelaAceitarPacientes> createState() => _TelaAceitarPacientesState();
}

class _TelaAceitarPacientesState extends State<TelaAceitarPacientes>
    with SingleTickerProviderStateMixin {
  final SecretariaController controller = Get.find<SecretariaController>();
  final TextEditingController searchController = TextEditingController();
  final RxList<Map<String, dynamic>> pacientesRegistrados =
      <Map<String, dynamic>>[].obs;
  final RxList<ParseObject> solicitacoesPendentes = <ParseObject>[].obs;
  final RxBool isLoading = false.obs;
  final RxString medicoSelecionadoId = ''.obs;
  String? _medicoSelecionadoNome;

  late TabController _tabController;
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(() {
      // This ensures we're not updating state during build
      if (!_tabController.indexIsChanging) {
        if (_tabController.index == 0) {
          Future.microtask(() => _buscarSolicitacoesPendentes());
        } else if (_tabController.index == 1) {
          Future.microtask(() => _buscarPacientesRegistrados());
        }
      }
    });

    // Use microtask to ensure these run after build
    Future.microtask(() {
      _buscarPacientesRegistrados();
      _buscarSolicitacoesPendentes();
    });
  }

  @override
  void dispose() {
    searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _buscarSolicitacoesPendentes() async {
    if (isLoading.value) return;

    try {
      isLoading.value = true;

      // Log de início para debug
      debugPrint('=== INICIANDO BUSCA DE SOLICITAÇÕES ===');
      final startTime = BrazilTimeZone.now();

      if (controller.currentHospital == null) {
        debugPrint(
            'Hospital não encontrado, carregando dados da secretaria...');
        await controller.carregarDadosSecretaria();

        if (controller.currentHospital == null) {
          debugPrint('ERRO: Hospital atual não pôde ser carregado');
          if (mounted) {
            setState(() {
              solicitacoesPendentes.clear();
            });
          }
          return;
        }
      }

      debugPrint(
          'Hospital encontrado: ${controller.currentHospital!.objectId}');

      // Buscar solicitações pendentes no hospital atual
      final querySolicitacoes =
          QueryBuilder<ParseObject>(ParseObject('FilaSolicitacao'))
            ..whereEqualTo('hospitalId', controller.currentHospital)
            ..whereEqualTo('status', 'pendente')
            ..orderByDescending('createdAt')
            ..setLimit(50); // Limitar para melhorar performance

      final response = await querySolicitacoes.query();

      final endTime = BrazilTimeZone.now();
      final duration = endTime.difference(startTime);
      debugPrint('Query completada em: ${duration.inMilliseconds}ms');

      if (response.success && response.results != null) {
        debugPrint(
            'Encontradas ${response.results!.length} solicitações pendentes');

        // Use setState to safely update list
        if (mounted) {
          setState(() {
            solicitacoesPendentes.value = response.results!.cast<ParseObject>();
          });
        }
      } else {
        debugPrint(
            'Erro na query ou nenhuma solicitação encontrada: ${response.error?.message}');
        if (mounted) {
          setState(() {
            solicitacoesPendentes.clear();
          });
        }
      }

      debugPrint('=== BUSCA DE SOLICITAÇÕES FINALIZADA ===');
    } catch (e) {
      debugPrint('ERRO CRÍTICO ao buscar solicitações: $e');
      _mostrarMensagem('Erro ao buscar solicitações: $e', isError: true);
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> _buscarPacientesRegistrados() async {
    if (isLoading.value) return;

    try {
      isLoading.value = true;

      // Buscar pacientes que fizeram registro via app mas ainda não estão em nenhuma fila
      final queryPacientes = QueryBuilder<ParseObject>(ParseObject('Paciente'))
        ..whereEqualTo('em_fila', false)
        ..orderByDescending('ultimoAcesso')
        ..setLimit(50);

      final response = await queryPacientes.query();

      if (response.success && response.results != null) {
        final pacientes = response.results!;
        final List<Map<String, dynamic>> pacientesList = [];

        for (var paciente in pacientes) {
          pacientesList.add({
            'id': paciente.objectId!,
            'nome': paciente.get<String>('nome') ?? 'Nome não informado',
            'telefone':
                paciente.get<String>('telefone') ?? 'Telefone não informado',
            'userId': paciente.get<String>('deviceId') ?? '',
            'dataCadastro':
                BrazilTimeZone.parseObjectDateToBrazil(paciente.createdAt),
            'ultimoAcesso': paciente.get<DateTime>('ultimoAcesso'),
            'object': paciente,
          });
        }

        // Use setState to safely update list
        if (mounted) {
          setState(() {
            pacientesRegistrados.value = pacientesList;
          });
        }
      }
    } catch (e) {
      _mostrarMensagem('Erro ao buscar pacientes: $e', isError: true);
    } finally {
      isLoading.value = false;
    }
  }

  void _filtrarPacientes(String query) {
    if (query.isEmpty) {
      // Se o campo de busca estiver vazio, recarregar todos os pacientes
      _buscarPacientesRegistrados();
      return;
    }

    final queryLowerCase = query.toLowerCase();

    // Filtragem simples e direta na lista atual
    final List<Map<String, dynamic>> resultados = pacientesRegistrados
        .where((paciente) =>
            paciente['nome']
                .toString()
                .toLowerCase()
                .contains(queryLowerCase) ||
            paciente['telefone']
                .toString()
                .toLowerCase()
                .contains(queryLowerCase) ||
            paciente['userId']
                .toString()
                .toLowerCase()
                .contains(queryLowerCase))
        .toList();

    // Atualiza a lista com os resultados filtrados e notifica a UI
    setState(() {
      pacientesRegistrados.value = resultados;
      // Força a atualização da interface
      debugPrint(
          "Filtro aplicado: ${resultados.length} resultados encontrados");
    });
  }

  Future<void> _selecionarMedico() async {
    try {
      if (controller.medicos.isEmpty) {
        await controller.carregarMedicos();
      }

      // Check if still mounted before showing dialog
      if (!mounted) return;

      // Filtrar médicos duplicados por ID
      final medicosUnicos = <String, ParseObject>{};
      for (var medico in controller.medicos) {
        // Só adiciona o médico se ele ainda não estiver no mapa (usando ID como chave)
        if (!medicosUnicos.containsKey(medico.objectId)) {
          medicosUnicos[medico.objectId!] = medico;
        }
      }
      // Converter para lista para exibição
      final medicosFiltrados = medicosUnicos.values.toList();

      await showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Selecionar Médico'),
          content: SizedBox(
            width: double.maxFinite,
            child: medicosFiltrados.isEmpty
                ? Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.warning_amber_rounded,
                          size: 48,
                          color: Colors.amber,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'Nenhum médico vinculado a este hospital',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontFamily: 'Georgia',
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    shrinkWrap: true,
                    itemCount: medicosFiltrados.length,
                    itemBuilder: (context, index) {
                      final medico = medicosFiltrados[index];
                      final nome = medico.get<String>('nome') ?? 'Médico';
                      final especialidade =
                          medico.get<String>('especialidade') ??
                              'Especialidade';

                      return ListTile(
                        leading: CircleAvatar(
                          backgroundColor: Colors.teal.withOpacity(0.1),
                          child: const Icon(Icons.person, color: Colors.teal),
                        ),
                        title: Text(nome),
                        subtitle: Text(especialidade),
                        onTap: () {
                          setState(() {
                            medicoSelecionadoId.value = medico.objectId!;
                            _medicoSelecionadoNome = nome;
                            controller.medicoSelecionado.value = medico;
                          });
                          Navigator.pop(context);
                        },
                      );
                    },
                  ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: medicosFiltrados.isEmpty
                  ? const Text('Fechar')
                  : const Text('Cancelar'),
            ),
          ],
        ),
      );
    } catch (e) {
      _mostrarMensagem('Erro ao carregar médicos: $e', isError: true);
    }
  }

  Future<void> _adicionarPacienteAFilaDireta(
      Map<String, dynamic> paciente) async {
    try {
      setState(() => isLoading.value = true);

      // Buscar médico e consultório
      final medicoObj = ParseObject('Medico')
        ..objectId = medicoSelecionadoId.value;
      final consultorioObj = controller.currentHospital!;

      // Verificar última posição na fila
      final queryUltimaPosicao = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('medico', medicoObj)
        ..whereEqualTo('consultorio', consultorioObj)
        ..whereContainedIn('status', ['aguardando', 'em_atendimento'])
        ..orderByDescending('posicao');

      final posicaoResponse = await queryUltimaPosicao.query();
      final proximaPosicao = (posicaoResponse.results?.isEmpty ?? true)
          ? 1
          : (posicaoResponse.results!.first.get<int>('posicao') ?? 0) + 1;

      // Preparar registro na fila
      final novaFila = ParseObject('Fila')
        ..set('nome', paciente['nome'])
        ..set('telefone', paciente['telefone'])
        ..set('idPaciente', paciente['userId'])
        ..set('status', 'aguardando')
        ..set('posicao', proximaPosicao)
        ..set(
            'data_entrada', BrazilTimeZone.createForParse(BrazilTimeZone.now()))
        ..set('medico', medicoObj)
        ..set('consultorio', consultorioObj)
        ..set('usuario', paciente['object']);

      // Definir ACL totalmente aberta
      final acl = ParseACL();
      acl.setPublicReadAccess(allowed: true);
      acl.setPublicWriteAccess(allowed: true);

      // Permissões específicas (opcional, já que public write está ativado)
      final currentUser = await ParseUser.currentUser() as ParseUser?;
      if (currentUser != null) {
        acl.setReadAccess(userId: currentUser.objectId!, allowed: true);
        acl.setWriteAccess(userId: currentUser.objectId!, allowed: true);
      }

      novaFila.setACL(acl);

      final filaResponse = await novaFila.save();

      if (!filaResponse.success) {
        throw Exception(
            'Erro ao criar entrada na fila: ${filaResponse.error?.message}');
      }

      // Atualizar status do usuário
      final userObj = paciente['object'] as ParseObject;
      userObj.set('em_fila', true);
      userObj.set(
          'ultima_fila', BrazilTimeZone.createForParse(BrazilTimeZone.now()));
      await userObj.save();

      // Tentar enviar notificação
      try {
        if (paciente['userId'] != null && paciente['userId'].isNotEmpty) {
          final notification = ParseObject('Notification')
            ..set('title', 'Adicionado à Fila')
            ..set('body',
                'Você foi adicionado à fila do Dr. $_medicoSelecionadoNome')
            ..set('data', {
              'type': 'adicionado_fila',
              'filaId': filaResponse.results!.first.objectId
            })
            ..set('userId', paciente['userId'])
            ..set('read', false)
            ..set('sent', false)
            ..setACL(acl);
          await notification.save();
        }
      } catch (e) {
        debugPrint('Erro ao criar notificação: $e');
        // Continuar mesmo com erro
      }

      setState(() {
        pacientesRegistrados.removeWhere((p) => p['id'] == paciente['id']);
      });

      _mostrarMensagem('Paciente adicionado à fila com sucesso!');
    } catch (e) {
      _mostrarMensagem('Erro ao adicionar paciente: $e', isError: true);
    } finally {
      setState(() => isLoading.value = false);
    }
  }

  Future<void> _adicionarPacienteAFila(Map<String, dynamic> paciente) async {
    if (medicoSelecionadoId.isEmpty) {
      await _selecionarMedico();
      // Check if still mounted after async operation
      if (!mounted) return;

      // Se mesmo após tentar selecionar, ainda não houver médico, retornar
      if (medicoSelecionadoId.isEmpty) {
        _mostrarMensagem('Selecione um médico para adicionar o paciente à fila',
            isError: true);
        return;
      }
    }

    // Check if still mounted before showing dialog
    if (!mounted) return;

    // Confirmar adição
    final confirmacao = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmar Adição'),
        content: Text(
            'Adicionar ${paciente['nome']} à fila do Dr. $_medicoSelecionadoNome?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(backgroundColor: Colors.teal),
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Confirmar'),
          ),
        ],
      ),
    );

    // Check if still mounted after dialog
    if (!mounted) return;
    if (confirmacao != true) return;

    try {
      // Usar implementação direta em vez da Cloud Function
      await _adicionarPacienteAFilaDireta(paciente);
    } catch (e) {
      String errorMsg = e.toString();
      if (errorMsg.contains('Permission denied')) {
        _mostrarMensagem(
            'Erro de permissão: Verifique as configurações de acesso no Back4App',
            isError: true);
      } else {
        _mostrarMensagem('Erro ao adicionar paciente: $e', isError: true);
      }
    }
  }

  Future<void> _aceitarSolicitacaoDireta(ParseObject solicitacao) async {
    try {
      setState(() => isLoading.value = true);

      // Buscar médico e consultório
      final medicoObj = ParseObject('Medico')
        ..objectId = medicoSelecionadoId.value;
      final consultorioObj = controller.currentHospital!;

      // Verificar última posição na fila
      final queryUltimaPosicao = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('medico', medicoObj)
        ..whereEqualTo('consultorio', consultorioObj)
        ..whereContainedIn('status', ['aguardando', 'em_atendimento'])
        ..orderByDescending('posicao');

      final posicaoResponse = await queryUltimaPosicao.query();
      final proximaPosicao = (posicaoResponse.results?.isEmpty ?? true)
          ? 1
          : (posicaoResponse.results!.first.get<int>('posicao') ?? 0) + 1;

      // Dados do paciente
      final idPaciente = solicitacao.get<String>('idPaciente') ?? '';
      final nomePaciente =
          solicitacao.get<String>('nomePaciente') ?? 'Paciente';
      final telefonePaciente =
          solicitacao.get<String>('telefonePaciente') ?? '';

      // Atualizar status da solicitação
      solicitacao.set('status', 'aceito');

      // Configurar ACL para a solicitação
      final solicitacaoAcl = ParseACL();
      solicitacaoAcl.setPublicReadAccess(allowed: true);
      solicitacaoAcl.setPublicWriteAccess(allowed: true);
      solicitacao.setACL(solicitacaoAcl);

      await solicitacao.save();

      // Criar nova entrada na fila usando o modelo
      final novaFila = await Fila.createFila(
        nome: nomePaciente,
        telefone: telefonePaciente,
        idPaciente: idPaciente,
        posicao: proximaPosicao,
        medico: medicoObj,
        consultorio: consultorioObj,
        solicitacao: solicitacao,
      );

      // Registrar log de tentativa para diagnóstico
      try {
        final logDebug = ParseObject('LogDebug')
          ..set('tipo', 'tentativa_criar_fila')
          ..set('dados', {
            'medicoId': medicoSelecionadoId.value,
            'consultorioId': controller.currentHospital?.objectId,
            'pacienteNome': nomePaciente,
            'solicitacaoId': solicitacao.objectId
          })
          ..set('data', BrazilTimeZone.createForParse(BrazilTimeZone.now()));
        await logDebug.save();
      } catch (e) {
        debugPrint('Erro ao criar log diagnóstico: $e');
      }

      // Usar masterKey para salvar (se possível)
      try {
        final filaResponse = await novaFila.save();

        if (!filaResponse.success) {
          throw Exception(
              'Erro ao criar entrada na fila: ${filaResponse.error?.message}');
        }

        // Enviar notificação para o paciente
        try {
          if (idPaciente.isNotEmpty) {
            final notification = ParseObject('Notification')
              ..set('title', 'Solicitação Aceita')
              ..set('body',
                  'Você foi adicionado à fila do Dr. ${controller.medicoSelecionado.value?.get('nome') ?? _medicoSelecionadoNome}')
              ..set('data', {
                'type': 'fila_aceita',
                'filaId': filaResponse.results!.first.objectId
              })
              ..set('userId', idPaciente)
              ..set('read', false)
              ..set('sent', false);

            // Configurar ACL para a notificação
            final notificationAcl = ParseACL();
            notificationAcl.setPublicReadAccess(allowed: true);
            notificationAcl.setPublicWriteAccess(allowed: true);
            notification.setACL(notificationAcl);

            await notification.save();
          }
        } catch (e) {
          debugPrint('Erro ao criar notificação: $e');
          // Continuar mesmo com erro
        }

        // Atualizar UI
        setState(() {
          solicitacoesPendentes.remove(solicitacao);
        });

        // Registrar sucesso
        _mostrarMensagem('Paciente adicionado à fila com sucesso!');
        _buscarSolicitacoesPendentes();
      } catch (saveError) {
        debugPrint('Erro detalhado ao salvar fila: $saveError');

        // Tentar salvar usando uma Cloud Function como fallback
        try {
          final params = {
            'solicitacaoId': solicitacao.objectId,
            'medicoId': medicoSelecionadoId.value,
            'consultorioId': controller.currentHospital?.objectId,
            'secretariaId': controller.currentSecretaria?.objectId,
            'nome': nomePaciente,
            'telefone': telefonePaciente
          };

          final cloudResponse =
              await ParseCloudFunction('adicionarPacienteNaFila')
                  .execute(parameters: params);

          if (cloudResponse.success) {
            setState(() {
              solicitacoesPendentes.remove(solicitacao);
            });

            _mostrarMensagem(
                'Paciente adicionado à fila com sucesso usando Cloud Function!');
            _buscarSolicitacoesPendentes();
          } else {
            throw Exception(
                'Erro ao usar Cloud Function: ${cloudResponse.error?.message}');
          }
        } catch (cloudError) {
          throw Exception(
              'Todos os métodos de adicionar paciente falharam. Erro: $saveError. Cloud Function: $cloudError');
        }
      }
    } catch (e) {
      debugPrint('Erro completo ao adicionar paciente: $e');
      _mostrarMensagem('Erro ao adicionar paciente: $e', isError: true);
    } finally {
      setState(() => isLoading.value = false);
    }
  }

  Future<void> _acceptRequest(ParseObject solicitacao) async {
    if (!mounted) return;

    if (medicoSelecionadoId.isEmpty) {
      await _selecionarMedico();
      if (!mounted || medicoSelecionadoId.isEmpty) {
        _mostrarMensagem('Selecione um médico para aceitar a solicitação',
            isError: true);
        return;
      }
    }

    if (controller.currentHospital == null) {
      _mostrarMensagem('Erro: Hospital atual não definido', isError: true);
      return;
    }

    final confirmacao = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmar Aceitação'),
        content: Text(
            'Aceitar solicitação do paciente ${solicitacao.get<String>('nomePaciente') ?? 'Paciente'}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(backgroundColor: Colors.teal),
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Confirmar'),
          ),
        ],
      ),
    );

    if (confirmacao == true && mounted) {
      await _aceitarSolicitacaoDireta(solicitacao);
    }
  }

  Future<void> _recusarSolicitacao(ParseObject solicitacao) async {
    try {
      if (!mounted) return;

      final confirmacao = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Confirmar Recusa'),
          content: Text(
              'Recusar solicitação do paciente ${solicitacao.get<String>('nomePaciente') ?? 'Paciente'}?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Cancelar'),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              onPressed: () => Navigator.pop(context, true),
              child: const Text('Confirmar'),
            ),
          ],
        ),
      );

      if (!mounted) return;
      if (confirmacao != true) return;

      setState(() => isLoading.value = true);

      solicitacao.set('status', 'recusado');
      await solicitacao.save();

      // Se conseguiu salvar, remover da lista
      setState(() {
        solicitacoesPendentes.remove(solicitacao);
      });

      _mostrarMensagem('Solicitação recusada com sucesso!');
    } catch (e) {
      _mostrarMensagem('Erro ao recusar solicitação: $e', isError: true);
    } finally {
      setState(() => isLoading.value = false);
    }
  }

  void _mostrarMensagem(String mensagem, {bool isError = false}) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(mensagem),
        backgroundColor: isError ? Colors.red : Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset:
          false, // Evita que a tela redimensione quando o teclado aparece
      appBar: AppBar(
        title: const Text('Aceitar Pacientes'),
      ),
      body: GradientBackground(
        child: SafeArea(
          child: Column(
            children: [
              // Médico selecionado
              medicoSelecionadoId.isNotEmpty
                  ? Column(
                      children: [
                        Container(
                          margin: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            color: Colors.teal.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.person, color: Colors.teal),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'Médico: $_medicoSelecionadoNome',
                                  style: const TextStyle(
                                    fontFamily: 'Georgia',
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              IconButton(
                                icon: const Icon(Icons.change_circle,
                                    color: Colors.teal),
                                onPressed: _selecionarMedico,
                                tooltip: 'Trocar médico',
                              ),
                            ],
                          ),
                        ),
                      ],
                    )
                  : Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          child: ElevatedButton.icon(
                            onPressed: _selecionarMedico,
                            icon: const Icon(Icons.person_add),
                            label: const Text('Selecionar Médico'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.teal,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

              // Tab Bar para separar solicitações e pacientes
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(25),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: TabBar(
                    controller: _tabController,
                    isScrollable: true,
                    labelPadding: const EdgeInsets.symmetric(horizontal: 10),
                    tabs: [
                      Tab(
                        height: 40,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.qr_code_scanner, size: 16),
                            const SizedBox(width: 4),
                            const Text("Solicitações",
                                style: TextStyle(fontSize: 11)),
                            Obx(() => solicitacoesPendentes.isNotEmpty
                                ? Container(
                                    margin: const EdgeInsets.only(left: 4),
                                    padding: const EdgeInsets.all(3),
                                    decoration: const BoxDecoration(
                                      color: Colors.red,
                                      shape: BoxShape.circle,
                                    ),
                                    constraints: const BoxConstraints(
                                      minWidth: 14,
                                      minHeight: 14,
                                    ),
                                    child: Text(
                                      solicitacoesPendentes.length.toString(),
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 8,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  )
                                : const SizedBox.shrink()),
                          ],
                        ),
                      ),
                      const Tab(
                        height: 40,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.person_add, size: 16),
                            SizedBox(width: 4),
                            Text("Pacientes", style: TextStyle(fontSize: 11)),
                          ],
                        ),
                      ),
                      const Tab(
                        height: 40,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.qr_code, size: 16),
                            SizedBox(width: 4),
                            Text("QR Code", style: TextStyle(fontSize: 11)),
                          ],
                        ),
                      ),
                    ],
                    labelColor: Colors.teal,
                    unselectedLabelColor: Colors.grey,
                    indicatorColor: Colors.teal,
                    indicatorSize: TabBarIndicatorSize.tab,
                  ),
                ),
              ),

              // Tab content
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    // Tab 1: Solicitações QR code
                    _buildSolicitacoesTab(),

                    // Tab 2: Pacientes registrados
                    _buildPacientesTab(),

                    // Tab 3: QR Code
                    _buildQRCodeTab(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          setState(() => isLoading.value = true);

          if (_tabController.index == 0) {
            await _buscarSolicitacoesPendentes();
          } else if (_tabController.index == 1) {
            await _buscarPacientesRegistrados();
          } else if (_tabController.index == 2) {
            if (medicoSelecionadoId.isNotEmpty) {
              await _mostrarQRCode();
            } else {
              _mostrarMensagem(
                  'Selecione um médico primeiro para gerar o QR Code');
            }
          }

          setState(() => isLoading.value = false);
          _mostrarMensagem('Atualizado com sucesso!', isError: false);
        },
        backgroundColor: Colors.teal,
        child: const Icon(Icons.refresh, color: Colors.white),
        tooltip: 'Atualizar',
      ),
    );
  }

  Widget _buildSolicitacoesTab() {
    if (isLoading.value) {
      return const Center(child: CircularProgressIndicator());
    }

    if (solicitacoesPendentes.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.qr_code_scanner,
              size: 64,
              color: Colors.grey.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            const Text(
              'Nenhuma solicitação pendente',
              style: TextStyle(
                fontFamily: 'Georgia',
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Solicitações de pacientes via QR code\naparecerão aqui',
              style: TextStyle(
                fontFamily: 'Georgia',
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: solicitacoesPendentes.length,
      itemBuilder: (context, index) {
        final solicitacao = solicitacoesPendentes[index];
        return _buildSolicitacaoCard(solicitacao);
      },
    );
  }

  Widget _buildSolicitacaoCard(ParseObject solicitacao) {
    final nome = solicitacao.get<String>('nome') ?? 'Paciente';
    final createdAt = solicitacao.createdAt;

    final dataFormatada = createdAt != null
        ? BrazilDateFormat.formatDateTime(
            BrazilTimeZone.parseObjectDateToBrazil(createdAt))
        : 'Data desconhecida';

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          ListTile(
            leading: const CircleAvatar(
              backgroundColor: Colors.blue,
              child: Icon(Icons.qr_code_scanner, color: Colors.white),
            ),
            title: Text(
              nome,
              style: const TextStyle(
                fontFamily: 'Georgia',
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Solicitado em: $dataFormatada'),
              ],
            ),
            isThreeLine: true,
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                OutlinedButton.icon(
                  onPressed: () => _recusarSolicitacao(solicitacao),
                  icon: const Icon(Icons.close, color: Colors.red),
                  label: const Text('Recusar',
                      style: TextStyle(color: Colors.red)),
                  style: OutlinedButton.styleFrom(
                    side: const BorderSide(color: Colors.red),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed: () => _acceptRequest(solicitacao),
                  icon: const Icon(Icons.check),
                  label: const Text('Aceitar'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.teal,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPacientesTab() {
    return Column(
      children: [
        // Search bar
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: TextField(
            controller: searchController,
            decoration: InputDecoration(
              hintText: 'Buscar paciente...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: IconButton(
                icon: const Icon(Icons.clear),
                onPressed: () {
                  searchController.clear();
                  _buscarPacientesRegistrados();
                },
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Colors.white,
            ),
            onChanged: _filtrarPacientes,
          ),
        ),

        // Patient list
        Expanded(
          child: Builder(builder: (context) {
            // Verificar carregamento
            if (isLoading.value) {
              return const Center(child: CircularProgressIndicator());
            }

            // Verificar se a lista está vazia
            if (pacientesRegistrados.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.people,
                      size: 70,
                      color: Colors.grey.withOpacity(0.5),
                    ),
                    const SizedBox(height: 24),
                    Container(
                      width: MediaQuery.of(context).size.width * 0.8,
                      padding: const EdgeInsets.symmetric(
                        vertical: 16,
                        horizontal: 24,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: const Text(
                        'Nenhum paciente encontrado. Tente outro nome!',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontFamily: 'Georgia',
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Altere os termos da busca ou tente novamente',
                      style: TextStyle(
                        fontFamily: 'Georgia',
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              );
            }

            // Lista de pacientes
            return ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: pacientesRegistrados.length,
              itemBuilder: (context, index) {
                // Verificação de segurança para evitar o erro
                if (index >= pacientesRegistrados.length) {
                  return const SizedBox.shrink(); // Widget vazio
                }

                final paciente = pacientesRegistrados[index];
                final ultimoAcesso = paciente['ultimoAcesso'] as DateTime?;
                final dataFormatada = ultimoAcesso != null
                    ? BrazilDateFormat.formatDateTime(
                        BrazilTimeZone.parseObjectDateToBrazil(ultimoAcesso))
                    : 'Data desconhecida';

                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Colors.blue.withOpacity(0.1),
                      child: const Icon(Icons.person, color: Colors.blue),
                    ),
                    title: Text(
                      paciente['nome'],
                      style: const TextStyle(
                        fontFamily: 'Georgia',
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Telefone: ${paciente['telefone']}'),
                        Text('ID: ${paciente['userId']}'),
                        Text('Último acesso: $dataFormatada'),
                      ],
                    ),
                    isThreeLine: true,
                    trailing: IconButton(
                      icon: const Icon(Icons.add_circle, color: Colors.green),
                      onPressed: () => _adicionarPacienteAFila(paciente),
                      tooltip: 'Adicionar à fila',
                    ),
                  ),
                );
              },
            );
          }),
        ),
      ],
    );
  }

  // Add QR Code generation functions
  Widget _buildQRCodeTab() {
    if (isLoading.value) {
      return const Center(child: CircularProgressIndicator());
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.qr_code_2,
            size: 100,
            color: Colors.teal.withOpacity(0.5),
          ),
          const SizedBox(height: 24),
          const Text(
            'QR Code de Atendimento',
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 22,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              'O QR Code permite que pacientes entrem na fila de atendimento escaneando com seus celulares',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontFamily: 'Georgia',
                fontSize: 16,
                color: Colors.black54,
              ),
            ),
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: medicoSelecionadoId.isEmpty
                ? _selecionarMedico
                : _mostrarQRCode,
            icon: const Icon(Icons.qr_code),
            label: Text(
              medicoSelecionadoId.isEmpty
                  ? 'Selecione um médico primeiro'
                  : 'Gerar QR Code para Dr. $_medicoSelecionadoNome',
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Método para mostrar QR Code, verificando se existe ou gerando um novo
  Future<void> _mostrarQRCode() async {
    if (controller.currentHospital == null) {
      _mostrarMensagem('Hospital não encontrado. Verifique sua configuração.',
          isError: true);
      return;
    }

    if (medicoSelecionadoId.value.isEmpty) {
      _mostrarMensagem('Selecione um médico antes de gerar o QR Code.',
          isError: true);
      return;
    }

    setState(() => isLoading.value = true);
    debugPrint('\n========== GERANDO QR CODE ==========');
    debugPrint(
        'Médico ID: ${medicoSelecionadoId.value}, Hospital ID: ${controller.currentHospital?.objectId}');

    try {
      // Verificar se já existe um QR code válido para este médico
      final queryQRExistente =
          QueryBuilder<ParseObject>(ParseObject('QRCodeGerado'))
            ..whereEqualTo('medico_id',
                ParseObject('Medico')..objectId = medicoSelecionadoId.value)
            ..whereEqualTo(
                'consultorio_id',
                ParseObject('consultorio')
                  ..objectId = controller.currentHospital!.objectId)
            ..whereEqualTo('valido', true);

      final responseQRExistente = await queryQRExistente.query();

      if (!responseQRExistente.success ||
          responseQRExistente.results == null ||
          responseQRExistente.results!.isEmpty) {
        // Não encontrou QR code válido, gerar um novo
        debugPrint('QR Code válido não encontrado. Gerando novo QR Code.');
        await _gerarNovoQRCode();
      } else {
        // Encontrou QR code válido, exibir
        debugPrint('QR Code válido encontrado. Exibindo QR Code existente.');
        setState(() => isLoading.value = false);
        _exibirQRCode(responseQRExistente.results!.first);
      }
    } catch (e) {
      debugPrint('Erro ao buscar/gerar QR Code: $e');
      setState(() => isLoading.value = false);
      _mostrarMensagem(
          'Erro ao buscar QR Code: ${e.toString().replaceAll('Exception: ', '')}',
          isError: true);
    }
  }

  // Método para gerar um novo QR Code
  Future<void> _gerarNovoQRCode() async {
    try {
      setState(() => isLoading.value = true);

      // 1. Invalidar QR codes antigos
      debugPrint('Invalidando QR codes antigos...');
      try {
        final queryQRExistente =
            QueryBuilder<ParseObject>(ParseObject('QRCodeGerado'))
              ..whereEqualTo('medico_id',
                  ParseObject('Medico')..objectId = medicoSelecionadoId.value)
              ..whereEqualTo(
                  'consultorio_id',
                  ParseObject('consultorio')
                    ..objectId = controller.currentHospital!.objectId)
              ..whereEqualTo('valido', true);

        final responseQRExistente = await queryQRExistente.query();

        if (responseQRExistente.success &&
            responseQRExistente.results != null &&
            responseQRExistente.results!.isNotEmpty) {
          for (var qrCode in responseQRExistente.results!) {
            qrCode.set('valido', false);
            qrCode.set('data_invalidacao',
                BrazilTimeZone.createForParse(BrazilTimeZone.now()));
            await qrCode.save();
          }
        }
      } catch (e) {
        debugPrint('Aviso: Erro ao invalidar QR codes antigos: $e');
        // Continuar mesmo com erro
      }

      // 2. Gerar identificadores únicos
      final agora = BrazilTimeZone.now();
      final timestamp = agora.millisecondsSinceEpoch.toString();
      final sequencial =
          '${agora.day}${agora.month}${agora.year.toString().substring(2)}-${timestamp.substring(timestamp.length - 6)}';
      final qrId = 'QR-${medicoSelecionadoId.value.substring(0, 4)}-$timestamp';

      debugPrint('Gerando QR Code com ID: $qrId, Sequencial: $sequencial');

      // 3. Criar novo objeto QR Code
      final qrCodeGerado = ParseObject('QRCodeGerado');

      // Dados básicos
      qrCodeGerado.set('qr_id', qrId);
      qrCodeGerado.set('sequencial', sequencial);

      // Referências
      qrCodeGerado.set('medico_id',
          ParseObject('Medico')..objectId = medicoSelecionadoId.value);
      qrCodeGerado.set(
          'consultorio_id',
          ParseObject('consultorio')
            ..objectId = controller.currentHospital!.objectId);

      // Status
      qrCodeGerado.set('valido', true);
      qrCodeGerado.set('impresso', false);
      qrCodeGerado.set('status', 'ativo');

      // Metadados
      qrCodeGerado.set(
          'data_criacao', BrazilTimeZone.createForParse(BrazilTimeZone.now()));
      qrCodeGerado.set(
          'data_expiracao',
          BrazilTimeZone.createForParse(
              BrazilTimeZone.now().add(const Duration(days: 30))));
      qrCodeGerado.set('versao', '2.0');

      // 4. Definir ACL totalmente aberta para diagnóstico
      final acl = ParseACL();
      acl.setPublicReadAccess(allowed: true);
      acl.setPublicWriteAccess(allowed: true);
      qrCodeGerado.setACL(acl);

      // 5. Salvar o QR Code
      final result = await qrCodeGerado.save();

      if (!mounted) return;
      setState(() => isLoading.value = false);

      if (result.success) {
        try {
          // Registrar log de criação (opcional)
          final qrLog = ParseObject('QRCodeLog');
          qrLog.set('action', 'criacao_direta');
          qrLog.set('qr_code', result.results!.first);
          qrLog.set('details', 'QR Code gerado diretamente pela secretária');
          qrLog.set(
              'timestamp', BrazilTimeZone.createForParse(BrazilTimeZone.now()));
          qrLog.set('medico_id',
              ParseObject('Medico')..objectId = medicoSelecionadoId.value);
          qrLog.set(
              'consultorio_id',
              ParseObject('consultorio')
                ..objectId = controller.currentHospital!.objectId);
          qrLog.setACL(acl);
          await qrLog.save();
        } catch (logError) {
          debugPrint('Erro ao registrar log: $logError');
          // Ignorar erros de log
        }

        // Exibir QR code
        _exibirQRCode(result.results!.first);
      } else {
        throw Exception('Erro ao salvar QR Code: ${result.error?.message}');
      }
    } catch (e) {
      debugPrint('Erro ao gerar QR Code: $e');
      if (mounted) {
        setState(() => isLoading.value = false);
        _mostrarMensagem('Erro ao gerar QR Code: $e', isError: true);
      }
    }
  }

  // Exibir o QR Code em um diálogo
  void _exibirQRCode(ParseObject qrCode) {
    if (!mounted) return;

    final qrData = {
      'qrId': qrCode.get<String>('qr_id') ?? '',
      'medicoId': medicoSelecionadoId.value,
      'medicoNome': _medicoSelecionadoNome ?? 'Médico',
      'especialidade':
          controller.medicoSelecionado.value?.get('especialidade') ??
              'Especialidade',
      'hospitalId': controller.currentHospital!.objectId ?? '',
      'hospitalNome': controller.currentHospital!.get('nome') ?? 'Hospital',
      'timestamp': BrazilTimeZone.now().millisecondsSinceEpoch,
      'version': '2.0'
    };

    final jsonData = jsonEncode(qrData);
    debugPrint('Dados do QR Code: $jsonData');

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          constraints: const BoxConstraints(maxWidth: 400),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color(0xFFE0EAFC),
                Color(0xFFCFDEF3),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
          ),
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Column(
                          children: [
                            const Text(
                              'QR Code de Atendimento',
                              style: TextStyle(
                                fontFamily: 'Georgia',
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Dr. $_medicoSelecionadoNome',
                              style: const TextStyle(
                                fontFamily: 'Georgia',
                                fontSize: 18,
                                fontWeight: FontWeight.normal,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            Text(
                              controller.medicoSelecionado.value
                                      ?.get('especialidade') ??
                                  '',
                              style: TextStyle(
                                fontFamily: 'Georgia',
                                fontSize: 16,
                                color: Colors.grey[600],
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  Container(
                    width: 280,
                    height: 280,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.black, width: 2),
                      borderRadius: BorderRadius.circular(12),
                      color: Colors.white,
                    ),
                    child: QrImageView(
                      data: jsonData,
                      version: QrVersions.auto,
                      size: 250,
                      backgroundColor: Colors.white,
                      errorCorrectionLevel: QrErrorCorrectLevel.H,
                    ),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'QR Code atualizado em:',
                    style: TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  Text(
                    BrazilDateFormat.formatDateTime(BrazilTimeZone.now()),
                    style: const TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'Sequencial: ${qrCode.get<String>('sequencial') ?? "N/A"}',
                    style: const TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 24),
                  Wrap(
                    spacing: 16,
                    runSpacing: 16,
                    alignment: WrapAlignment.center,
                    children: [
                      ElevatedButton.icon(
                        onPressed: () => _imprimirQRCode(qrCode),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFFE0EAFC),
                          foregroundColor: Colors.black,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 12,
                          ),
                          elevation: 2,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        icon: const Icon(Icons.print),
                        label: const Text(
                          'Imprimir',
                          style: TextStyle(
                            fontFamily: 'Georgia',
                          ),
                        ),
                      ),
                      ElevatedButton.icon(
                        onPressed: () {
                          Navigator.pop(context);
                          _gerarNovoQRCode();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFFE0EAFC),
                          foregroundColor: Colors.black,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 12,
                          ),
                          elevation: 2,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        icon: const Icon(Icons.refresh),
                        label: const Text(
                          'Atualizar',
                          style: TextStyle(
                            fontFamily: 'Georgia',
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Este QR Code pode ser impresso e fixado na recepção',
                    style: TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 14,
                      fontStyle: FontStyle.italic,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Método para imprimir o QR Code
  Future<void> _imprimirQRCode(ParseObject qrCode) async {
    try {
      debugPrint('Iniciando processo de impressão do QR Code');
      final jaImpresso = qrCode.get<bool>('impresso') ?? false;

      if (jaImpresso) {
        debugPrint('QR Code já foi impresso anteriormente');
        if (!mounted) return;

        final reimprimirConfirmacao = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('QR Code já impresso'),
            content: const Text(
                'Este QR Code já foi impresso anteriormente. Deseja imprimir novamente?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('Não'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: const Text('Sim'),
              ),
            ],
          ),
        );

        if (reimprimirConfirmacao != true) {
          debugPrint('Usuário cancelou a reimpressão');
          return;
        }

        debugPrint('Usuário confirmou a reimpressão');
      }

      final pdf = pw.Document();

      debugPrint('Criando PDF para impressão');

      final qrData = {
        'qrId': qrCode.get<String>('qr_id') ?? '',
        'medicoId': medicoSelecionadoId.value,
        'medicoNome': _medicoSelecionadoNome ?? 'Médico',
        'especialidade':
            controller.medicoSelecionado.value?.get('especialidade') ??
                'Especialidade',
        'hospitalId': controller.currentHospital!.objectId ?? '',
        'hospitalNome': controller.currentHospital!.get('nome') ?? 'Hospital',
        'timestamp': BrazilTimeZone.now().millisecondsSinceEpoch,
        'version': '2.0'
      };

      final qrPainter = QrPainter(
        data: jsonEncode(qrData),
        version: QrVersions.auto,
        errorCorrectionLevel: QrErrorCorrectLevel.H,
        eyeStyle: const QrEyeStyle(
          eyeShape: QrEyeShape.square,
          color: Color(0xFF000000),
        ),
        dataModuleStyle: const QrDataModuleStyle(
          dataModuleShape: QrDataModuleShape.square,
          color: Color(0xFF000000),
        ),
      );

      final qrImageData = await qrPainter.toImageData(300);

      if (qrImageData == null) {
        throw Exception('Falha ao gerar a imagem do QR code para impressão');
      }

      debugPrint('Imagem do QR Code gerada com sucesso');

      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Center(
              child: pw.Column(
                mainAxisAlignment: pw.MainAxisAlignment.center,
                children: [
                  pw.Text(
                    'QR Code de Atendimento',
                    style: pw.TextStyle(
                      fontSize: 24,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(height: 10),
                  pw.Text(
                    'Dr. $_medicoSelecionadoNome',
                    style: const pw.TextStyle(fontSize: 18),
                  ),
                  pw.Text(
                    controller.medicoSelecionado.value?.get('especialidade') ??
                        'Especialidade',
                    style: const pw.TextStyle(fontSize: 16),
                  ),
                  pw.SizedBox(height: 20),
                  pw.Container(
                    width: 300,
                    height: 300,
                    child: pw.Image(
                      pw.MemoryImage(qrImageData.buffer.asUint8List()),
                    ),
                  ),
                  pw.SizedBox(height: 20),
                  pw.Text(
                    'Data de geração: ${BrazilDateFormat.formatDateTime(BrazilTimeZone.now())}',
                    style: const pw.TextStyle(fontSize: 14),
                  ),
                  pw.SizedBox(height: 10),
                  pw.Text(
                    'Sequencial: ${qrCode.get<String>('sequencial') ?? "N/A"}',
                    style: const pw.TextStyle(fontSize: 14),
                  ),
                  pw.SizedBox(height: 30),
                  pw.Text(
                    'Este QR Code deve ser escaneado pelo paciente\npara entrar na fila de atendimento',
                    style: const pw.TextStyle(fontSize: 12),
                    textAlign: pw.TextAlign.center,
                  ),
                ],
              ),
            );
          },
        ),
      );

      debugPrint('Enviando PDF para impressão');
      final result = await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdf.save(),
        name: 'QR Code - Dr. $_medicoSelecionadoNome',
      );

      if (result) {
        debugPrint('QR Code enviado para impressão com sucesso');

        // Atualizar status de impressão
        qrCode.set('impresso', true);
        await qrCode.save();

        // Registrar log de impressão
        final logImpressao = ParseObject('QRCodeImpressaoLog')
          ..set('qr_code', qrCode)
          ..set('data_impressao',
              BrazilTimeZone.createForParse(BrazilTimeZone.now()))
          ..set('medico_id', medicoSelecionadoId.value);
        await logImpressao.save();

        debugPrint('Status de impressão e log atualizados com sucesso');

        if (!mounted) return;
        _mostrarMensagem('QR Code impresso com sucesso!', isError: false);
      } else {
        debugPrint('Impressão cancelada ou não concluída');
      }
    } catch (e) {
      debugPrint('Erro ao imprimir QR Code: $e');
      if (mounted) {
        _mostrarMensagem(
            'Erro ao imprimir QR Code: ${e.toString().replaceAll('Exception: ', '')}',
            isError: true);
      }
    }
  }
}
