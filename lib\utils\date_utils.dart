import 'package:intl/intl.dart';

/// ✅ FUSO HORÁRIO DO BRASIL (GMT-3) - CONSOLIDADO
class BrazilTimeZone {
  static const int offsetHours = -3; // GMT-3

  /// Converte DateTime UTC para horário do Brasil
  static DateTime utcToBrazil(DateTime utcDateTime) {
    return utcDateTime.add(Duration(hours: offsetHours));
  }

  /// Converte DateTime do Brasil para UTC
  static DateTime brazilToUtc(DateTime brazilDateTime) {
    return brazilDateTime.subtract(Duration(hours: offsetHours));
  }

  /// Obtém DateTime atual no horário do Brasil
  static DateTime now() {
    final utcNow = DateTime.now().toUtc();
    return utcToBrazil(utcNow);
  }

  /// ✅ COMPATIBILIDADE: Método do timezone_utils.dart
  static DateTime getNowBrasil() {
    return now();
  }

  /// ✅ COMPATIBILIDADE: Para operações de backend
  static DateTime nowForBackend() {
    return now();
  }

  /// ✅ COMPATIBILIDADE: Horário do dispositivo
  static DateTime getNowInDeviceTimezone() {
    return DateTime.now();
  }

  /// Converte ParseObject date para horário do Brasil
  static DateTime? parseObjectDateToBrazil(DateTime? parseDate) {
    if (parseDate == null) return null;

    // Parse sempre retorna em UTC, então convertemos para Brasil
    return utcToBrazil(parseDate.toUtc());
  }

  /// Cria DateTime para salvar no Parse (converte Brasil para UTC)
  static DateTime createForParse(DateTime brazilDateTime) {
    return brazilToUtc(brazilDateTime);
  }

  /// ✅ COMPATIBILIDADE: Converte DateTime para string ISO8601 com timezone do Brasil
  static String toIso8601StringBrasil(DateTime dateTime) {
    final brasilTime = dateTime.isUtc
        ? utcToBrazil(dateTime)
        : brazilToUtc(dateTime).add(Duration(hours: offsetHours));

    final isoString = brasilTime.toIso8601String();
    final withoutZ = isoString.replaceAll('Z', '');
    return '$withoutZ-03:00';
  }

  /// ✅ COMPATIBILIDADE: Converte DateTime atual para string no timezone do Brasil
  static String nowToIso8601StringBrasil() {
    return toIso8601StringBrasil(now());
  }

  /// ✅ COMPATIBILIDADE: Parse string ISO8601 considerando timezone do Brasil
  static DateTime parseIso8601Brasil(String dateString) {
    final parsed = DateTime.parse(dateString);
    if (dateString.contains('-03:00') || dateString.contains('GMT-3')) {
      return parsed;
    }
    // Se não tem timezone, assume que é do Brasil
    return parsed.add(Duration(hours: -offsetHours));
  }

  /// ✅ COMPATIBILIDADE: Obtém data de hoje no Brasil (00:00:00)
  static DateTime getTodayBrasil() {
    final nowBrasil = now();
    return DateTime(nowBrasil.year, nowBrasil.month, nowBrasil.day);
  }

  /// ✅ COMPATIBILIDADE: Obtém diferença em minutos entre duas datas
  static int getDifferenceInMinutes(DateTime start, DateTime end) {
    return end.difference(start).inMinutes;
  }

  /// ✅ COMPATIBILIDADE: Verifica se uma data é de hoje (Brasil)
  static bool isToday(DateTime date) {
    final today = getTodayBrasil();
    final dateBrasil = date.isUtc ? utcToBrazil(date) : date;
    return dateBrasil.year == today.year &&
        dateBrasil.month == today.month &&
        dateBrasil.day == today.day;
  }
}

/// ✅ FORMATADORES PADRONIZADOS PARA O BRASIL
class BrazilDateFormat {
  static final _dateTimeFormat = DateFormat('dd/MM/yyyy HH:mm');
  static final _dateFormat = DateFormat('dd/MM/yyyy');
  static final _timeFormat = DateFormat('HH:mm');
  static final _fullDateTimeFormat = DateFormat('dd/MM/yyyy HH:mm:ss');

  /// Formatar data e hora no padrão brasileiro
  static String formatDateTime(DateTime? dateTime) {
    if (dateTime == null) return '--/--/---- --:--';

    // Garantir que está no horário do Brasil
    final brazilTime =
        dateTime.isUtc ? BrazilTimeZone.utcToBrazil(dateTime) : dateTime;

    return _dateTimeFormat.format(brazilTime);
  }

  /// ✅ COMPATIBILIDADE: Formata data para exibição brasileira
  static String formatBrasil(DateTime dateTime,
      {String pattern = 'dd/MM/yyyy HH:mm'}) {
    final brazilTime =
        dateTime.isUtc ? BrazilTimeZone.utcToBrazil(dateTime) : dateTime;

    final formatter = DateFormat(pattern, 'pt_BR');
    return formatter.format(brazilTime);
  }

  /// Formatar apenas data no padrão brasileiro
  static String formatDate(DateTime? dateTime) {
    if (dateTime == null) return '--/--/----';

    final brazilTime =
        dateTime.isUtc ? BrazilTimeZone.utcToBrazil(dateTime) : dateTime;

    return _dateFormat.format(brazilTime);
  }

  /// Formatar apenas hora no padrão brasileiro
  static String formatTime(DateTime? dateTime) {
    if (dateTime == null) return '--:--';

    final brazilTime =
        dateTime.isUtc ? BrazilTimeZone.utcToBrazil(dateTime) : dateTime;

    return _timeFormat.format(brazilTime);
  }

  /// Formatar data e hora completa com segundos
  static String formatFullDateTime(DateTime? dateTime) {
    if (dateTime == null) return '--/--/---- --:--:--';

    final brazilTime =
        dateTime.isUtc ? BrazilTimeZone.utcToBrazil(dateTime) : dateTime;

    return _fullDateTimeFormat.format(brazilTime);
  }
}

/// ✅ COMPATIBILIDADE TOTAL COM timezone_utils.dart
class TimezoneUtils {
  /// ✅ REDIRECIONAMENTO: Timezone do Brasil (UTC-3)
  static const int brasilTimezoneOffsetHours = BrazilTimeZone.offsetHours;

  /// ✅ REDIRECIONAMENTO: Obtém data atual no timezone do Brasil (GMT-3)
  static DateTime getNowBrasil() => BrazilTimeZone.getNowBrasil();

  /// ✅ REDIRECIONAMENTO: Obtém data atual formatada para operações de backend
  static DateTime nowForBackend() => BrazilTimeZone.nowForBackend();

  /// ✅ REDIRECIONAMENTO: Obtém data atual no timezone do dispositivo
  static DateTime getNowInDeviceTimezone() =>
      BrazilTimeZone.getNowInDeviceTimezone();

  /// ✅ REDIRECIONAMENTO: Converte DateTime para string ISO8601 com timezone do Brasil
  static String toIso8601StringBrasil(DateTime dateTime) =>
      BrazilTimeZone.toIso8601StringBrasil(dateTime);

  /// ✅ REDIRECIONAMENTO: Converte DateTime atual para string no timezone do Brasil
  static String nowToIso8601StringBrasil() =>
      BrazilTimeZone.nowToIso8601StringBrasil();

  /// ✅ REDIRECIONAMENTO: Parse string ISO8601 considerando timezone do Brasil
  static DateTime parseIso8601Brasil(String dateString) =>
      BrazilTimeZone.parseIso8601Brasil(dateString);

  /// ✅ REDIRECIONAMENTO: Formata data para exibição brasileira
  static String formatBrasil(DateTime dateTime,
      {String pattern = 'dd/MM/yyyy HH:mm'}) {
    return BrazilDateFormat.formatBrasil(dateTime, pattern: pattern);
  }

  /// ✅ REDIRECIONAMENTO: Obtém data de hoje no Brasil (00:00:00)
  static DateTime getTodayBrasil() => BrazilTimeZone.getTodayBrasil();

  /// ✅ REDIRECIONAMENTO: Obtém diferença em minutos entre duas datas
  static int getDifferenceInMinutes(DateTime start, DateTime end) =>
      BrazilTimeZone.getDifferenceInMinutes(start, end);

  /// ✅ REDIRECIONAMENTO: Verifica se uma data é de hoje (Brasil)
  static bool isToday(DateTime date) => BrazilTimeZone.isToday(date);
}

/// ✅ FUNÇÃO ORIGINAL ATUALIZADA PARA USAR HORÁRIO DO BRASIL
String formatarTempoRelativo(DateTime? dataHora) {
  if (dataHora == null) {
    return 'Data não disponível';
  }

  // Usar horário do Brasil para ambas as datas
  final agora = BrazilTimeZone.now();
  final dataHoraBrasil =
      dataHora.isUtc ? BrazilTimeZone.utcToBrazil(dataHora) : dataHora;

  final diferenca = agora.difference(dataHoraBrasil);

  if (diferenca.inSeconds < 60) {
    return 'Agora';
  } else if (diferenca.inMinutes < 60) {
    return '${diferenca.inMinutes} ${diferenca.inMinutes == 1 ? 'minuto' : 'minutos'} atrás';
  } else if (diferenca.inHours < 24) {
    return '${diferenca.inHours} ${diferenca.inHours == 1 ? 'hora' : 'horas'} atrás';
  } else if (diferenca.inDays < 30) {
    return '${diferenca.inDays} ${diferenca.inDays == 1 ? 'dia' : 'dias'} atrás';
  } else {
    return BrazilDateFormat.formatDateTime(dataHoraBrasil);
  }
}

/// ✅ HELPERS ESPECÍFICOS PARA O APP
class FilaAppDateUtils {
  /// Formatar tempo de espera em minutos para display
  static String formatarTempoEspera(int minutos) {
    if (minutos < 60) {
      return '${minutos}min';
    } else {
      final horas = minutos ~/ 60;
      final min = minutos % 60;
      return '${horas}h${min > 0 ? ' ${min}min' : ''}';
    }
  }

  /// Calcular tempo decorrido desde uma data
  static String calcularTempoDecorrido(DateTime? dataInicio) {
    if (dataInicio == null) return '0min';

    final agora = BrazilTimeZone.now();
    final dataInicioBrasil =
        dataInicio.isUtc ? BrazilTimeZone.utcToBrazil(dataInicio) : dataInicio;

    final diferenca = agora.difference(dataInicioBrasil);

    if (diferenca.inMinutes < 60) {
      return '${diferenca.inMinutes}min';
    } else {
      final horas = diferenca.inHours;
      final minutos = diferenca.inMinutes % 60;
      return '${horas}h${minutos > 0 ? ' ${minutos}min' : ''}';
    }
  }

  /// Formatar data de entrada na fila
  static String formatarDataEntrada(DateTime? dataEntrada) {
    if (dataEntrada == null) return 'Data não disponível';

    final agora = BrazilTimeZone.now();
    final dataEntradaBrasil = dataEntrada.isUtc
        ? BrazilTimeZone.utcToBrazil(dataEntrada)
        : dataEntrada;

    final diferenca = agora.difference(dataEntradaBrasil);

    if (diferenca.inDays == 0) {
      // Hoje - mostrar apenas horário
      return 'Hoje às ${BrazilDateFormat.formatTime(dataEntradaBrasil)}';
    } else if (diferenca.inDays == 1) {
      // Ontem
      return 'Ontem às ${BrazilDateFormat.formatTime(dataEntradaBrasil)}';
    } else {
      // Outros dias
      return BrazilDateFormat.formatDateTime(dataEntradaBrasil);
    }
  }

  /// Verificar se uma data é de hoje
  static bool isToday(DateTime? date) {
    return BrazilTimeZone.isToday(date ?? DateTime.now());
  }
}
