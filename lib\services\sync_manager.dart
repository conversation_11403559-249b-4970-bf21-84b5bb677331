import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import '../conexao.dart';
import '../utils/date_utils.dart';
import 'intelligent_cache_service.dart';

/// 🔄 GERENCIADOR DE SINCRONIZAÇÃO AVANÇADO
/// Usa webhooks e batch operations para otimizar a comunicação com Back4App
class SyncManager extends GetxController {
  static const String _tag = '[SYNC_MANAGER]';

  // Controladores de estado
  final _isSyncing = false.obs;
  final _lastSyncTime = Rxn<DateTime>();
  final _syncQueue = <Map<String, dynamic>>[].obs;
  final _pendingUpdates = <String, Map<String, dynamic>>{}.obs;

  // Timers para sincronização automática
  Timer? _autoSyncTimer;
  Timer? _batchProcessTimer;

  // Configurações
  static const int _batchSize = 10;
  static const int _batchDelayMs = 2000; // 2 segundos para agrupar operações
  static const int _autoSyncIntervalMs = 30000; // 30 segundos

  // Getters reativos
  bool get isSyncing => _isSyncing.value;
  DateTime? get lastSyncTime => _lastSyncTime.value;
  int get queueSize => _syncQueue.length;
  int get pendingUpdatesCount => _pendingUpdates.length;

  @override
  void onInit() {
    super.onInit();
    _initializeSyncManager();
  }

  @override
  void onClose() {
    _autoSyncTimer?.cancel();
    _batchProcessTimer?.cancel();
    super.onClose();
  }

  /// 🚀 INICIALIZAÇÃO DO GERENCIADOR
  void _initializeSyncManager() {
    debugPrint('$_tag Inicializando gerenciador de sincronização');

    // Configurar sincronização automática
    _autoSyncTimer = Timer.periodic(
      const Duration(milliseconds: _autoSyncIntervalMs),
      (_) => _performAutoSync(),
    );

    // Configurar processamento em lote
    _batchProcessTimer = Timer.periodic(
      const Duration(milliseconds: _batchDelayMs),
      (_) => _processBatchUpdates(),
    );

    debugPrint(
        '$_tag Gerenciador inicializado - Auto sync: ${_autoSyncIntervalMs}ms, Batch: ${_batchDelayMs}ms');
  }

  /// 📊 SINCRONIZAÇÃO INTELIGENTE DE FILAS
  Future<Map<String, dynamic>?> syncFilasInteligente({
    required String consultorioId,
    bool includeMessages = false,
    bool forceSync = false,
  }) async {
    try {
      if (_isSyncing.value && !forceSync) {
        debugPrint('$_tag Sincronização já em andamento, aguardando...');
        return null;
      }

      _isSyncing.value = true;

      final lastSync = _lastSyncTime.value?.toIso8601String();

      debugPrint(
          '$_tag Iniciando sincronização inteligente - Consultório: $consultorioId');
      debugPrint('$_tag Último sync: ${lastSync ?? "primeiro sync"}');

      // ✅ DESABILITAR smartFilaSync - função não existe no servidor
      debugPrint(
          '$_tag ⚠️ smartFilaSync desabilitada (não existe no servidor)');

      // Implementar sincronização local simples
      try {
        // Buscar dados diretamente sem função cloud
        final filaQuery = QueryBuilder<ParseObject>(ParseObject('Fila'))
          ..whereEqualTo('consultorio.objectId', consultorioId)
          ..includeObject(['medico', 'consultorio']);

        final filaResponse = await filaQuery.query();

        if (filaResponse.success && filaResponse.results != null) {
          debugPrint(
              '$_tag ✅ Sincronização local - ${filaResponse.results!.length} filas encontradas');

          // Atualizar timestamp de sincronização
          _lastSyncTime.value = TimezoneUtils.getNowInDeviceTimezone();

          return {
            'dados': {
              'filas': filaResponse.results!.map((f) => f.toJson()).toList(),
              'mensagens': [],
              'sync_info': {
                'next_sync_recommended': 30000, // 30 segundos
                'total_changes': filaResponse.results!.length,
              }
            }
          };
        } else {
          debugPrint(
              '$_tag ❌ Erro na sincronização local: ${filaResponse.error?.message}');
          return null;
        }
      } catch (e) {
        debugPrint('$_tag ❌ Erro na sincronização local: $e');
        return null;
      }
    } catch (e) {
      debugPrint('$_tag ❌ Exceção na sincronização: $e');
      return null;
    } finally {
      _isSyncing.value = false;
    }
  }

  /// 📦 PROCESSAMENTO EM LOTE DE ATUALIZAÇÕES
  Future<void> _processBatchUpdates() async {
    if (_pendingUpdates.isEmpty) return;

    try {
      final updates = _pendingUpdates.values.toList();
      if (updates.length < 3 &&
          _pendingUpdates.values.first['timestamp'] != null) {
        // Aguardar mais atualizações se tiver poucas e for recente
        final firstUpdateTime =
            DateTime.parse(_pendingUpdates.values.first['timestamp']);
        final elapsed =
            DateTime.now().difference(firstUpdateTime).inMilliseconds;

        if (elapsed < _batchDelayMs) {
          return; // Aguardar mais atualizações
        }
      }

      debugPrint('$_tag Processando ${updates.length} atualizações em lote');

      // Preparar dados para o webhook de batch
      final batchUpdates = updates
          .map((update) => {
                'className': update['className'],
                'objectId': update['objectId'],
                'data': update['data'],
                'action': update['action'] ?? 'update',
              })
          .toList();

      // Executar webhook de batch
      final cloudFunction = ParseCloudFunction('processBatchUpdates');
      final response = await Conexao.throttledExecuteCloudFunction(
        cloudFunction,
        parameters: {
          'updates': batchUpdates,
          'priority': 'normal',
        },
      );

      if (response.success) {
        final result = response.result;
        debugPrint(
            '$_tag ✅ Batch processado - Sucesso: ${result['successful']}, Falhas: ${result['failed']}');

        // Limpar atualizações processadas
        _pendingUpdates.clear();
      } else {
        debugPrint('$_tag ❌ Erro no batch: ${response.error?.message}');
      }
    } catch (e) {
      debugPrint('$_tag ❌ Exceção no processamento em lote: $e');
    }
  }

  /// ➕ ADICIONAR ATUALIZAÇÃO À FILA
  void addPendingUpdate({
    required String className,
    required String objectId,
    required Map<String, dynamic> data,
    String action = 'update',
  }) {
    final key = '${className}_$objectId';

    _pendingUpdates[key] = {
      'className': className,
      'objectId': objectId,
      'data': data,
      'action': action,
      'timestamp': DateTime.now().toIso8601String(),
    };

    debugPrint(
        '$_tag Adicionada atualização pendente: $key (${_pendingUpdates.length} total)');

    // Processar imediatamente se atingir o tamanho do lote
    if (_pendingUpdates.length >= _batchSize) {
      _processBatchUpdates();
    }
  }

  /// 🔄 SINCRONIZAÇÃO AUTOMÁTICA
  Future<void> _performAutoSync() async {
    if (_isSyncing.value) return;

    try {
      // Buscar configurações ativas do usuário
      final consultorioId = _getCurrentConsultorioId();
      if (consultorioId == null) return;

      debugPrint('$_tag Executando sincronização automática');

      await syncFilasInteligente(
        consultorioId: consultorioId,
        includeMessages: false, // Mensagens apenas sob demanda
      );
    } catch (e) {
      debugPrint('$_tag Erro na sincronização automática: $e');
    }
  }

  /// 🗄️ ATUALIZAR CACHE LOCAL
  Future<void> _updateLocalCache(
      Map<String, dynamic> dados, String consultorioId) async {
        try {
      final cacheService = Get.find<IntelligentCacheService>();

      // Cache de filas
      if (dados['filas'] != null) {
        for (final filaData in dados['filas']) {
          final filaId = filaData['objectId'];
          await cacheService.cacheFilaData(filaId, filaData);
        }
      }

      // Cache de mensagens
      if (dados['mensagens'] != null) {
        // Implementar cache de mensagens aqui se necessário
        debugPrint(
            '$_tag Cache de mensagens atualizado: ${dados['mensagens'].length}');
              }

      // Cache de estatísticas
      if (dados['estatisticas'] != null) {
        await cacheService.cacheFilaData(
            'stats_$consultorioId', dados['estatisticas']);
              }

      debugPrint('$_tag Cache local atualizado com sucesso');
        } catch (e) {
      debugPrint('$_tag Erro ao atualizar cache: $e');
        }
      }

  /// 🏥 OBTER ID DO CONSULTÓRIO ATUAL
  String? _getCurrentConsultorioId() {
    // Implementar lógica para obter o consultório atual do usuário
    // Por exemplo, do AuthService ou SessionManager
    try {
      // Esta é uma implementação de exemplo
      // Você deve adaptar conforme sua lógica de negócio
      return 'consultorio_ativo'; // Placeholder
    } catch (e) {
      debugPrint('$_tag Erro ao obter consultório atual: $e');
      return null;
    }
  }

  /// 🧹 INVALIDAR CACHE DISTRIBUÍDO
  Future<void> invalidateCache(List<String> keys) async {
    try {
      debugPrint('$_tag Invalidando cache para: ${keys.join(", ")}');

      final cloudFunction = ParseCloudFunction('cacheOptimization');
      await Conexao.throttledExecuteCloudFunction(
        cloudFunction,
        parameters: {
          'action': 'invalidate',
          'keys': keys,
        },
      );

      // Também invalidar cache local
      try {
        final cacheService = Get.find<IntelligentCacheService>();
        for (final key in keys) {
          if (key.startsWith('fila_')) {
            // Implementar invalidação local específica
            debugPrint('$_tag Invalidando cache local para: $key');
          }
        }
      } catch (e) {
        debugPrint('$_tag Cache service não disponível: $e');
      }
    } catch (e) {
      debugPrint('$_tag Erro ao invalidar cache: $e');
    }
  }

  /// 🔥 PRÉ-AQUECER CACHE
  Future<void> warmCache(List<String> keys, {int ttl = 300}) async {
    try {
      debugPrint('$_tag Pré-aquecendo cache para: ${keys.join(", ")}');

      final cloudFunction = ParseCloudFunction('cacheOptimization');
      await Conexao.throttledExecuteCloudFunction(
        cloudFunction,
        parameters: {
          'action': 'warm',
          'keys': keys,
          'ttl': ttl,
        },
      );
    } catch (e) {
      debugPrint('$_tag Erro ao pré-aquecer cache: $e');
    }
  }

  /// 📈 OBTER MÉTRICAS DE SINCRONIZAÇÃO
  Map<String, dynamic> getSyncMetrics() {
    return {
      'is_syncing': _isSyncing.value,
      'last_sync': _lastSyncTime.value?.toIso8601String(),
      'queue_size': _syncQueue.length,
      'pending_updates': _pendingUpdates.length,
      'auto_sync_enabled': _autoSyncTimer?.isActive ?? false,
      'batch_processing_enabled': _batchProcessTimer?.isActive ?? false,
    };
  }

  /// 🔄 FORÇAR SINCRONIZAÇÃO COMPLETA
  Future<Map<String, dynamic>?> forceSyncAll(String consultorioId) async {
    debugPrint('$_tag Forçando sincronização completa');

    // Limpar cache local primeiro
    await invalidateCache(['fila_*', 'stats_*', 'messages_*']);

    // Resetar timestamp para sync completo
    _lastSyncTime.value = null;

    return await syncFilasInteligente(
      consultorioId: consultorioId,
      includeMessages: true,
      forceSync: true,
    );
  }
}
