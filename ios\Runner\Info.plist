<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Saúde Sem Espera</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>fila_app</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>br.com.saudesemespera</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>saudesemespera</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>FirebaseAppDelegateProxyEnabled</key>
	<false/>
	<key>GMSApiKey</key>
	<string>AIzaSyD0JK2KYn6QCmHvE-EwuHqOe3NwM3BiiFc</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>MinimumOSVersion</key>
	<string>15.0</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>Este app não precisa de acesso ao Apple Music.</string>
	<key>NSCalendarsUsageDescription</key>
	<string>Este app não precisa de acesso ao seu calendário.</string>
	<key>NSCameraUsageDescription</key>
	<string>Este app precisa de acesso à câmera para ler QR codes e capturar imagens.</string>
	<key>NSContactsUsageDescription</key>
	<string>Este app não precisa de acesso aos seus contatos.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Este app precisa de acesso à sua localização para verificar se você está próximo da clínica e fornecer informações de localização precisas.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Este app precisa de acesso à sua localização para verificar se você está próximo da clínica e fornecer informações de localização precisas.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Este app precisa de acesso à sua localização para verificar se você está próximo da clínica e fornecer informações de localização precisas.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Este app não precisa de acesso ao seu microfone.</string>
	<key>NSMotionUsageDescription</key>
	<string>Este app não precisa de acesso aos seus dados de movimento.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Este app precisa de permissão para salvar imagens na sua galeria de fotos.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Este app precisa de acesso à sua galeria de fotos para permitir seleção de imagens.</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>Este app não precisa de reconhecimento de fala.</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
		<string>background-app-refresh</string>
		<string>location</string>
		<string>processing</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UIStatusBarHidden</key>
	<false/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
</dict>
</plist>
