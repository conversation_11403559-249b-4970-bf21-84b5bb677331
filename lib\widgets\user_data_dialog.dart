// lib/widgets/user_data_dialog.dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fila_app/controllers/user_data_controller.dart';
import 'package:phone_form_field/phone_form_field.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';

class UserDataDialog extends StatefulWidget {
  final UserDataController controller;
  final VoidCallback onComplete;
  final bool isEditing;

  const UserDataDialog({
    super.key,
    required this.controller,
    required this.onComplete,
    this.isEditing = false,
  });

  @override
  State<UserDataDialog> createState() => _UserDataDialogState();
}

class _UserDataDialogState extends State<UserDataDialog> {
  final _formKey = GlobalKey<FormState>();
  late PhoneController _phoneController;
  bool _isUIReady = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    PhoneNumber? initialPhone;
    if (widget.controller.telefoneController.text.isNotEmpty) {
      try {
        initialPhone =
            PhoneNumber.parse(widget.controller.telefoneController.text);
      } catch (e) {
        initialPhone = null;
      }
    }

    _phoneController = PhoneController(
      initialValue: initialPhone ?? PhoneNumber.parse('+55'),
    );

    // Use a microtask to delay the initialization
    Future.microtask(() {
      if (mounted) {
        setState(() {
          _isUIReady = true;
        });
      }
    });
  }

  @override
  void dispose() {
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bottomInset = MediaQuery.of(context).viewInsets.bottom;

    return WillPopScope(
      onWillPop: () async => widget.isEditing, // Prevents back on initial setup
      child: Dialog(
        insetPadding: const EdgeInsets.symmetric(horizontal: 16),
        backgroundColor: Colors.transparent,
        child: Container(
          constraints: const BoxConstraints(maxWidth: 400),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 10,
                spreadRadius: 1,
              ),
            ],
          ),
          child: Form(
            key: _formKey,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    widget.isEditing ? 'Editar Dados' : 'Bem-vindo!',
                    style: const TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.teal,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    widget.isEditing
                        ? 'Atualize suas informações:'
                        : 'Para uma melhor experiência, precisamos de algumas informações básicas:',
                    style: const TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 16,
                      color: Colors.black87,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  _buildNameField(),
                  const SizedBox(height: 20),
                  _isUIReady
                      ? _buildPhoneField()
                      : const SizedBox(
                          height: 60,
                          child: Center(
                            child: CircularProgressIndicator(
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.teal),
                              strokeWidth: 2,
                            ),
                          ),
                        ),
                  const SizedBox(height: 24),
                  _buildSubmitButton(),
                  if (!widget.isEditing) ...[
                    const SizedBox(height: 16),
                    _buildRecoveryButton(),
                  ],
                  if (bottomInset == 0)
                    Padding(
                      padding: const EdgeInsets.only(top: 16),
                      child: _buildInfoSection(),
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNameField() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: widget.controller.nomeController,
        decoration: InputDecoration(
          labelText: 'Nome completo',
          hintText: 'Digite seu nome completo',
          prefixIcon: const Icon(
            Icons.person_outline,
            color: Colors.teal,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.teal.shade200),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.teal.shade200),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Colors.teal, width: 2),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.red.shade300),
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding:
              const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
          helperText: 'Nome que aparecerá para o médico e secretária',
          helperStyle: TextStyle(fontSize: 12, color: Colors.grey.shade600),
        ),
        validator: widget.controller.validateNome,
        textCapitalization: TextCapitalization.words,
        autovalidateMode: widget.isEditing
            ? AutovalidateMode.disabled
            : AutovalidateMode.onUserInteraction,
        style: const TextStyle(
          fontFamily: 'Georgia',
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildPhoneField() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: Colors.teal.shade200),
      ),
      child: PhoneFormField(
        controller: _phoneController,
        decoration: InputDecoration(
          labelText: 'Telefone (WhatsApp)',
          hintText: '(11) 99999-9999',
          hintStyle: TextStyle(
            color: Colors.grey.shade400,
            fontFamily: 'Georgia',
            fontSize: 15,
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          prefixIcon: const Icon(Icons.phone_outlined, color: Colors.teal),
          helperText: 'Telefone para contato em caso de emergências',
          helperStyle: TextStyle(fontSize: 12, color: Colors.grey.shade600),
        ),
        validator: (phoneNumber) {
          if (widget.isEditing &&
              widget.controller.telefoneController.text.isNotEmpty) {
            final existingText = widget.controller.telefoneController.text;
            if (phoneNumber?.international == existingText) {
              return null;
            }
          }

          if (phoneNumber == null || !phoneNumber.isValid()) {
            return 'Por favor, digite um telefone válido';
          }
          return null;
        },
        onChanged: (phoneNumber) {
          if (phoneNumber != null) {
            widget.controller.telefoneController.text =
                phoneNumber.international;
          }
        },
        autovalidateMode: widget.isEditing
            ? AutovalidateMode.disabled
            : AutovalidateMode.onUserInteraction,
        countrySelectorNavigator: const CountrySelectorNavigator.dialog(),
        isCountryButtonPersistent: true,
        countryButtonStyle: const CountryButtonStyle(
          showFlag: true,
          showDialCode: true,
          showIsoCode: false,
        ),
      ),
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      height: 54,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          backgroundColor: Colors.teal,
          elevation: 2,
          shadowColor: Colors.teal.withOpacity(0.3),
        ),
        onPressed: _isLoading ? null : () => _handleSubmit(context),
        child: _isLoading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  strokeWidth: 2,
                ),
              )
            : Text(
                widget.isEditing ? 'Atualizar' : 'Confirmar',
                style: const TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
      ),
    );
  }

  Widget _buildRecoveryButton() {
    return SizedBox(
      width: double.infinity,
      height: 48,
      child: OutlinedButton.icon(
        style: OutlinedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          side: BorderSide(color: Colors.teal.shade300),
          backgroundColor: Colors.teal.shade50,
        ),
        onPressed: _isLoading ? null : _handleRecovery,
        icon: Icon(
          Icons.restore,
          color: Colors.teal.shade700,
          size: 20,
        ),
        label: Text(
          'Já tenho conta - Recuperar dados',
          style: TextStyle(
            fontFamily: 'Georgia',
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.teal.shade700,
          ),
        ),
      ),
    );
  }

  Widget _buildInfoSection() {
    return Column(
      children: [
        const Divider(color: Colors.teal),
        const SizedBox(height: 10),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(
              Icons.info_outline,
              size: 18,
              color: Colors.teal.shade700,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Estas informações serão usadas para identificar você em todas as filas de atendimento.',
                style: TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 13,
                  color: Colors.grey.shade700,
                ),
              ),
            ),
          ],
        ),
        if (!widget.isEditing) ...[
          const SizedBox(height: 8),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                Icons.check_circle_outline,
                size: 18,
                color: Colors.green.shade700,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Você não precisará inserir esses dados novamente ao entrar em outras filas.',
                  style: TextStyle(
                    fontFamily: 'Georgia',
                    fontSize: 13,
                    color: Colors.grey.shade700,
                  ),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Future<void> _handleSubmit(BuildContext context) async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    final completePhoneNumber = _phoneController.value?.international ?? '';

    widget.controller.telefoneController.text = completePhoneNumber;

    try {
      await widget.controller.saveUserData(
        widget.controller.nomeController.text.trim(),
        completePhoneNumber.trim(),
        isEditing: widget.isEditing,
      );
      if (mounted) {
        Navigator.of(context).pop();
        widget.onComplete();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao salvar dados: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _handleRecovery() async {
    // Solicitar telefone ao usuário
    final phoneController = TextEditingController();

    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'Recuperar dados',
          style: TextStyle(
            fontFamily: 'Georgia',
            color: Colors.teal,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Digite o telefone da sua conta anterior:',
              style: TextStyle(fontFamily: 'Georgia'),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: phoneController,
              decoration: InputDecoration(
                labelText: 'Telefone',
                hintText: '(11) 99999-9999',
                prefixIcon: const Icon(Icons.phone, color: Colors.teal),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Colors.teal, width: 2),
                ),
              ),
              keyboardType: TextInputType.phone,
              autofocus: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancelar',
              style: TextStyle(fontFamily: 'Georgia'),
            ),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(backgroundColor: Colors.teal),
            onPressed: () => Navigator.pop(context, phoneController.text),
            child: const Text(
              'Recuperar',
              style: TextStyle(
                fontFamily: 'Georgia',
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );

    if (result != null && result.isNotEmpty) {
      setState(() => _isLoading = true);

      try {
        final success = await widget.controller.recoverUserDataByPhone(result);

        if (mounted) {
          if (success) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('✅ Dados recuperados com sucesso!'),
                backgroundColor: Colors.green,
              ),
            );
            Navigator.of(context).pop();
            widget.onComplete();
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('❌ Nenhuma conta encontrada com este telefone'),
                backgroundColor: Colors.orange,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('❌ Erro ao recuperar dados: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    }
  }
}
