# ⚡ Configuração Rápida do Java para o Projeto

## 🎯 Para o seu projeto atual (você já tem Java 24)

### 1. Configurar JAVA_HOME (Execute no PowerShell como Administrador):

```powershell
# Encontrar onde o Java está instalado
$javaPath = (Get-Command java).Source | Split-Path | Split-Path

# Definir JAVA_HOME
[Environment]::SetEnvironmentVariable("JAVA_HOME", $javaPath, "Machine")

# Verificar
echo $env:JAVA_HOME
```

### 2. Atualizar android/gradle.properties

Adicione estas linhas no final do arquivo `android/gradle.properties`:

```properties
# Configuração Java 24 (ajuste o caminho conforme necessário)
org.gradle.java.home=C:\\Program Files\\Java\\jdk-24

# Suprimir warnings de versões obsoletas
org.gradle.jvmargs=-Xmx3072m -XX:MaxMetaspaceSize=768m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -XX:+UseG1GC -XX:+ExplicitGCInvokesConcurrent --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED -Xlint:-options
```

### 3. Verificar configuração:

```cmd
java -version
echo %JAVA_HOME%
```

## 📋 Para a equipe (Guia completo)

### Passo 1: Download do JDK
- **Recomendado**: OpenJDK 21 LTS de https://adoptium.net/
- Baixar: **Windows x64 Installer (.msi)**

### Passo 2: Instalação
1. Execute o arquivo .msi
2. **IMPORTANTE**: Marque "Set JAVA_HOME variable"
3. **IMPORTANTE**: Marque "Add to PATH"

### Passo 3: Configuração Manual (se necessário)

#### Método Gráfico:
1. `Win + R` → digite `sysdm.cpl`
2. Aba "Avançado" → "Variáveis de Ambiente"
3. **Variáveis do sistema** → "Novo":
   - Nome: `JAVA_HOME`
   - Valor: `C:\Program Files\Eclipse Adoptium\jdk-21.0.x-hotspot`
4. Editar "Path" → "Novo" → `%JAVA_HOME%\bin`

#### Método PowerShell (como Admin):
```powershell
# Definir JAVA_HOME (ajustar caminho)
[Environment]::SetEnvironmentVariable("JAVA_HOME", "C:\Program Files\Eclipse Adoptium\jdk-21.0.5-hotspot", "Machine")

# Adicionar ao PATH
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
$newPath = "%JAVA_HOME%\bin;" + $currentPath
[Environment]::SetEnvironmentVariable("PATH", $newPath, "Machine")
```

### Passo 4: Verificação
```cmd
java -version
javac -version
echo %JAVA_HOME%
```

### Passo 5: Configurar projeto Flutter
No arquivo `android/gradle.properties`:
```properties
org.gradle.java.home=C:\\Program Files\\Eclipse Adoptium\\jdk-21.0.5-hotspot
```

## 🚨 Solução de Problemas Comuns

### "java não é reconhecido"
1. Verificar se PATH contém `%JAVA_HOME%\bin`
2. Reiniciar terminal
3. Reiniciar computador se necessário

### Múltiplas versões do Java
1. Desinstalar versões antigas (Painel de Controle)
2. Limpar PATH de entradas antigas
3. Configurar apenas a versão desejada

### Android Studio não reconhece JDK
1. File → Settings → Build → Build Tools → Gradle
2. Gradle JDK → selecionar JDK correto
3. Reiniciar Android Studio

## 📱 Script Automático

Use o arquivo `setup-java.ps1` incluído:

```powershell
# Execute como Administrador
.\setup-java.ps1

# Para versão específica
.\setup-java.ps1 -JavaVersion 21
```

## ✅ Checklist Final

- [ ] Java instalado (JDK 17, 21 ou superior)
- [ ] JAVA_HOME configurado
- [ ] PATH atualizado
- [ ] `java -version` funciona
- [ ] `javac -version` funciona
- [ ] android/gradle.properties atualizado
- [ ] Android Studio configurado
- [ ] Terminal reiniciado

## 🔗 Links Úteis

- **OpenJDK (Gratuito)**: https://adoptium.net/
- **Oracle JDK**: https://www.oracle.com/java/technologies/downloads/
- **Verificar versão online**: https://www.java.com/verify/

---

**💡 Dica**: Use sempre versões LTS (Long Term Support) para projetos em produção:
- **JDK 17 LTS** (suporte até 2029)
- **JDK 21 LTS** (suporte até 2031)
