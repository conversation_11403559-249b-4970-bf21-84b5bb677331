import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:fila_app/views/gradient_background.dart';
import 'package:fila_app/controllers/cadastro_secretaria_controller.dart';
import 'package:fila_app/controllers/login_controller.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:fila_app/widgets/back_arrow_widget.dart';
// Importe o pacote para formatação de texto
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import '../utils/date_utils.dart';

class TelaCadastroSecretaria extends StatefulWidget {
  const TelaCadastroSecretaria({super.key});

  @override
  State<TelaCadastroSecretaria> createState() => _TelaCadastroSecretariaState();
}

class _TelaCadastroSecretariaState extends State<TelaCadastroSecretaria>
    with SingleTickerProviderStateMixin {
  final CadastroSecretariaController controller =
      Get.put(CadastroSecretariaController());
  final LoginController loginController = Get.put(LoginController());
  late TabController _tabController;
  bool _senhaVisivel = false;

  // Criando máscaras para CPF e telefone
  final cpfFormatter = MaskTextInputFormatter(
      mask: '###.###.###-##', filter: {"#": RegExp(r'[0-9]')});

  final telefoneFormatter = MaskTextInputFormatter(
      mask: '(##) #####-####', filter: {"#": RegExp(r'[0-9]')});

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(_handleTabChange);
    controller.carregarDadosHospital();
    controller.carregarSecretarias();
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      setState(() {
        // Isso fará o widget reconstruir quando a aba mudar
      });
    }
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  void _mostrarSnackBar(String mensagem, {bool sucesso = true}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(mensagem),
        backgroundColor: sucesso ? Colors.green : Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  Future<void> _cadastrarSecretaria() async {
    // Validação completa dos campos
    String? errorMsg;

    if (controller.nomeController.text.trim().isEmpty) {
      errorMsg = 'O nome é obrigatório';
    } else if (controller.emailController.text.trim().isEmpty) {
      errorMsg = 'O email é obrigatório';
    } else if (!GetUtils.isEmail(controller.emailController.text.trim())) {
      errorMsg = 'Digite um email válido';
    } else if (controller.senhaController.text.trim().isEmpty) {
      errorMsg = 'A senha é obrigatória';
    } else if (controller.cpfController.text.trim().isEmpty) {
      errorMsg = 'O CPF é obrigatório';
    }

    if (errorMsg != null) {
      _mostrarSnackBar(errorMsg, sucesso: false);
      return;
    }

    try {
      final result = await controller.cadastrarSecretaria();

      if (result) {
        _mostrarSnackBar('Secretário(a) cadastrado(a) com sucesso');
        _tabController.animateTo(0); // Volta para a aba de listagem
      } else {
        _mostrarSnackBar('Erro: ${controller.error.value}', sucesso: false);

        // Mostrar alerta mais detalhado
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Erro no cadastro'),
            content: Text('Detalhes do erro: ${controller.error.value}'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      _mostrarSnackBar('Erro inesperado: $e', sucesso: false);
    }
  }

  Future<void> _reenviarCredenciais(String secretariaId) async {
    try {
      final result = await controller.reenviarCredenciais(secretariaId);

      if (result) {
        _mostrarSnackBar('Credenciais reenviadas com sucesso');
      } else {
        _mostrarSnackBar(
            'Erro ao reenviar credenciais: ${controller.error.value}',
            sucesso: false);
      }
    } catch (e) {
      _mostrarSnackBar('Erro ao reenviar credenciais: $e', sucesso: false);
    }
  }

  Future<void> _alterarStatusSecretaria(
      String secretariaId, bool novoStatus) async {
    final result =
        await controller.atualizarStatusSecretaria(secretariaId, novoStatus);

    if (result) {
      _mostrarSnackBar(novoStatus
          ? 'Secretário(a) ativado(a) com sucesso'
          : 'Secretário(a) desativado(a) com sucesso');
    } else {
      _mostrarSnackBar('Erro ao alterar status: ${controller.error.value}',
          sucesso: false);
    }
  }

  Future<void> _confirmarRemocao(String secretariaId, String nome) async {
    final confirmacao = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remover Secretário(a)'),
        content:
            Text('Tem certeza que deseja remover o(a) secretário(a) $nome?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancelar'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Remover'),
          ),
        ],
      ),
    );

    if (confirmacao == true) {
      try {
        // Mostrar indicador de progresso
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return const Dialog(
              child: Padding(
                padding: EdgeInsets.all(20.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 20),
                    Text('Removendo secretário(a)...'),
                  ],
                ),
              ),
            );
          },
        );

        // Realizar a remoção
        final result = await controller.removerSecretaria(secretariaId);

        // Fechar o diálogo de progresso
        if (context.mounted) Navigator.of(context).pop();

        if (result) {
          _mostrarSnackBar('Secretário(a) removido(a) com sucesso');
          // Atualizar a lista após a remoção bem-sucedida
          await controller.carregarSecretarias();
        } else {
          _mostrarSnackBar(
              'Erro ao remover secretário(a): ${controller.error.value}',
              sucesso: false);
        }
      } catch (e) {
        // Garantir que o diálogo seja fechado em caso de erro
        if (context.mounted && Navigator.of(context).canPop()) {
          Navigator.of(context).pop();
        }
        _mostrarSnackBar('Erro inesperado: $e', sucesso: false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: const BackArrowWidget(
          iconColor: Colors.black,
        ),
        title: const FittedBox(
          fit: BoxFit.scaleDown,
          child: Text(
            'Gerenciamento de Secretários(as)',
            style: TextStyle(fontFamily: 'Georgia'),
          ),
        ),
        backgroundColor: const Color(0xFF34ECCB),
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(
              icon: Icon(Icons.list_alt),
              text: 'Secretários(as)',
            ),
            Tab(
              icon: Icon(Icons.person_add),
              text: 'Novo Cadastro',
            ),
          ],
        ),
      ),
      body: GradientBackground(
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildListaSecretarias(),
            _buildFormularioCadastro(),
          ],
        ),
      ),
      floatingActionButton: _tabController.index == 0
          ? FloatingActionButton(
              onPressed: controller.carregarSecretarias,
              backgroundColor: const Color(0xFF34ECCB),
              child: const Icon(Icons.refresh, color: Colors.black),
            )
          : null,
    );
  }

  Widget _buildListaSecretarias() {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text(
                'Carregando secretários(as)...',
                style: TextStyle(fontFamily: 'Georgia'),
              ),
            ],
          ),
        );
      }

      if (controller.secretarias.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.person_outline, size: 70, color: Colors.grey),
              const SizedBox(height: 16),
              const Text(
                'Nenhum(a) secretário(a) cadastrado(a)',
                style: TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 18,
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: controller.carregarSecretarias,
                icon: const Icon(Icons.refresh),
                label: const Text('Atualizar'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF34ECCB),
                ),
              ),
            ],
          ),
        );
      }

      return RefreshIndicator(
        onRefresh: controller.carregarSecretarias,
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: controller.secretarias.length,
          itemBuilder: (context, index) {
            final secretaria = controller.secretarias[index];
            return _buildSecretariaCard(secretaria);
          },
        ),
      );
    });
  }

  Widget _buildSecretariaCard(ParseObject secretaria) {
    final nome = secretaria.get<String>('nome') ?? 'Secretário(a) sem nome';
    final user = secretaria.get<ParseUser>('user_secretaria');
    final email = secretaria.get<String>('email') ??
        user?.emailAddress ??
        user?.get<String>('email') ??
        user?.username ??
        'Email não disponível';
    final telefone =
        secretaria.get<String>('telefone') ?? 'Telefone não disponível';
    final ativo = secretaria.get<bool>('ativo') ?? true;
    final dataCadastro =
        secretaria.get<DateTime>('dataCadastro') ?? secretaria.createdAt;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: 4,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Cabeçalho do card com status
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: ativo ? const Color(0xFF34ECCB) : Colors.grey,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                const Icon(Icons.person, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    nome,
                    style: const TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: ativo ? Colors.green : Colors.red,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    ativo ? 'Ativo' : 'Inativo',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Conteúdo principal
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Informações de contato
                _buildInfoRow(Icons.email, 'Email', email),
                const SizedBox(height: 8),
                _buildInfoRow(Icons.phone, 'Telefone', telefone),
                const SizedBox(height: 8),
                if (dataCadastro != null)
                  _buildInfoRow(Icons.calendar_today, 'Cadastrado em',
                      _formatarData(dataCadastro)),

                const Divider(height: 24),

                // Botões de ação
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // Botão de reenviar credenciais
                    _buildActionButton(
                      onPressed: () =>
                          _reenviarCredenciais(secretaria.objectId!),
                      icon: Icons.password,
                      label: 'Credenciais',
                      color: Colors.blue,
                    ),

                    // Botão de mudar status
                    _buildActionButton(
                      onPressed: () => _alterarStatusSecretaria(
                          secretaria.objectId!, !ativo),
                      icon: ativo ? Icons.toggle_off : Icons.toggle_on,
                      label: ativo ? 'Desativar' : 'Ativar',
                      color: ativo ? Colors.orange : Colors.green,
                    ),

                    // Botão de remover
                    _buildActionButton(
                      onPressed: () =>
                          _confirmarRemocao(secretaria.objectId!, nome),
                      icon: Icons.delete,
                      label: 'Remover',
                      color: Colors.red,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required VoidCallback onPressed,
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4),
        child: ElevatedButton.icon(
          onPressed: onPressed,
          icon: Icon(icon, size: 16),
          label: Text(
            label,
            style: const TextStyle(fontSize: 12),
          ),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 8),
            backgroundColor: color,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFormularioCadastro() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Card(
            elevation: 4,
            margin: const EdgeInsets.only(bottom: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                // Cabeçalho do card
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: const BoxDecoration(
                    color: Color(0xFF34ECCB),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(12),
                      topRight: Radius.circular(12),
                    ),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.person_add, color: Colors.white),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Cadastro de Novo(a) Secretário(a)',
                          style: TextStyle(
                            fontFamily: 'Georgia',
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Conteúdo do formulário
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Dados Pessoais
                      _buildSectionTitle('Dados Pessoais'),
                      const SizedBox(height: 16),

                      // Nome da secretária
                      _buildTextField(
                        controller: controller.nomeController,
                        label: 'Nome',
                        hint: 'Digite o nome completo',
                        icon: Icons.person,
                        required: true,
                      ),
                      const SizedBox(height: 16),

                      // CPF com formatação
                      _buildTextField(
                        controller: controller.cpfController,
                        label: 'CPF',
                        hint: 'Digite o CPF',
                        icon: Icons.badge,
                        keyboardType: TextInputType.number,
                        inputFormatters: [cpfFormatter],
                        required: true,
                      ),
                      const SizedBox(height: 16),

                      // Telefone com formatação
                      _buildTextField(
                        controller: controller.telefoneController,
                        label: 'Telefone',
                        hint: 'Digite o número de telefone',
                        icon: Icons.phone,
                        keyboardType: TextInputType.phone,
                        inputFormatters: [telefoneFormatter],
                        required: false,
                      ),
                      const SizedBox(height: 24),

                      // Dados de Acesso
                      _buildSectionTitle('Dados de Acesso'),
                      const SizedBox(height: 16),

                      // Email
                      _buildTextField(
                        controller: controller.emailController,
                        label: 'Email',
                        hint: 'Digite o email',
                        icon: Icons.email,
                        keyboardType: TextInputType.emailAddress,
                        required: true,
                      ),
                      const SizedBox(height: 16),

                      // Senha com botão para gerar senha aleatória e olho para visualizar
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: TextField(
                              controller: controller.senhaController,
                              decoration: InputDecoration(
                                labelText: 'Senha Provisória *',
                                hintText: 'Senha para primeiro acesso',
                                prefixIcon: const Icon(Icons.password,
                                    color: Colors.teal),
                                suffixIcon: IconButton(
                                  icon: Icon(
                                    _senhaVisivel
                                        ? Icons.visibility
                                        : Icons.visibility_off,
                                    color: Colors.teal,
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      _senhaVisivel = !_senhaVisivel;
                                    });
                                  },
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide:
                                      const BorderSide(color: Colors.teal),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: const BorderSide(
                                      color: Colors.teal, width: 2),
                                ),
                                filled: true,
                                fillColor: Colors.white,
                                contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 12),
                              ),
                              obscureText: !_senhaVisivel,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Padding(
                            padding: const EdgeInsets.only(top: 8),
                            child: ElevatedButton.icon(
                              onPressed: controller.gerarSenhaPadrao,
                              icon: const Icon(Icons.refresh, size: 16),
                              label: const Text('Gerar',
                                  style: TextStyle(fontSize: 12)),
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 12),
                                backgroundColor: Colors.blue,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),

                      // Configurações
                      _buildSectionTitle('Configurações'),
                      const SizedBox(height: 12),

                      // Status de ativo/inativo
                      Card(
                        elevation: 0,
                        color: Colors.grey[100],
                        child: Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: Row(
                            children: [
                              const Icon(Icons.toggle_on_outlined,
                                  color: Colors.teal),
                              const SizedBox(width: 8),
                              const Text(
                                'Status ativo:',
                                style: TextStyle(
                                  fontFamily: 'Georgia',
                                  fontSize: 16,
                                ),
                              ),
                              const Spacer(),
                              Obx(() => Switch(
                                    value: controller.ativo.value,
                                    onChanged: (value) =>
                                        controller.ativo.value = value,
                                    activeColor: Colors.teal,
                                  )),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Botões de ação
          Row(
            children: [
              // Botão de limpar formulário
              Expanded(
                flex: 1,
                child: TextButton.icon(
                  onPressed: controller.limparFormulario,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Limpar'),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.teal,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                ),
              ),

              const SizedBox(width: 16),

              // Botão de cadastro
              Expanded(
                flex: 2,
                child: Obx(() => ElevatedButton.icon(
                      onPressed: controller.isLoading.value
                          ? null
                          : _cadastrarSecretaria,
                      icon: controller.isLoading.value
                          ? const SizedBox(
                              width: 24,
                              height: 24,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : const Icon(Icons.person_add),
                      label: const Text(
                        'Cadastrar Secretário(a)',
                        style: TextStyle(
                          fontFamily: 'Georgia',
                          fontSize: 16,
                        ),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF34ECCB),
                        foregroundColor: Colors.black87,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    )),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontFamily: 'Georgia',
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.teal,
          ),
        ),
        const Divider(color: Colors.teal, thickness: 1),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    bool obscureText = false,
    bool required = false,
  }) {
    return TextField(
      controller: controller,
      decoration: InputDecoration(
        labelText: required ? '$label *' : label,
        hintText: hint,
        prefixIcon: Icon(icon, color: Colors.teal),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.teal),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.teal, width: 2),
        ),
        filled: true,
        fillColor: Colors.white,
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      keyboardType: keyboardType,
      inputFormatters: inputFormatters,
      obscureText: obscureText,
    );
  }

  String _formatarData(DateTime data) {
    final brazilDate = BrazilTimeZone.parseObjectDateToBrazil(data);
    if (brazilDate == null) return 'Data inválida';
    return '${brazilDate.day.toString().padLeft(2, '0')}/'
        '${brazilDate.month.toString().padLeft(2, '0')}/'
        '${brazilDate.year}';
  }
}
