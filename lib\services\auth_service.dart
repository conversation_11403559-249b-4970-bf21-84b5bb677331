import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:flutter/foundation.dart' show debugPrint;
import 'package:get/get.dart';
import 'session_manager.dart';

class AuthService {
  /// Verificar usuário atual com suporte a SessionManager
  static Future<Map<String, dynamic>> checkCurrentUser() async {
    try {
      // Primeiro, tentar auto-login via SessionManager
      final sessionManager = Get.find<SessionManager>();
      final autoLoginResult = await sessionManager.attemptAutoLogin();

      if (autoLoginResult['success'] == true) {
        debugPrint('✅ Auto-login realizado via SessionManager');
        return autoLoginResult;
      }

      // Se auto-login falhou, verificar usuário atual do Parse
      final user = await ParseUser.currentUser() as ParseUser?;

      if (user == null) {
        debugPrint('❌ Nenhum usuário logado');
        return {'success': false, 'error': 'Nenhum usuário logado'};
      }

      // Verificar se o token ainda é válido
      final ParseResponse response = await user.getUpdatedUser();
      if (!response.success) {
        debugPrint('⚠️ Sessão expirada, fazendo logout...');
        await sessionManager.logout();
        return {'success': false, 'error': 'Sessão expirada'};
      }

      debugPrint('✅ Usuário já logado: ${user.objectId}');
      final tipo = user.get<String>('tipo');

      // Salvar sessão no SessionManager para futuras inicializações
      await sessionManager.saveSession(user: user, userType: tipo ?? 'unknown');

      return await _validateUserTypeAndGetRoute(user, tipo);
    } catch (e) {
      debugPrint('❌ Erro ao verificar usuário atual: $e');
      return {
        'success': false,
        'error': e.toString().replaceAll('Exception: ', '')
      };
    }
  }

  /// Validar tipo de usuário e retornar rota apropriada
  static Future<Map<String, dynamic>> _validateUserTypeAndGetRoute(
    ParseUser user,
    String? tipo,
  ) async {
    if (tipo == null) {
      throw Exception('Tipo de usuário não definido');
    }

    switch (tipo) {
      case 'medico':
        return await _handleMedicoValidation(user);
      case 'consultorio':
        return await _handleConsultorioValidation(user);
      case 'secretaria':
        return await _handleSecretariaValidation(user);
      case 'admin':
        return await _handleAdminValidation(user);
      default:
        throw Exception('Tipo de usuário inválido: $tipo');
    }
  }

  /// Validação específica para médicos
  static Future<Map<String, dynamic>> _handleMedicoValidation(
      ParseUser user) async {
    final queryMedico = QueryBuilder<ParseObject>(ParseObject('Medico'))
      ..whereEqualTo('user_medico', user.toPointer());

    final medicoResponse = await queryMedico.query();

    if (!medicoResponse.success ||
        medicoResponse.results == null ||
        medicoResponse.results!.isEmpty) {
      throw Exception('Perfil médico não encontrado');
    }

    final medico = medicoResponse.results!.first;
    final ativo = medico.get<bool>('ativo') ?? false;

    if (!ativo) {
      throw Exception('Conta desativada. Entre em contato com o suporte.');
    }

    return {
      'success': true,
      'user': user,
      'tipo': 'medico',
      'medico': medico,
      'route': '/medico'
    };
  }

  /// Validação específica para consultórios
  static Future<Map<String, dynamic>> _handleConsultorioValidation(
      ParseUser user) async {
    final queryConsultorio =
        QueryBuilder<ParseObject>(ParseObject('consultorio'))
          ..whereEqualTo('user_consultorio', user.toPointer());

    final consultorioResponse = await queryConsultorio.query();

    if (!consultorioResponse.success ||
        consultorioResponse.results == null ||
        consultorioResponse.results!.isEmpty) {
      throw Exception('Perfil de consultório não encontrado');
    }

    final consultorio = consultorioResponse.results!.first;
    final ativo = consultorio.get<bool>('ativo') ?? true;

    if (!ativo) {
      throw Exception(
          'Consultório desativado. Entre em contato com o suporte.');
    }

    return {
      'success': true,
      'user': user,
      'tipo': 'consultorio',
      'consultorio': consultorio,
      'route': '/hospital'
    };
  }

  /// Validação específica para secretárias
  static Future<Map<String, dynamic>> _handleSecretariaValidation(
      ParseUser user) async {
    final querySecretaria = QueryBuilder<ParseObject>(ParseObject('Secretaria'))
      ..whereEqualTo('user_secretaria', user.toPointer())
      ..includeObject(['consultorio']);

    final secretariaResponse = await querySecretaria.query();

    if (!secretariaResponse.success ||
        secretariaResponse.results == null ||
        secretariaResponse.results!.isEmpty) {
      throw Exception('Perfil de secretária não encontrado');
    }

    final secretaria = secretariaResponse.results!.first;
    final ativo = secretaria.get<bool>('ativo') ?? false;

    if (!ativo) {
      throw Exception('Conta desativada. Entre em contato com o suporte.');
    }

    // Verificar se o consultório também está ativo
    final consultorio = secretaria.get<ParseObject>('consultorio');
    if (consultorio != null) {
      final consultorioAtivo = consultorio.get<bool>('ativo') ?? true;
      if (!consultorioAtivo) {
        throw Exception(
            'Consultório desativado. Entre em contato com o suporte.');
      }
    }

    return {
      'success': true,
      'user': user,
      'tipo': 'secretaria',
      'secretaria': secretaria,
      'route': '/home_secretaria'
    };
  }

  /// Validação específica para administradores
  static Future<Map<String, dynamic>> _handleAdminValidation(
      ParseUser user) async {
    final isAdmin = user.get<bool>('isAdmin') ?? false;

    if (!isAdmin) {
      throw Exception('Este usuário não possui permissões de administrador');
    }

    return {
      'success': true,
      'user': user,
      'tipo': 'admin',
      'route': '/admin_hospitais'
    };
  }

  /// Login com SessionManager
  static Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    try {
      debugPrint('🔐 Iniciando login para: $email');

      final user = ParseUser(email, password, email);
      final response = await user.login();

      if (!response.success) {
        throw Exception(response.error?.message ?? 'Erro ao fazer login');
      }

      final userType = user.get<String>('tipo');
      if (userType == null) {
        throw Exception('Tipo de usuário não definido');
      }

      // Validar usuário e obter dados específicos
      final validationResult =
          await _validateUserTypeAndGetRoute(user, userType);

      if (validationResult['success']) {
        // Salvar sessão no SessionManager
        final sessionManager = Get.find<SessionManager>();
        await sessionManager.saveSession(
          user: user,
          userType: userType,
          additionalData: {
            'loginTime': DateTime.now().toIso8601String(),
            'loginMethod': 'email_password',
          },
        );

        // Atualizar atividade
        await sessionManager.updateActivity();

        debugPrint('✅ Login realizado com sucesso');
      }

      return validationResult;
    } catch (e) {
      debugPrint('❌ Erro no login: $e');
      return {
        'success': false,
        'error': e.toString().replaceAll('Exception: ', ''),
      };
    }
  }

  /// Logout com SessionManager
  static Future<void> logout() async {
    try {
      debugPrint('🚪 Iniciando logout...');

      final sessionManager = Get.find<SessionManager>();
      await sessionManager.logout();

      debugPrint('✅ Logout realizado com sucesso');
    } catch (e) {
      debugPrint('❌ Erro no logout: $e');
    }
  }

  /// Verificar se a sessão está ativa
  static bool isSessionActive() {
    try {
      final sessionManager = Get.find<SessionManager>();
      return sessionManager.isSessionActive.value;
    } catch (e) {
      return false;
    }
  }

  /// Obter informações do usuário atual
  static Map<String, dynamic>? getCurrentUserInfo() {
    try {
      final sessionManager = Get.find<SessionManager>();
      if (sessionManager.isSessionActive.value) {
        return sessionManager.sessionData.value;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Forçar refresh da sessão
  static Future<bool> refreshSession() async {
    try {
      final sessionManager = Get.find<SessionManager>();
      return await sessionManager.forceRefreshSession();
    } catch (e) {
      debugPrint('❌ Erro ao fazer refresh da sessão: $e');
      return false;
    }
  }

  /// Verificar se o auto-login está habilitado
  static bool isAutoLoginEnabled() {
    try {
      final sessionManager = Get.find<SessionManager>();
      return sessionManager.isAutoLoginEnabled.value;
    } catch (e) {
      return true;
    }
  }

  /// Configurar auto-login
  static Future<void> setAutoLoginEnabled(bool enabled) async {
    try {
      final sessionManager = Get.find<SessionManager>();
      await sessionManager.setAutoLoginEnabled(enabled);
    } catch (e) {
      debugPrint('❌ Erro ao configurar auto-login: $e');
    }
  }
}
