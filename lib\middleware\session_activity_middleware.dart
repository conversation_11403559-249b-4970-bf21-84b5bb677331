import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/session_manager.dart';

/// 🕐 Middleware para Atualização Automática de Atividade
///
/// Atualiza automaticamente o timestamp de última atividade
/// sempre que o usuário navega entre telas
class SessionActivityMiddleware extends GetMiddleware {
  @override
  int? get priority => 1;

  @override
  RouteSettings? redirect(String? route) {
    // Atualizar atividade sempre que houver navegação
    _updateActivity();
    return null;
  }

  @override
  GetPage? onPageCalled(GetPage? page) {
    // Atualizar atividade quando uma página é chamada
    _updateActivity();
    return super.onPageCalled(page);
  }

  @override
  List<Bindings>? onBindingsStart(List<Bindings>? bindings) {
    // Atualizar atividade quando bindings são iniciados
    _updateActivity();
    return super.onBindingsStart(bindings);
  }

  @override
  GetPageBuilder? onPageBuildStart(GetPageBuilder? page) {
    // Atualizar atividade quando uma página começa a ser construída
    _updateActivity();
    return super.onPageBuildStart(page);
  }

  /// Atualizar atividade de forma segura
  void _updateActivity() {
    try {
      final sessionManager = Get.find<SessionManager>();
      sessionManager.updateActivity();
    } catch (e) {
      // SessionManager pode não estar disponível ainda
      debugPrint('SessionActivityMiddleware: SessionManager não disponível');
    }
  }
}

/// 🎯 Mixin para Widgets que Atualizam Atividade
///
/// Use este mixin em widgets importantes para atualizar
/// a atividade automaticamente
mixin SessionActivityMixin<T extends StatefulWidget> on State<T> {
  @override
  void initState() {
    super.initState();
    _updateActivity();
  }

  @override
  void didUpdateWidget(T oldWidget) {
    super.didUpdateWidget(oldWidget);
    _updateActivity();
  }

  void _updateActivity() {
    try {
      final sessionManager = Get.find<SessionManager>();
      sessionManager.updateActivity();
    } catch (e) {
      // Ignorar se SessionManager não estiver disponível
    }
  }
}

/// 🔄 Widget Wrapper para Atualização de Atividade
///
/// Envolve qualquer widget para atualizar atividade automaticamente
class SessionActivityWrapper extends StatefulWidget {
  final Widget child;
  final bool updateOnTap;
  final bool updateOnScroll;

  const SessionActivityWrapper({
    super.key,
    required this.child,
    this.updateOnTap = true,
    this.updateOnScroll = false,
  });

  @override
  State<SessionActivityWrapper> createState() => _SessionActivityWrapperState();
}

class _SessionActivityWrapperState extends State<SessionActivityWrapper>
    with SessionActivityMixin {
  @override
  Widget build(BuildContext context) {
    Widget wrappedChild = widget.child;

    // Adicionar detector de tap se habilitado
    if (widget.updateOnTap) {
      wrappedChild = GestureDetector(
        onTap: _updateActivity,
        onPanStart: (_) => _updateActivity(),
        onPanUpdate: (_) => _updateActivity(),
        behavior: HitTestBehavior.translucent,
        child: wrappedChild,
      );
    }

    // Adicionar detector de scroll se habilitado
    if (widget.updateOnScroll) {
      wrappedChild = NotificationListener<ScrollNotification>(
        onNotification: (scrollNotification) {
          if (scrollNotification is ScrollStartNotification ||
              scrollNotification is ScrollUpdateNotification) {
            _updateActivity();
          }
          return false;
        },
        child: wrappedChild,
      );
    }

    return wrappedChild;
  }
}
