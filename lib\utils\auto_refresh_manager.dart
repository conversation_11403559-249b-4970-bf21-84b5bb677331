import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

/// Sistema de Gerenciamento de Atualização Automática
/// Evita atualizações manuais e garante sincronização em tempo real
class AutoRefreshManager {
  static final AutoRefreshManager _instance = AutoRefreshManager._internal();
  factory AutoRefreshManager() => _instance;
  AutoRefreshManager._internal();

  // Controladores de streaming para diferentes tipos de dados
  final Map<String, StreamController<Map<String, dynamic>>> _dataStreams = {};

  // Timers para diferentes tipos de refresh
  final Map<String, Timer> _refreshTimers = {};

  // Configurações de intervalo por tipo de dados
  final Map<String, Duration> _refreshIntervals = {
    'fila_paciente': const Duration(seconds: 30), // Fila do paciente
    'fila_medico': const Duration(seconds: 20), // Fila do médico
    'secretaria': const Duration(minutes: 1), // Dados da secretaria
    'notificacoes': const Duration(seconds: 15), // Notificações
    'emergencias': const Duration(seconds: 10), // Emergências
    'status_geral': const Duration(seconds: 45), // Status geral do sistema
  };

  // Callbacks de atualização por tipo
  final Map<String, Future<Map<String, dynamic>> Function()> _refreshCallbacks =
      {};

  /// Registrar um tipo de dados para atualização automática
  void registerAutoRefresh({
    required String dataType,
    required Future<Map<String, dynamic>> Function() refreshCallback,
    Duration? customInterval,
  }) {
    debugPrint('🔄 [AUTO_REFRESH] Registrando tipo: $dataType');

    // Configurar callback
    _refreshCallbacks[dataType] = refreshCallback;

    // Configurar intervalo personalizado se fornecido
    if (customInterval != null) {
      _refreshIntervals[dataType] = customInterval;
    }

    // Criar stream se não existir
    if (!_dataStreams.containsKey(dataType)) {
      _dataStreams[dataType] =
          StreamController<Map<String, dynamic>>.broadcast();
    }

    // Iniciar timer de refresh
    _startRefreshTimer(dataType);

    // Executar primeira atualização imediatamente
    _executeRefresh(dataType);
  }

  /// Iniciar timer de refresh para um tipo de dados
  void _startRefreshTimer(String dataType) {
    // Cancelar timer anterior se existir
    _refreshTimers[dataType]?.cancel();

    final interval = _refreshIntervals[dataType] ?? const Duration(minutes: 1);

    _refreshTimers[dataType] = Timer.periodic(interval, (_) {
      _executeRefresh(dataType);
    });

    debugPrint(
        '✅ [AUTO_REFRESH] Timer configurado para $dataType - Intervalo: ${interval.inSeconds}s');
  }

  /// Executar refresh para um tipo de dados
  Future<void> _executeRefresh(String dataType) async {
    try {
      final callback = _refreshCallbacks[dataType];
      if (callback == null) return;

      debugPrint('🔄 [AUTO_REFRESH] Executando refresh para: $dataType');

      final data = await callback();

      // Enviar dados atualizados para o stream
      final stream = _dataStreams[dataType];
      if (stream != null && !stream.isClosed) {
        stream.add({
          'type': dataType,
          'data': data,
          'timestamp': DateTime.now().toIso8601String(),
          'source': 'auto_refresh',
        });

        debugPrint('✅ [AUTO_REFRESH] Dados atualizados para: $dataType');
      }
    } catch (e) {
      debugPrint('❌ [AUTO_REFRESH] Erro ao atualizar $dataType: $e');

      // Enviar erro para o stream
      final stream = _dataStreams[dataType];
      if (stream != null && !stream.isClosed) {
        stream.add({
          'type': dataType,
          'error': e.toString(),
          'timestamp': DateTime.now().toIso8601String(),
          'source': 'auto_refresh_error',
        });
      }
    }
  }

  /// Obter stream de dados para um tipo específico
  Stream<Map<String, dynamic>>? getDataStream(String dataType) {
    return _dataStreams[dataType]?.stream;
  }

  /// Forçar refresh imediato para um tipo de dados
  Future<void> forceRefresh(String dataType) async {
    debugPrint('🔄 [AUTO_REFRESH] Refresh forçado para: $dataType');
    await _executeRefresh(dataType);
  }

  /// Pausar refresh para um tipo de dados
  void pauseRefresh(String dataType) {
    _refreshTimers[dataType]?.cancel();
    debugPrint('⏸️ [AUTO_REFRESH] Refresh pausado para: $dataType');
  }

  /// Retomar refresh para um tipo de dados
  void resumeRefresh(String dataType) {
    if (_refreshCallbacks.containsKey(dataType)) {
      _startRefreshTimer(dataType);
      debugPrint('▶️ [AUTO_REFRESH] Refresh retomado para: $dataType');
    }
  }

  /// Alterar intervalo de refresh para um tipo de dados
  void updateRefreshInterval(String dataType, Duration newInterval) {
    _refreshIntervals[dataType] = newInterval;

    if (_refreshCallbacks.containsKey(dataType)) {
      _startRefreshTimer(dataType);
      debugPrint(
          '🔧 [AUTO_REFRESH] Intervalo atualizado para $dataType: ${newInterval.inSeconds}s');
    }
  }

  /// Remover tipo de dados do sistema de refresh
  void unregisterAutoRefresh(String dataType) {
    debugPrint('🗑️ [AUTO_REFRESH] Removendo tipo: $dataType');

    // Cancelar timer
    _refreshTimers[dataType]?.cancel();
    _refreshTimers.remove(dataType);

    // Fechar stream
    _dataStreams[dataType]?.close();
    _dataStreams.remove(dataType);

    // Remover callback
    _refreshCallbacks.remove(dataType);

    debugPrint('✅ [AUTO_REFRESH] Tipo removido: $dataType');
  }

  /// Configurar modo de baixo consumo (intervalos maiores)
  void enableLowPowerMode() {
    debugPrint('🔋 [AUTO_REFRESH] Ativando modo de baixo consumo...');

    final lowPowerIntervals = {
      'fila_paciente': const Duration(minutes: 2), // 30s → 2min
      'fila_medico': const Duration(minutes: 1), // 20s → 1min
      'secretaria': const Duration(minutes: 3), // 1min → 3min
      'notificacoes': const Duration(minutes: 1), // 15s → 1min
      'emergencias': const Duration(seconds: 30), // 10s → 30s
      'status_geral': const Duration(minutes: 5), // 45s → 5min
    };

    for (final entry in lowPowerIntervals.entries) {
      if (_refreshCallbacks.containsKey(entry.key)) {
        updateRefreshInterval(entry.key, entry.value);
      }
    }

    debugPrint('✅ [AUTO_REFRESH] Modo de baixo consumo ativado');
  }

  /// Configurar modo normal (intervalos padrão)
  void enableNormalMode() {
    debugPrint('⚡ [AUTO_REFRESH] Ativando modo normal...');

    final normalIntervals = {
      'fila_paciente': const Duration(seconds: 30),
      'fila_medico': const Duration(seconds: 20),
      'secretaria': const Duration(minutes: 1),
      'notificacoes': const Duration(seconds: 15),
      'emergencias': const Duration(seconds: 10),
      'status_geral': const Duration(seconds: 45),
    };

    for (final entry in normalIntervals.entries) {
      if (_refreshCallbacks.containsKey(entry.key)) {
        updateRefreshInterval(entry.key, entry.value);
      }
    }

    debugPrint('✅ [AUTO_REFRESH] Modo normal ativado');
  }

  /// Obter estatísticas do sistema
  Map<String, dynamic> getStats() {
    return {
      'registered_types': _refreshCallbacks.keys.toList(),
      'active_timers': _refreshTimers.length,
      'active_streams': _dataStreams.length,
      'intervals': Map.fromEntries(
        _refreshIntervals.entries.map(
          (e) => MapEntry(e.key, '${e.value.inSeconds}s'),
        ),
      ),
    };
  }

  /// Limpar todos os recursos
  void dispose() {
    debugPrint('🧹 [AUTO_REFRESH] Limpando todos os recursos...');

    // Cancelar todos os timers
    for (final timer in _refreshTimers.values) {
      timer.cancel();
    }
    _refreshTimers.clear();

    // Fechar todos os streams
    for (final stream in _dataStreams.values) {
      stream.close();
    }
    _dataStreams.clear();

    // Limpar callbacks
    _refreshCallbacks.clear();

    debugPrint('✅ [AUTO_REFRESH] Todos os recursos limpos');
  }

  /// ✅ NOVO: Sistema inteligente de detecção de conectividade
  bool _isOnline = true;

  void setConnectivityStatus(bool isOnline) {
    if (_isOnline != isOnline) {
      _isOnline = isOnline;

      if (isOnline) {
        debugPrint('🌐 [AUTO_REFRESH] Conexão restaurada - retomando refreshs');
        _resumeAllRefresh();
      } else {
        debugPrint('📵 [AUTO_REFRESH] Conexão perdida - pausando refreshs');
        _pauseAllRefresh();
      }
    }
  }

  void _pauseAllRefresh() {
    for (final dataType in _refreshCallbacks.keys) {
      pauseRefresh(dataType);
    }
  }

  void _resumeAllRefresh() {
    for (final dataType in _refreshCallbacks.keys) {
      resumeRefresh(dataType);
    }
  }

  /// ✅ NOVO: Sistema de priorização de refresh
  void setPriority(String dataType, RefreshPriority priority) {
    Duration interval;

    switch (priority) {
      case RefreshPriority.high:
        interval = const Duration(seconds: 10);
        break;
      case RefreshPriority.medium:
        interval = const Duration(seconds: 30);
        break;
      case RefreshPriority.low:
        interval = const Duration(minutes: 2);
        break;
    }

    updateRefreshInterval(dataType, interval);
    debugPrint(
        '🎯 [AUTO_REFRESH] Prioridade $priority definida para $dataType');
  }
}

/// Enum para prioridades de refresh
enum RefreshPriority {
  high, // Atualização rápida (10s)
  medium, // Atualização normal (30s)
  low, // Atualização lenta (2min)
}
