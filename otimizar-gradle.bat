@echo off
echo 🚀 Otimizando Gradle para máxima performance...
echo.

:: Verificar se está no diretório correto
if not exist "android\gradle.properties" (
    echo ❌ Execute este script na raiz do projeto Flutter!
    echo 📍 Certifique-se de estar na pasta que contém a pasta 'android'
    pause
    exit /b 1
)

echo 🧹 Limpando caches do Gradle...

:: Parar todos os daemons do Gradle
echo 🛑 Parando daemons do Gradle...
call gradlew --stop 2>nul

:: Limpar cache do Gradle
echo 🗑️ Limpando cache do Gradle...
if exist "%USERPROFILE%\.gradle\caches" (
    rmdir /s /q "%USERPROFILE%\.gradle\caches" 2>nul
    echo ✅ Cache do Gradle limpo
)

:: Limpar daemon do Gradle
if exist "%USERPROFILE%\.gradle\daemon" (
    rmdir /s /q "%USERPROFILE%\.gradle\daemon" 2>nul
    echo ✅ Daemon do Gradle limpo
)

:: Limpar build do projeto
echo 🗑️ Limpando build do projeto...
if exist "build" (
    rmdir /s /q "build" 2>nul
)
if exist "android\build" (
    rmdir /s /q "android\build" 2>nul
)
if exist "android\app\build" (
    rmdir /s /q "android\app\build" 2>nul
)
if exist ".dart_tool" (
    rmdir /s /q ".dart_tool" 2>nul
)

echo ✅ Builds limpos

:: Flutter clean
echo 🧹 Executando flutter clean...
call flutter clean

:: Flutter pub get
echo 📦 Executando flutter pub get...
call flutter pub get

:: Pré-compilar dependências do Gradle
echo ⚡ Pré-compilando dependências do Gradle...
cd android
call gradlew tasks --all >nul 2>&1
cd ..

echo.
echo 🎉 Otimização concluída!
echo.
echo 📊 Configurações aplicadas:
echo    • Memória JVM: 6GB (otimizada para Ryzen 7 5700X)
echo    • Workers: 8 (um por core físico)
echo    • Cache: Habilitado
echo    • Paralelismo: Máximo
echo    • Compilação incremental: Habilitada
echo    • G1GC: Otimizado para performance
echo    • Opções experimentais: Removidas para estabilidade
echo.
echo 🚀 Próximos builds serão muito mais rápidos!
echo.
echo 💡 Dicas para manter a performance:
echo    • Mantenha o VS Code aberto (daemon ativo)
echo    • Use 'flutter run' em vez de rebuild completo
echo    • Execute este script semanalmente para limpar caches
echo.
pause
