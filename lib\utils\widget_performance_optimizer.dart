import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Sistema de otimização de performance para widgets
class WidgetPerformanceOptimizer {
  static final Map<String, dynamic> _memoizedWidgets = {};
  static final Map<String, DateTime> _buildTimes = {};

  /// Wrapper que automaticamente adiciona RepaintBoundary para widgets pesados
  static Widget optimizedWidget({
    required Widget child,
    required String id,
    bool forceRepaint = false,
    bool enableMemoization = true,
  }) {
    // Memoization: retorna widget cached se não mudou
    if (enableMemoization &&
        _memoizedWidgets.containsKey(id) &&
        !forceRepaint) {
      final cachedWidget = _memoizedWidgets[id];
      if (cachedWidget != null) {
        return cachedWidget;
      }
    }

    // Criar widget otimizado
    final optimizedChild = RepaintBoundary(
      key: ValueKey(id),
      child: child,
    );

    // Cache do widget
    if (enableMemoization) {
      _memoizedWidgets[id] = optimizedChild;
    }

    // Track build time silenciosamente
    _buildTimes[id] = DateTime.now();

    return optimizedChild;
  }

  /// Limpa cache de widgets para liberar memória
  static void clearWidgetCache() {
    _memoizedWidgets.clear();
    _buildTimes.clear();
  }

  /// Limpa widgets específicos do cache
  static void invalidateWidget(String id) {
    _memoizedWidgets.remove(id);
    _buildTimes.remove(id);
  }

  /// Relatório de performance de widgets
  static Map<String, dynamic> getPerformanceReport() {
    return {
      'cached_widgets': _memoizedWidgets.length,
      'build_times': _buildTimes,
      'memory_usage_estimated':
          _memoizedWidgets.length * 1024, // Estimativa em bytes
    };
  }
}

/// Mixin para otimização automática de widgets
mixin OptimizedWidgetMixin<T extends StatefulWidget> on State<T> {
  String get widgetId => T.toString();

  @override
  Widget build(BuildContext context) {
    return WidgetPerformanceOptimizer.optimizedWidget(
      id: widgetId,
      child: buildOptimized(context),
    );
  }

  /// Override este método em vez de build()
  Widget buildOptimized(BuildContext context);
}

/// Widget base otimizado para listas
class OptimizedListTile extends StatelessWidget {
  final Widget? leading;
  final Widget? title;
  final Widget? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;
  final String cacheKey;

  const OptimizedListTile({
    super.key,
    required this.cacheKey,
    this.leading,
    this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return WidgetPerformanceOptimizer.optimizedWidget(
      id: 'list_tile_$cacheKey',
      child: ListTile(
        leading: leading,
        title: title,
        subtitle: subtitle,
        trailing: trailing,
        onTap: onTap,
      ),
    );
  }
}

/// Widget otimizado para cards
class OptimizedCard extends StatelessWidget {
  final Widget child;
  final String cacheKey;
  final EdgeInsetsGeometry? margin;
  final double? elevation;

  const OptimizedCard({
    super.key,
    required this.child,
    required this.cacheKey,
    this.margin,
    this.elevation,
  });

  @override
  Widget build(BuildContext context) {
    return WidgetPerformanceOptimizer.optimizedWidget(
      id: 'card_$cacheKey',
      child: Card(
        margin: margin,
        elevation: elevation ?? 2,
        child: child,
      ),
    );
  }
}

/// Sistema de pré-loading de widgets
class WidgetPreloader {
  static final Map<String, Widget> _preloadedWidgets = {};

  /// Pré-carrega widget que será usado em breve
  static void preloadWidget(String id, Widget Function() builder) {
    if (!_preloadedWidgets.containsKey(id)) {
      Future.microtask(() {
        _preloadedWidgets[id] = builder();
      });
    }
  }

  /// Obtém widget pré-carregado
  static Widget? getPreloadedWidget(String id) {
    return _preloadedWidgets[id];
  }

  /// Limpa widgets pré-carregados
  static void clearPreloaded() {
    _preloadedWidgets.clear();
  }
}
