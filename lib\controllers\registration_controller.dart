import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:flutter/foundation.dart' show debugPrint;
import '../utils/date_utils.dart';

Future<ParseUser?> registerUser({
  required String username,
  required String email,
  required String senha,
  required String tipo,
  String? nome,
  String? crm,
  String? cpf,
  String? especializacao,
  String? cnpj,
  String? tipoHospital,
  String? telefone,
  double? latitude,
  double? longitude,
  bool? ativo,
  String? areaProfissional, // Adicionado novo parâmetro opcional
}) async {
  ParseUser? createdUser;
  ParseObject? createdProfile;

  try {
    // Verificar se já existe usuário
    final queryEmail = QueryBuilder<ParseUser>(ParseUser.forQuery())
      ..whereEqualTo('email', email);
    final queryUsername = QueryBuilder<ParseUser>(ParseUser.forQuery())
      ..whereEqualTo('username', username);

    final emails = await queryEmail.query();
    final usernames = await queryUsername.query();

    if (emails.results != null && emails.results!.isNotEmpty) {
      throw Exception('Este e-mail já está cadastrado');
    }
    if (usernames.results != null && usernames.results!.isNotEmpty) {
      throw Exception('Este nome já está em uso');
    }

    // Obter data atual
    final now = BrazilTimeZone.now();
    debugPrint('Data de cadastro: ${now.toString()}');

    // Criar usuário base
    final user = ParseUser(username, senha, email)
      ..set('tipo', tipo)
      ..set<DateTime>('dataCadastro', BrazilTimeZone.createForParse(now));

    final userResult = await user.signUp();
    if (!userResult.success) {
      throw Exception(userResult.error?.message ?? 'Erro ao criar usuário');
    }

    createdUser = user;

    // Criar perfil específico
    if (tipo == 'medico') {
      if (crm == null || cpf == null || especializacao == null) {
        throw Exception('Dados do médico incompletos');
      }

      final medico = ParseObject('Medico')
        ..set('crm', crm)
        ..set('cpf', int.tryParse(cpf.replaceAll(RegExp(r'[^0-9]'), '')))
        ..set('especialidade', especializacao)
        ..set('nome', nome ?? username) // Usando o username como nome
        ..set('ativo', ativo)
        ..set<DateTime>('dataCadastro', BrazilTimeZone.createForParse(now))
        ..set<ParseUser>('user_medico', user);

      final medicoResult = await medico.save();
      if (!medicoResult.success) {
        await user.destroy();
        throw Exception(
            'Erro ao criar perfil médico: ${medicoResult.error?.message}');
      }

      createdProfile = medico;
    } else if (tipo == 'consultorio') {
      if (cnpj == null ||
          tipoHospital == null ||
          telefone == null ||
          latitude == null ||
          longitude == null) {
        throw Exception('Dados do hospital incompletos');
      }

      final consultorio = ParseObject('consultorio')
        ..set('cnpj', cnpj)
        ..set('tipo', tipoHospital)
        ..set('telefone', telefone)
        ..set('latitude', latitude)
        ..set('longitude', longitude)
        ..set('nome', username)
        ..set('ativo', ativo)
        ..set<DateTime>('dataCadastro', BrazilTimeZone.createForParse(now))
        ..set<ParseUser>('user_consultorio', user);

      final consultorioResult = await consultorio.save();
      if (!consultorioResult.success) {
        await user.destroy();
        throw Exception(
            'Erro ao criar perfil do hospital: ${consultorioResult.error?.message}');
      }

      createdProfile = consultorio;
    }

    return createdUser;
  } catch (e) {
    // Rollback em caso de erro
    if (createdProfile != null) {
      await createdProfile.delete();
    }
    if (createdUser != null) {
      await createdUser.delete();
    }

    if (e is Exception) {
      rethrow;
    }
    throw Exception('Erro no registro: $e');
  }
}

// Função auxiliar para validar CPF
bool isValidCPF(String cpf) {
  var numbers = cpf.replaceAll(RegExp(r'[^0-9]'), '');
  if (numbers.length != 11) return false;

  if (RegExp(r'^(\d)\1*$').hasMatch(numbers)) return false;

  List<int> digits = numbers.split('').map(int.parse).toList();

  var sum = 0;
  for (var i = 0; i < 9; i++) {
    sum += digits[i] * (10 - i);
  }
  var digit1 = (sum * 10) % 11;
  digit1 = digit1 == 10 ? 0 : digit1;

  sum = 0;
  for (var i = 0; i < 10; i++) {
    sum += digits[i] * (11 - i);
  }
  var digit2 = (sum * 10) % 11;
  digit2 = digit2 == 10 ? 0 : digit2;

  return digits[9] == digit1 && digits[10] == digit2;
}

// Função auxiliar para validar CNPJ
bool isValidCNPJ(String cnpj) {
  var numbers = cnpj.replaceAll(RegExp(r'[^0-9]'), '');
  if (numbers.length != 14) return false;

  if (RegExp(r'^(\d)\1*$').hasMatch(numbers)) return false;

  List<int> digits = numbers.split('').map(int.parse).toList();

  var sum = 0;
  var weight = 2;
  for (var i = 11; i >= 0; i--) {
    sum += digits[i] * weight;
    weight = weight == 9 ? 2 : weight + 1;
  }
  var digit1 = sum % 11 < 2 ? 0 : 11 - (sum % 11);

  sum = 0;
  weight = 2;
  for (var i = 12; i >= 0; i--) {
    sum += digits[i] * weight;
    weight = weight == 9 ? 2 : weight + 1;
  }
  var digit2 = sum % 11 < 2 ? 0 : 11 - (sum % 11);

  return digits[12] == digit1 && digits[13] == digit2;
}
