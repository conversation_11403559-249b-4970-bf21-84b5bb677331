# Guia Completo: Configuração do Java e JAVA_HOME

## 📋 Pré-requisitos
- Windows 10/11
- Acesso de administrador
- Conexão com internet

## 🎯 Objetivo
Configurar o Java Development Kit (JDK) mais recente e definir corretamente as variáveis de ambiente JAVA_HOME e PATH.

## 📥 Passo 1: Download do JDK

### Opção A: Oracle JDK (Recomendado para produção)
1. Acesse: https://www.oracle.com/java/technologies/downloads/
2. Baixe a versão mais recente do JDK (atualmente JDK 21 LTS ou JDK 23)
3. Escolha: **Windows x64 Installer** (.exe)

### Opção B: OpenJDK (Gratuito e open source)
1. Acesse: https://adoptium.net/
2. Baixe o **Eclipse Temurin** (OpenJDK)
3. Escolha: **JDK 21 LTS** ou **JDK 17 LTS** (versões estáveis)
4. Selecione: **Windows x64** (.msi)

## 🔧 Passo 2: Instalação

### Para Oracle JDK:
1. Execute o arquivo `.exe` baixado
2. Siga o assistente de instalação
3. **IMPORTANTE**: Anote o caminho de instalação (geralmente: `C:\Program Files\Java\jdk-XX`)

### Para OpenJDK (Eclipse Temurin):
1. Execute o arquivo `.msi` baixado
2. Durante a instalação, **marque a opção**: "Set JAVA_HOME variable"
3. **Marque também**: "Add to PATH"
4. Anote o caminho de instalação

## 🌍 Passo 3: Configurar Variáveis de Ambiente

### Método 1: Interface Gráfica (Recomendado)

1. **Abrir Configurações do Sistema:**
   - Pressione `Win + R`
   - Digite: `sysdm.cpl`
   - Pressione Enter

2. **Acessar Variáveis de Ambiente:**
   - Clique na aba "Avançado"
   - Clique em "Variáveis de Ambiente..."

3. **Configurar JAVA_HOME:**
   - Na seção "Variáveis do sistema", clique em "Novo..."
   - Nome da variável: `JAVA_HOME`
   - Valor da variável: `C:\Program Files\Java\jdk-21` (ajuste conforme sua versão)
   - Clique em "OK"

4. **Configurar PATH:**
   - Na seção "Variáveis do sistema", encontre e selecione "Path"
   - Clique em "Editar..."
   - Clique em "Novo"
   - Adicione: `%JAVA_HOME%\bin`
   - Clique em "OK" em todas as janelas

### Método 2: Linha de Comando (PowerShell como Administrador)

```powershell
# Definir JAVA_HOME (ajuste o caminho conforme sua instalação)
[Environment]::SetEnvironmentVariable("JAVA_HOME", "C:\Program Files\Java\jdk-21", "Machine")

# Adicionar ao PATH
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
$newPath = $currentPath + ";%JAVA_HOME%\bin"
[Environment]::SetEnvironmentVariable("PATH", $newPath, "Machine")
```

## ✅ Passo 4: Verificação

1. **Feche todos os terminais/prompt de comando abertos**
2. **Abra um novo PowerShell ou CMD**
3. **Execute os comandos de verificação:**

```cmd
# Verificar versão do Java
java -version

# Verificar JAVA_HOME
echo %JAVA_HOME%

# Verificar javac (compilador)
javac -version
```

### Resultado esperado:
```
java version "21.0.1" 2023-10-17 LTS
Java(TM) SE Runtime Environment (build 21.0.1+12-LTS-29)
Java HotSpot(TM) 64-Bit Server VM (build 21.0.1+12-LTS-29, mixed mode, sharing)

C:\Program Files\Java\jdk-21

javac 21.0.1
```

## 🔧 Passo 5: Configuração para Flutter/Android

### Atualizar gradle.properties
No arquivo `android/gradle.properties` do seu projeto Flutter, adicione:

```properties
# Forçar uso do Java configurado
org.gradle.java.home=C:\\Program Files\\Java\\jdk-21
```

### Verificar Android Studio
1. Abra o Android Studio
2. Vá em: **File > Project Structure > SDK Location**
3. Em "JDK Location", defina o caminho do JDK instalado

## 🚨 Solução de Problemas

### Problema: "java não é reconhecido como comando"
**Solução:**
1. Verifique se o PATH está correto
2. Reinicie o computador
3. Abra um novo terminal

### Problema: Múltiplas versões do Java
**Solução:**
1. Desinstale versões antigas pelo Painel de Controle
2. Limpe variáveis de ambiente antigas
3. Configure apenas a versão desejada

### Problema: Android Studio não reconhece o JDK
**Solução:**
1. No Android Studio: **File > Settings > Build > Build Tools > Gradle**
2. Em "Gradle JDK", selecione o JDK correto
3. Reinicie o Android Studio

## 📝 Versões Recomendadas

### Para Desenvolvimento Android/Flutter:
- **JDK 17 LTS** (Mais estável)
- **JDK 21 LTS** (Mais recente LTS)

### Para Projetos Corporativos:
- **JDK 17 LTS** (Suporte até 2029)
- **JDK 11 LTS** (Se necessário compatibilidade)

## 🔄 Script Automático (PowerShell)

Salve como `setup-java.ps1` e execute como administrador:

```powershell
# Script para configurar Java automaticamente
$javaVersion = "21"
$javaPath = "C:\Program Files\Java\jdk-$javaVersion"

# Verificar se o Java está instalado
if (Test-Path $javaPath) {
    Write-Host "Configurando JAVA_HOME para: $javaPath"
    
    # Definir JAVA_HOME
    [Environment]::SetEnvironmentVariable("JAVA_HOME", $javaPath, "Machine")
    
    # Atualizar PATH
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
    if ($currentPath -notlike "*%JAVA_HOME%\bin*") {
        $newPath = $currentPath + ";%JAVA_HOME%\bin"
        [Environment]::SetEnvironmentVariable("PATH", $newPath, "Machine")
    }
    
    Write-Host "✅ Java configurado com sucesso!"
    Write-Host "🔄 Reinicie o terminal para aplicar as mudanças"
} else {
    Write-Host "❌ JDK não encontrado em: $javaPath"
    Write-Host "📥 Baixe e instale o JDK primeiro"
}
```

## 📞 Suporte

Se encontrar problemas:
1. Verifique se executou como administrador
2. Reinicie o computador após as configurações
3. Confirme o caminho de instalação do Java
4. Teste em um novo terminal/PowerShell

---
**Última atualização:** Janeiro 2025
**Testado em:** Windows 10/11, Flutter 3.x, Android Studio
