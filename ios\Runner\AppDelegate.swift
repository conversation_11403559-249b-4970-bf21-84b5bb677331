import Flutter
import UIKit
import Firebase
import FirebaseMessaging
import GoogleMaps
import Security

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    
    // ✅ PRIMEIRO: Inicializar Google Maps SDK com a chave correta
    print("🗺️ Iniciando configuração do Google Maps...")
    
    if let apiKey = Bundle.main.object(forInfoDictionaryKey: "GMSApiKey") as? String {
      print("✅ API Key encontrada no Info.plist: \(apiKey.prefix(20))...")
      GMSServices.provideAPIKey(apiKey)
      print("✅ Google Maps SDK inicializado com sucesso!")
    } else {
      print("❌ ERRO CRÍTICO: GMSApiKey não encontrada no Info.plist")
      print("❌ Google Maps NÃO VAI FUNCIONAR!")
      
      // Listar todas as chaves do Info.plist para debug
      if let infoPlist = Bundle.main.infoDictionary {
        print("🔍 Chaves disponíveis no Info.plist:")
        for key in infoPlist.keys.sorted() {
          print("   - \(key)")
        }
      }
    }
    
    // SEGUNDO: Configurar Firebase
    print("🔥 Inicializando Firebase...")
    FirebaseApp.configure()
    print("✅ Firebase configurado!")
    
    // Configurar push notifications
    if #available(iOS 10.0, *) {
      UNUserNotificationCenter.current().delegate = self

      let authOptions: UNAuthorizationOptions = [.alert, .badge, .sound]
      UNUserNotificationCenter.current().requestAuthorization(
        options: authOptions,
        completionHandler: { _, _ in }
      )
    } else {
      let settings: UIUserNotificationSettings =
        UIUserNotificationSettings(types: [.alert, .badge, .sound], categories: nil)
      application.registerUserNotificationSettings(settings)
    }

    application.registerForRemoteNotifications()
    
    // Configurar Messaging delegate
    Messaging.messaging().delegate = self
    
    let controller : FlutterViewController = window?.rootViewController as! FlutterViewController
    let keychainChannel = FlutterMethodChannel(name: "br.com.saudesemespera.keychain",
                                              binaryMessenger: controller.binaryMessenger)
    
    keychainChannel.setMethodCallHandler({
      (call: FlutterMethodCall, result: @escaping FlutterResult) -> Void in
      
      if call.method == "getKeychainIdentifier" {
        self.getKeychainIdentifier(result: result)
      } else if call.method == "setKeychainIdentifier" {
        if let identifier = call.arguments as? String {
          self.setKeychainIdentifier(identifier: identifier, result: result)
        } else {
          result(FlutterError(code: "INVALID_ARGUMENT", 
                             message: "Identifier deve ser uma string", 
                             details: nil))
        }
      } else {
        result(FlutterMethodNotImplemented)
      }
    })
    
    // Registrar plugins Flutter
    GeneratedPluginRegistrant.register(with: self)
    
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
  
  // MARK: - Push Notifications
  override func application(_ application: UIApplication,
                           didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
    print("APNS Token received: \(deviceToken)")
    Messaging.messaging().apnsToken = deviceToken
  }
  
  override func application(_ application: UIApplication,
                           didFailToRegisterForRemoteNotificationsWithError error: Error) {
    print("Failed to register for remote notifications: \(error)")
  }
  
  // Handle notification when app is in foreground
  override func userNotificationCenter(_ center: UNUserNotificationCenter,
                                      willPresent notification: UNNotification,
                                      withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
    if #available(iOS 14.0, *) {
      completionHandler([.banner, .badge, .sound])
    } else {
      completionHandler([.alert, .badge, .sound])
    }
  }
  
  // Handle notification tap
  override func userNotificationCenter(_ center: UNUserNotificationCenter,
                                      didReceive response: UNNotificationResponse,
                                      withCompletionHandler completionHandler: @escaping () -> Void) {
    completionHandler()
  }
  
  // MARK: - Keychain Methods
  private func setKeychainIdentifier(identifier: String, result: @escaping FlutterResult) {
    let service = "br.com.saudesemespera.deviceid"
    let account = "unique_device_identifier"
    
    if setKeychainValue(service: service, account: account, value: identifier) {
      result(true)
    } else {
      result(FlutterError(code: "KEYCHAIN_ERROR", 
                         message: "Não foi possível salvar identificador no keychain", 
                         details: nil))
    }
  }
  
  private func getKeychainIdentifier(result: @escaping FlutterResult) {
    let service = "br.com.saudesemespera.deviceid"
    let account = "unique_device_identifier"
    
    // Primeiro, tentar recuperar identificador existente
    if let existingIdentifier = getKeychainValue(service: service, account: account) {
      result(existingIdentifier)
      return
    }
    
    // Se não existe, gerar novo identificador e salvar
    let newIdentifier = UUID().uuidString
    
    if setKeychainValue(service: service, account: account, value: newIdentifier) {
      result(newIdentifier)
    } else {
      result(FlutterError(code: "KEYCHAIN_ERROR", 
                         message: "Não foi possível salvar no keychain", 
                         details: nil))
    }
  }
  
  private func getKeychainValue(service: String, account: String) -> String? {
    let query: [String: Any] = [
      kSecClass as String: kSecClassGenericPassword,
      kSecAttrService as String: service,
      kSecAttrAccount as String: account,
      kSecReturnData as String: true,
      kSecMatchLimit as String: kSecMatchLimitOne
    ]
    
    var dataTypeRef: AnyObject?
    let status = SecItemCopyMatching(query as CFDictionary, &dataTypeRef)
    
    if status == errSecSuccess,
       let data = dataTypeRef as? Data,
       let value = String(data: data, encoding: .utf8) {
      return value
    }
    
    return nil
  }
  
  private func setKeychainValue(service: String, account: String, value: String) -> Bool {
    guard let data = value.data(using: .utf8) else { return false }
    
    let query: [String: Any] = [
      kSecClass as String: kSecClassGenericPassword,
      kSecAttrService as String: service,
      kSecAttrAccount as String: account,
      kSecValueData as String: data,
      kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly
    ]
    
    // Primeiro tentar adicionar
    let addStatus = SecItemAdd(query as CFDictionary, nil)
    
    if addStatus == errSecSuccess {
      return true
    } else if addStatus == errSecDuplicateItem {
      // Se já existe, atualizar
      let updateQuery: [String: Any] = [
        kSecClass as String: kSecClassGenericPassword,
        kSecAttrService as String: service,
        kSecAttrAccount as String: account
      ]
      
      let updateAttributes: [String: Any] = [
        kSecValueData as String: data
      ]
      
      let updateStatus = SecItemUpdate(updateQuery as CFDictionary, updateAttributes as CFDictionary)
      return updateStatus == errSecSuccess
    }
    
    return false
  }
}

// MARK: - Firebase Messaging Delegate
extension AppDelegate: MessagingDelegate {
  func messaging(_ messaging: Messaging, didReceiveRegistrationToken fcmToken: String?) {
    print("Firebase registration token: \(String(describing: fcmToken))")
    
    let dataDict: [String: String] = ["token": fcmToken ?? ""]
    NotificationCenter.default.post(
      name: Notification.Name("FCMToken"),
      object: nil,
      userInfo: dataDict
    )
  }
}
