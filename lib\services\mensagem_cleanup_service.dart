import 'package:flutter/foundation.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import '../models/mensagem_fila.dart';

/// ✅ SERVIÇO DE LIMPEZA INTELIGENTE DE MENSAGENS
class MensagemCleanupService {
  static final MensagemCleanupService _instance =
      MensagemCleanupService._internal();
  factory MensagemCleanupService() => _instance;
  MensagemCleanupService._internal();

  /// ✅ LIMPAR MENSAGENS ANTIGAS QUANDO ENTRAR EM NOVA FILA
  Future<void> limparMensagensAnteriores({
    required String medicoId,
    required String consultorioId,
    required String pacienteId,
  }) async {
    try {
      debugPrint('🧹 [MENSAGEM_CLEANUP] Limpando mensagens antigas...');

      // ✅ MARCAR MENSAGENS ANTIGAS COMO EXPIRADAS
      final queryMensagensAntigas = QueryBuilder<ParseObject>(
          ParseObject('MensagemFila'))
        ..whereEqualTo('medico_id', ParseObject('Medico')..objectId = medicoId)
        ..whereEqualTo('consultorio_id',
            ParseObject('consultorio')..objectId = consultorioId)
        ..whereLessThan(
            'createdAt',
            DateTime.now().subtract(
                const Duration(hours: 1))) // Mensagens mais antigas que 1 hora
        ..whereNotEqualTo('expirada', true); // Não já expiradas

      final mensagensAntigas = await queryMensagensAntigas.query();

      if (mensagensAntigas.success && mensagensAntigas.results != null) {
        for (final mensagem in mensagensAntigas.results!) {
          mensagem.set('expirada', true);
          mensagem.set('dataExpiracao', DateTime.now());
          await mensagem.save();
        }

        debugPrint(
            '🧹 [MENSAGEM_CLEANUP] ${mensagensAntigas.results!.length} mensagens antigas marcadas como expiradas');
      }

      // ✅ CRIAR MENSAGEM DE NOVA ENTRADA NA FILA
      await _criarMensagemNovaEntrada(medicoId, consultorioId, pacienteId);
    } catch (e) {
      debugPrint('❌ [MENSAGEM_CLEANUP] Erro ao limpar mensagens: $e');
    }
  }

  /// ✅ FILTRAR MENSAGENS VÁLIDAS (NÃO EXPIRADAS)
  List<MensagemFila> filtrarMensagensValidas(List<MensagemFila> mensagens) {
    final agora = DateTime.now();

    return mensagens.where((mensagem) {
      // ✅ REGRAS DE EXPIRAÇÃO
      final idade = agora.difference(mensagem.dataEnvio);

      // Mensagens gerais expiram em 2 horas
      if (mensagem.prioridade == 'baixa' && idade.inHours >= 2) {
        return false;
      }

      // Mensagens de atraso expiram em 1 hora
      if (mensagem.titulo.toLowerCase().contains('atraso') &&
          idade.inHours >= 1) {
        return false;
      }

      // Mensagens de emergência expiram em 4 horas
      if (mensagem.prioridade == 'alta' && idade.inHours >= 4) {
        return false;
      }

      // Mensagens muito antigas (mais de 6 horas) sempre expiram
      if (idade.inHours >= 6) {
        return false;
      }

      return true;
    }).toList();
  }

  /// ✅ CRIAR MENSAGEM DE NOVA ENTRADA NA FILA
  Future<void> _criarMensagemNovaEntrada(
      String medicoId, String consultorioId, String pacienteId) async {
    try {
      final novaMensagem = ParseObject('MensagemFila')
        ..set('medico_id', ParseObject('Medico')..objectId = medicoId)
        ..set('consultorio_id',
            ParseObject('consultorio')..objectId = consultorioId)
        ..set('titulo', 'Entrada na Fila')
        ..set('mensagem',
            'Você entrou na fila. Acompanhe sua posição em tempo real.')
        ..set('prioridade', 'media')
        ..set('icone', 'queue')
        ..set('data_envio', DateTime.now())
        ..set('expirada', false)
        ..set('paciente_id', pacienteId);

      await novaMensagem.save();
      debugPrint('✅ [MENSAGEM_CLEANUP] Mensagem de nova entrada criada');
    } catch (e) {
      debugPrint('❌ [MENSAGEM_CLEANUP] Erro ao criar mensagem de entrada: $e');
    }
  }

  /// ✅ LIMPEZA AUTOMÁTICA DE MENSAGENS EXPIRADAS (EXECUTAR PERIODICAMENTE)
  Future<void> executarLimpezaAutomatica() async {
    try {
      debugPrint('🗑️ [MENSAGEM_CLEANUP] Executando limpeza automática...');

      // ✅ DELETAR MENSAGENS MUITO ANTIGAS (MAIS DE 24 HORAS)
      final queryMensagensMuitoAntigas = QueryBuilder<ParseObject>(
          ParseObject('MensagemFila'))
        ..whereLessThan(
            'createdAt', DateTime.now().subtract(const Duration(hours: 24)));

      final mensagensMuitoAntigas = await queryMensagensMuitoAntigas.query();

      if (mensagensMuitoAntigas.success &&
          mensagensMuitoAntigas.results != null) {
        for (final mensagem in mensagensMuitoAntigas.results!) {
          await mensagem.delete();
        }

        debugPrint(
            '🗑️ [MENSAGEM_CLEANUP] ${mensagensMuitoAntigas.results!.length} mensagens antigas deletadas');
      }
    } catch (e) {
      debugPrint('❌ [MENSAGEM_CLEANUP] Erro na limpeza automática: $e');
    }
  }

  /// ✅ VERIFICAR SE MENSAGEM AINDA É RELEVANTE PARA SESSÃO ATUAL
  bool isMensagemRelevante(MensagemFila mensagem, String filaAtualId) {
    final agora = DateTime.now();
    final idade = agora.difference(mensagem.dataEnvio);

    // ✅ REGRAS DE RELEVÂNCIA POR TIPO DE MENSAGEM
    switch (mensagem.titulo.toLowerCase()) {
      case String titulo when titulo.contains('atraso'):
        return idade.inMinutes <= 60; // Atrasos são relevantes por 1 hora

      case String titulo when titulo.contains('emergência'):
        return idade.inHours <= 2; // Emergências por 2 horas

      case String titulo when titulo.contains('entrada'):
        return idade.inHours <= 4; // Entrada na fila por 4 horas

      default:
        return idade.inHours <= 2; // Mensagens gerais por 2 horas
    }
  }

  /// ✅ OBTER MENSAGENS VÁLIDAS COM FILTROS INTELIGENTES
  Future<List<MensagemFila>> obterMensagensValidas({
    required String medicoId,
    required String consultorioId,
    required String filaAtualId,
  }) async {
    try {
      // ✅ BUSCAR MENSAGENS RECENTES (ÚLTIMAS 6 HORAS)
      final queryMensagens = QueryBuilder<ParseObject>(
          ParseObject('MensagemFila'))
        ..whereEqualTo('medico_id', ParseObject('Medico')..objectId = medicoId)
        ..whereEqualTo('consultorio_id',
            ParseObject('consultorio')..objectId = consultorioId)
        ..whereGreaterThan(
            'createdAt', DateTime.now().subtract(const Duration(hours: 6)))
        ..whereNotEqualTo('expirada', true)
        ..orderByDescending('createdAt')
        ..setLimit(10); // Limitar a 10 mensagens mais recentes

      final response = await queryMensagens.query();

      if (!response.success || response.results == null) {
        return [];
      }

      // ✅ CONVERTER PARA MENSAGEM_FILA E FILTRAR
      final mensagens = response.results!.map((parseObject) {
        return MensagemFila(
          id: parseObject.objectId ?? 'unknown',
          titulo: parseObject.get<String>('titulo') ?? 'Aviso',
          texto: parseObject.get<String>('mensagem') ?? '',
          dataEnvio: parseObject.get<DateTime>('data_envio') ??
              parseObject.get<DateTime>('createdAt') ??
              DateTime.now(),
          medicoId: medicoId,
          prioridade: parseObject.get<String>('prioridade') ?? 'media',
          icone: parseObject.get<String>('icone') ?? 'notification',
        );
      }).toList();

      // ✅ APLICAR FILTROS DE RELEVÂNCIA
      final mensagensValidas = mensagens.where((mensagem) {
        return isMensagemRelevante(mensagem, filaAtualId);
      }).toList();

      debugPrint(
          '✅ [MENSAGEM_CLEANUP] ${mensagensValidas.length} mensagens válidas encontradas');

      return mensagensValidas;
    } catch (e) {
      debugPrint('❌ [MENSAGEM_CLEANUP] Erro ao obter mensagens válidas: $e');
      return [];
    }
  }
}
