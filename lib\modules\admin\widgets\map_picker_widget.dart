import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:fila_app/theme/theme.dart';
import 'package:fila_app/theme/constants.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:fila_app/modules/admin/widgets/fullscreen_map_view.dart';

class MapPickerWidget extends StatefulWidget {
  final double? latitude;
  final double? longitude;
  final Function(double lat, double lng) onLocationSelected;
  final bool isLoading;

  const MapPickerWidget({
    super.key,
    this.latitude,
    this.longitude,
    required this.onLocationSelected,
    this.isLoading = false,
  });

  @override
  State<MapPickerWidget> createState() => _MapPickerWidgetState();
}

class _MapPickerWidgetState extends State<MapPickerWidget> {
  GoogleMapController? _mapController;
  Set<Marker> _markers = {};
  LatLng? _selectedLocation;

  @override
  void initState() {
    super.initState();
    _initializeLocation();
  }

  @override
  void didUpdateWidget(MapPickerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.latitude != widget.latitude ||
        oldWidget.longitude != widget.longitude) {
      _initializeLocation();
    }
  }

  void _initializeLocation() {
    if (widget.latitude != null && widget.longitude != null) {
      // Validação anti-NaN
      if (!widget.latitude!.isNaN &&
          !widget.longitude!.isNaN &&
          widget.latitude!.isFinite &&
          widget.longitude!.isFinite) {
        setState(() {
          _selectedLocation = LatLng(widget.latitude!, widget.longitude!);
          _updateMarkers();
        });

        // Centralizar mapa se controller estiver disponível
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _centerMap();
        });
      }
    }
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
    _centerMap();
  }

  void _centerMap() {
    if (_mapController != null && _selectedLocation != null) {
      _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(_selectedLocation!, 15),
      );
    }
  }

  void _updateMarkers() {
    if (_selectedLocation != null) {
      setState(() {
        _markers = {
          Marker(
            markerId: const MarkerId('selected_location'),
            position: _selectedLocation!,
            icon:
                BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
            infoWindow: InfoWindow(
              title: 'Localização do Consultório',
              snippet:
                  'Lat: ${_selectedLocation!.latitude.toStringAsFixed(6)}, '
                  'Lng: ${_selectedLocation!.longitude.toStringAsFixed(6)}',
            ),
          ),
        };
      });
    } else {
      setState(() {
        _markers = {};
      });
    }
  }

  void _openFullscreenMap() {
    Get.to(
      () => FullscreenMapView(
        initialLatitude: _selectedLocation?.latitude,
        initialLongitude: _selectedLocation?.longitude,
        onLocationSelected: (lat, lng) {
          setState(() {
            _selectedLocation = LatLng(lat, lng);
            _updateMarkers();
          });
          _centerMap();
          widget.onLocationSelected(lat, lng);
        },
      ),
      transition: Transition.rightToLeft,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSpacing.borderRadius),
      ),
      child: Container(
        height: 300,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppSpacing.borderRadius),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(AppSpacing.borderRadius),
          child: Stack(
            children: [
              // Google Map
              GoogleMap(
                onMapCreated: _onMapCreated,
                initialCameraPosition: CameraPosition(
                  target: _selectedLocation ?? const LatLng(-15.827, -47.921),
                  zoom: _selectedLocation != null ? 15 : 5,
                ),
                markers: _markers,
                mapType: MapType.normal,
                compassEnabled: true,
                myLocationEnabled: false,
                myLocationButtonEnabled: false,
                zoomControlsEnabled: false,
                mapToolbarEnabled: false,
                scrollGesturesEnabled: false,
                zoomGesturesEnabled: false,
                rotateGesturesEnabled: false,
                tiltGesturesEnabled: false,
              ),

              // Overlay clicável para abrir tela cheia
              Positioned.fill(
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: _openFullscreenMap,
                    child: Container(
                      color: Colors.transparent,
                      child: _selectedLocation == null
                          ? Center(
                              child: Container(
                                padding: const EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.9),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.location_on,
                                      color: AppTheme.primaryColor,
                                      size: 32,
                                    ),
                                    const SizedBox(height: 8),
                                    const Text(
                                      'Toque para selecionar localização',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                ),
                              ),
                            )
                          : null,
                    ),
                  ),
                ),
              ),

              // Botões de ação
              Positioned(
                bottom: 16,
                right: 16,
                child: FloatingActionButton(
                  heroTag: "btn_fullscreen",
                  mini: true,
                  backgroundColor: Colors.white,
                  foregroundColor: AppTheme.primaryColor,
                  onPressed: _openFullscreenMap,
                  child: const Icon(Icons.open_in_full),
                ),
              ),

              // Coordenadas atuais
              if (_selectedLocation != null)
                Positioned(
                  top: 16,
                  left: 16,
                  right: 16,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.9),
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.location_on,
                          color: AppTheme.primaryColor,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            '${_selectedLocation!.latitude.toStringAsFixed(6)}, ${_selectedLocation!.longitude.toStringAsFixed(6)}',
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              // Loading overlay
              if (widget.isLoading)
                Positioned.fill(
                  child: Container(
                    color: Colors.black.withOpacity(0.3),
                    child: const Center(
                      child: CircularProgressIndicator(),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }
}
