import 'package:flutter/material.dart';
import 'package:fila_app/theme/theme.dart';
import 'dart:math' as math;

class FloatingActionButtonMenu extends StatefulWidget {
  final Function()? onAddPressed;
  final Function()? onAlertPressed;
  final Function()? onMessagePressed;
  final Function()? onRepairPressed;
  final Function()? onEmergencyPressed;
  final Function()? onMedicoEmergencyPressed;
  final Function()? onFinalizarEmergenciaPressed;

  const FloatingActionButtonMenu({
    super.key,
    this.onAddPressed,
    this.onAlertPressed,
    this.onMessagePressed,
    this.onRepairPressed,
    this.onEmergencyPressed,
    this.onMedicoEmergencyPressed,
    this.onFinalizarEmergenciaPressed,
  });

  @override
  State<FloatingActionButtonMenu> createState() =>
      _FloatingActionButtonMenuState();
}

class _FloatingActionButtonMenuState extends State<FloatingActionButtonMenu>
    with TickerProviderStateMixin {
  bool _isOpen = false;
  late AnimationController _animationController;
  late AnimationController _overlayController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 250),
      vsync: this,
    );
    _overlayController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _overlayController.dispose();
    super.dispose();
  }

  void _toggle() {
    print('🔧 [DEBUG] Menu toggle clicado - Estado atual: $_isOpen');

    // ✅ GARANTIR QUE O WIDGET ESTÁ MONTADO
    if (!mounted) {
      print('❌ [DEBUG] Widget não montado, ignorando toggle');
      return;
    }

    setState(() {
      _isOpen = !_isOpen;
    });

    if (_isOpen) {
      _overlayController.forward();
      _animationController.forward();
    } else {
      _animationController.reverse();
      _overlayController.reverse();
    }

    print('🔧 [DEBUG] Menu toggle - Novo estado: $_isOpen');
  }

  void _close() {
    if (!mounted) return; // ✅ VERIFICAR SE ESTÁ MONTADO

    if (_isOpen) {
      setState(() {
        _isOpen = false;
      });
      _animationController.reverse();
      _overlayController.reverse();
    }
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
    required int index,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      width: 250,
              child: Material(
        elevation: 6,
                borderRadius: BorderRadius.circular(24),
        color: color,
                child: InkWell(
                  onTap: () {
                    print('🔧 [DEBUG] MenuItem clicado: $label');
                    _close();
            // Delay para garantir que a animação não interfira
            Future.delayed(const Duration(milliseconds: 100), () {
                    onTap();
            });
                  },
                  borderRadius: BorderRadius.circular(24),
                  child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                  padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            icon,
                            color: Colors.white,
                    size: 20,
                          ),
                        ),
                const SizedBox(width: 12),
                Expanded(
                          child: Text(
                            label,
                            style: const TextStyle(
                              color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
    );
  }

  @override
  Widget build(BuildContext context) {
    print('🔧 [DEBUG] Build FloatingActionButtonMenu - isOpen: $_isOpen');

    return Material(
      type: MaterialType.transparency,
      child: Stack(
        clipBehavior: Clip.none,
      alignment: Alignment.bottomRight,
      children: [
          // ✅ OVERLAY COM BLUR
        if (_isOpen)
            Positioned.fill(
                child: GestureDetector(
                  onTap: _close,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                    decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.4),
                  ),
                ),
              ),
          ),

          // ✅ MENU ITEMS
        if (_isOpen)
          Positioned(
              bottom: 80,
              right: 8,
              child: Material(
                type: MaterialType.transparency,
            child: Column(
                  mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                // 🟢 FINALIZAR EMERGÊNCIA
                if (widget.onFinalizarEmergenciaPressed != null)
                  _buildMenuItem(
                    icon: Icons.check_circle,
                    label: 'Finalizar Emergência',
                        color: const Color(0xFF2E7D32),
                    onTap: widget.onFinalizarEmergenciaPressed!,
                    index: 0,
                  ),

                    // 🏥 Emergência Médica
                if (widget.onMedicoEmergencyPressed != null)
                  _buildMenuItem(
                    icon: Icons.local_hospital,
                    label: 'Emergência Médica',
                        color: const Color(0xFF8B0000),
                    onTap: widget.onMedicoEmergencyPressed!,
                    index: 1,
                  ),

                    // 🚨 Pausar Fila
                if (widget.onEmergencyPressed != null)
                  _buildMenuItem(
                    icon: Icons.pause_circle_filled,
                    label: 'Pausar Fila',
                        color: const Color(0xFFD32F2F),
                    onTap: widget.onEmergencyPressed!,
                    index: 2,
                  ),

                    // 🔧 Reparar Fila
                if (widget.onRepairPressed != null)
                  _buildMenuItem(
                    icon: Icons.build_circle,
                    label: 'Reparar Fila',
                        color: const Color(0xFFFF8F00),
                    onTap: widget.onRepairPressed!,
                    index: 3,
                  ),

                    // 💬 Mensagem Customizada
                if (widget.onMessagePressed != null)
                  _buildMenuItem(
                    icon: Icons.message,
                    label: 'Mensagem Customizada',
                        color: const Color(0xFF1976D2),
                    onTap: widget.onMessagePressed!,
                    index: 4,
                  ),

                // 📢 Alerta Rápido
                if (widget.onAlertPressed != null)
                  _buildMenuItem(
                    icon: Icons.campaign,
                    label: 'Alerta Rápido',
                        color: const Color(0xFFFF5722),
                    onTap: widget.onAlertPressed!,
                    index: 5,
                  ),
              ],
            ),
              ),
          ),

          // ✅ BOTÃO PRINCIPAL
          Positioned(
            bottom: 16,
            right: 16,
            child: Material(
              elevation: 8,
            borderRadius: BorderRadius.circular(28),
          child: FloatingActionButton(
                onPressed: () {
                  print('🔧 [DEBUG] FloatingActionButton pressionado');
                  _toggle();
                },
                backgroundColor: _isOpen ? Colors.red[400] : Colors.teal,
            child: AnimatedRotation(
              turns: _isOpen ? 0.125 : 0,
              duration: const Duration(milliseconds: 200),
              child: Icon(
                _isOpen ? Icons.close : Icons.menu,
                color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ),
          ),
        ],
        ),
    );
  }
}
