import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:async';
import 'package:fila_app/controllers/secretaria_controller.dart';
import 'package:fila_app/controllers/login_controller.dart';
import 'package:fila_app/views/gradient_background.dart';
import 'package:fila_app/widgets/app_header.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';

class TelaHomeSecretaria extends StatefulWidget {
  const TelaHomeSecretaria({super.key});

  @override
  State<TelaHomeSecretaria> createState() => _TelaHomeSecretariaState();
}

class _TelaHomeSecretariaState extends State<TelaHomeSecretaria> {
  final SecretariaController controller = Get.find<SecretariaController>();
  final LoginController loginController = Get.find<LoginController>();
  final RxInt solicitacoesPendentes = 0.obs;
  Timer? _refreshTimer;
  // Assinatura para receber notificações de mudanças em médicos vinculados
  Subscription? _hospitalMedicoUpdateSubscription;

  @override
  void initState() {
    super.initState();

    // Adiar carregar os dados para depois da construção inicial
    Future.microtask(() {
      _loadHospitalInfo();
      _checkPendingSolicitations();
      _configurarListenerNovosMedicos();
    });

    // Set up timer to periodically check for new solicitations
    _refreshTimer = Timer.periodic(const Duration(minutes: 2), (_) {
      _checkPendingSolicitations();
    });
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    // Cancelar assinatura do LiveQuery
    if (_hospitalMedicoUpdateSubscription != null) {
      LiveQuery().client.unSubscribe(_hospitalMedicoUpdateSubscription!);
    }
    super.dispose();
  }

  // Método para configurar listener de vinculação/desvinculação de médicos
  void _configurarListenerNovosMedicos() async {
    try {
      // Cancelar assinatura anterior, se houver
      if (_hospitalMedicoUpdateSubscription != null) {
        LiveQuery().client.unSubscribe(_hospitalMedicoUpdateSubscription!);
        _hospitalMedicoUpdateSubscription = null;
      }

      // Inscrever em atualizações de vínculo médico-hospital
      final QueryBuilder<ParseObject> query =
          QueryBuilder<ParseObject>(ParseObject('HospitalMedicoUpdate'))
            ..orderByDescending('createdAt');

      // Registrar a assinatura
      _hospitalMedicoUpdateSubscription =
          await LiveQuery().client.subscribe(query);

      // Configurar o handler para eventos de criação
      _hospitalMedicoUpdateSubscription!.on(LiveQueryEvent.create,
          (ParseObject update) {
        _handleHospitalMedicoUpdate(update);
      });

      debugPrint(
          'LiveQuery para HospitalMedicoUpdate configurado na tela home');
    } catch (e) {
      debugPrint(
          'Erro ao configurar LiveQuery para HospitalMedicoUpdate na tela home: $e');
    }
  }

  // Método para processar atualizações de vínculos médico-hospital
  void _handleHospitalMedicoUpdate(ParseObject update) {
    final hospitalId = update.get<String>('hospitalId');
    final action = update.get<String>('action');
    final medicoNome = update.get<String>('medicoNome') ?? 'Médico';

    // Verifica se a notificação é para o hospital atual
    if (controller.currentHospital != null &&
        hospitalId == controller.currentHospital!.objectId) {
      debugPrint(
          '[HOME] Recebida notificação de ${action == 'vincular' ? 'vinculação' : 'desvinculação'} de médico: $medicoNome');

      // Recarregar a lista de médicos para atualizar o contador
      controller.carregarMedicos().then((_) {
        // Agora podemos verificar quantos médicos temos APÓS atualizar a lista
        final contagem = controller.medicos.length;

        // Exibir feedback visual ao usuário
        if (mounted && context.mounted) {
          if (action == 'vincular') {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content:
                    Text('Dr(a). $medicoNome foi vinculado(a) a este hospital'),
                backgroundColor: Colors.green,
                duration: const Duration(seconds: 3),
              ),
            );
          } else if (action == 'desvincular') {
            // Se for o último médico desvinculado, mostrar uma mensagem diferente
            if (contagem == 0) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Nenhum médico vinculado a este hospital'),
                  backgroundColor: Colors.orange,
                  duration: Duration(seconds: 3),
                ),
              );
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                      'Dr(a). $medicoNome foi desvinculado(a) deste hospital'),
                  backgroundColor: Colors.orange,
                  duration: const Duration(seconds: 3),
                ),
              );
            }
          }
        }
      });
    }
  }

  Future<void> _checkPendingSolicitations() async {
    try {
      if (controller.currentHospital == null) return;

      final querySolicitacoes = QueryBuilder<ParseObject>(
          ParseObject('FilaSolicitacao'))
        ..whereEqualTo('hospitalId', controller.currentHospital!.toPointer())
        ..whereEqualTo('status', 'pendente');

      final response = await querySolicitacoes.query();

      if (response.success && response.results != null) {
        solicitacoesPendentes.value = response.results!.length;
      }
    } catch (e) {
      if (mounted) {
        debugPrint('Erro ao verificar solicitações pendentes: $e');
      }
    }
  }

  Future<void> _loadHospitalInfo() async {
    try {
      await controller.carregarDadosSecretaria();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao carregar dados: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _handleLogout() async {
    try {
      // Exibir diálogo de confirmação
      final shouldLogout = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Sair'),
          content: const Text('Deseja realmente sair da sua conta?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Cancelar'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text('Sair'),
            ),
          ],
        ),
      );

      if (shouldLogout == true && mounted) {
        await loginController.logout();
        Get.offAllNamed('/login'); // Retorna para a tela de login
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao fazer logout: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;

        final confirmarSaida = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Confirmar Saída'),
            content: const Text('Deseja realmente sair do aplicativo?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Não'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Sim'),
              ),
            ],
          ),
        );

        if (confirmarSaida == true) {
          Navigator.of(context).pop();
        }
      },
      child: Scaffold(
        body: GradientBackground(
          child: SafeArea(
            // Remover o Obx aqui e usar Column diretamente
            child: Column(
              children: [
                // Header com título e botão voltar com callback para logout
                AppHeader(
                  title: 'Central da Secretária',
                  centerTitle: true,
                  onBackPressed: _handleLogout,
                ),

                // Hospital information
                Container(
                  margin: const EdgeInsets.all(16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black12,
                        blurRadius: 10,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 24,
                        backgroundColor: Colors.teal.withOpacity(0.2),
                        child: const Icon(Icons.local_hospital,
                            color: Colors.teal, size: 28),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        // Usar Obx apenas para este componente específico que precisa ser reativo
                        child: Obx(() => Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  controller.currentHospital
                                          ?.get<String>('nome') ??
                                      'Consultório',
                                  style: const TextStyle(
                                    fontFamily: 'Georgia',
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                if (!controller.isLoading.value)
                                  Text(
                                    controller.medicos.isEmpty
                                        ? 'Nenhum médico vinculado'
                                        : '${controller.medicos.length} ${controller.medicos.length == 1 ? 'médico' : 'médicos'} vinculados',
                                    style: TextStyle(
                                      fontFamily: 'Georgia',
                                      fontSize: 14,
                                      color: Colors.grey.shade700,
                                    ),
                                  ),
                              ],
                            )),
                      ),
                    ],
                  ),
                ),

                // Main content with three options
                // Usar Obx apenas para este componente específico
                Obx(() {
                  if (controller.isLoading.value) {
                    return const Expanded(
                      child: Center(
                        child: CircularProgressIndicator(),
                      ),
                    );
                  } else {
                    return Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            _buildActionButton(
                              title: 'Aceitar Pacientes',
                              subtitle:
                                  'Adicionar pacientes à fila a partir do cadastro QR Code',
                              icon: Icons.people_alt,
                              color: Colors.blue,
                              onTap: () => Get.toNamed('/aceitar_pacientes'),
                              badge: solicitacoesPendentes.value,
                            ),
                            const SizedBox(height: 20),
                            _buildActionButton(
                              title: 'Gerenciar Filas',
                              subtitle:
                                  'Administrar as filas de atendimento dos médicos',
                              icon: Icons.queue,
                              color: Colors.teal,
                              onTap: () => Get.toNamed('/secretaria'),
                            ),
                            const SizedBox(height: 20),
                            _buildActionButton(
                              title: 'Cadastrar Médicos',
                              subtitle:
                                  'Adicionar novos médicos ao consultório',
                              icon: Icons.medical_services_outlined,
                              color: Colors.orange,
                              onTap: () => Get.toNamed('/cadastrar_medicos'),
                            ),
                          ],
                        ),
                      ),
                    );
                  }
                }),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    int badge = 0,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Row(
              children: [
                Stack(
                  clipBehavior: Clip.none,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: color.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        icon,
                        size: 30,
                        color: color,
                      ),
                    ),
                    // Usar Obx apenas para o badge quando ele precisa ser reativo
                    if (badge > 0)
                      Positioned(
                        right: -5,
                        top: -5,
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          constraints: const BoxConstraints(
                            minWidth: 18,
                            minHeight: 18,
                          ),
                          child: Text(
                            badge > 99 ? '99+' : badge.toString(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontFamily: 'Georgia',
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                badge > 0
                    ? TweenAnimationBuilder<double>(
                        duration: const Duration(milliseconds: 500),
                        tween: Tween(begin: 0.8, end: 1.0),
                        builder: (context, value, child) {
                          return Transform.scale(
                            scale: value,
                            child: const Icon(
                              Icons.arrow_forward_ios,
                              color: Colors.red,
                              size: 16,
                            ),
                          );
                        },
                      )
                    : Icon(
                        Icons.arrow_forward_ios,
                        color: color,
                        size: 16,
                      ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
