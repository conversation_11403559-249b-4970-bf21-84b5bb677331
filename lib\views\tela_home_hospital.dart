import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fila_app/views/gradient_background.dart';
import 'package:fila_app/controllers/login_controller.dart';
import 'package:fila_app/views/tela_dados_administrador.dart';

class TelaHospitalScreen extends StatelessWidget {
  const TelaHospitalScreen({super.key});

  Future<void> _handleLogout(BuildContext context) async {
    // Mostra um diálogo de confirmação antes de sair
    final bool? confirmSair = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'Sair da sessão hospital',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontFamily: 'Georgia',
              fontWeight: FontWeight.bold,
            ),
          ),
          content: const Text(
            'Deseja realmente sair da sua conta?',
            textAlign: TextAlign.center,
            style: TextStyle(fontFamily: 'Georgia'),
          ),
          actionsAlignment: MainAxisAlignment.center,
          actions: [
            TextButton(
              style: TextButton.styleFrom(
                foregroundColor: Colors.white,
                backgroundColor: const Color(0xFFA0F0F0), // Verde água mais suave
              ),
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancelar'),
            ),
            const SizedBox(width: 16), // Espaçamento entre os botões
            TextButton(
              style: TextButton.styleFrom(
                foregroundColor: Colors.white,
                backgroundColor: Colors.red, // Vermelho
              ),
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Sim, sair'),
            ),
          ],
        );
      },
    );

    // Se o usuário não confirmou a saída ou fechou o diálogo, não faz nada
    if (confirmSair != true) return;

    try {
      final loginController = LoginController();
      await loginController.logout();

      if (!context.mounted) return;

      Navigator.of(context).pushNamedAndRemoveUntil(
        '/login',
        (Route<dynamic> route) => false,
      );
    } catch (e) {
      debugPrint('Erro ao fazer logout: $e');
      if (!context.mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Erro ao fazer logout. Tente novamente.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientBackground(
        child: LayoutBuilder(
          builder: (context, constraints) {
            final larguraTela = constraints.maxWidth;            return Column(
              children: [
                const Spacer(flex: 1), // Reduzido de flex: 2 para flex: 1 para mover os elementos mais para cima
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: larguraTela * 0.05),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.arrow_back, color: Colors.black87),
                        iconSize: 40,
                        onPressed: () => _handleLogout(context),
                      ),
                      const Text(
                        'Hospital',
                        style: TextStyle(
                          fontFamily: 'Georgia',
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      const SizedBox(width: 40),
                    ],
                  ),
                ),
                const Spacer(flex: 2),
                _buildOptionButton(
                  larguraTela: larguraTela,
                  label: 'Gerenciar secretárias',
                  iconPath: 'assets/settings.png',
                  onTap: () {
                    Get.toNamed('/cadastro_secretaria');
                  },
                ),
                const SizedBox(height: 40),
                _buildOptionButton(
                  larguraTela: larguraTela,
                  label: 'Administrador',
                  iconPath: 'assets/settings.png',
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const TelaDadosAdministrador(),
                      ),
                    );
                  },
                ),
                const Spacer(flex: 3),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildOptionButton({
    required double larguraTela,
    required String label,
    required String iconPath,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            iconPath,
            width: larguraTela * 0.25,
            height: larguraTela * 0.25,
            fit: BoxFit.contain,
          ),
          const SizedBox(height: 10),
          Text(
            label,
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: larguraTela * 0.06,
              fontWeight: FontWeight.w700,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }
}
