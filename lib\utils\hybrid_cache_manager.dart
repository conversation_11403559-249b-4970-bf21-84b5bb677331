import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';

/// Sistema de Cache Híbrido
/// Combina cache em memória, persistência local e LiveQuery
class HybridCacheManager {
  static final HybridCacheManager _instance = HybridCacheManager._internal();
  factory HybridCacheManager() => _instance;
  HybridCacheManager._internal();

  // Cache em memória para acesso rápido
  final Map<String, CacheEntry> _memoryCache = {};

  // Controle de LiveQuery por endpoint
  final Map<String, LiveQuerySubscription> _liveQueries = {};

  // Configurações de TTL por tipo de dados
  final Map<String, Duration> _ttlConfigs = {
    'fila_status': const Duration(seconds: 30),
    'mensagens': const Duration(minutes: 2),
    'solicitacoes': const Duration(seconds: 45),
    'medicos': const Duration(minutes: 10),
    'hospitais': const Duration(hours: 1),
    'notificacoes': const Duration(minutes: 1),
  };

  // Estatísticas de cache
  int _hits = 0;
  int _misses = 0;
  int _liveQueryUpdates = 0;

  /// Buscar dados com cache híbrido e LiveQuery
  Future<T?> fetchWithCache<T>({
    required String cacheKey,
    required String dataType,
    required Future<T> Function() fetchFunction,
    required T Function(Map<String, dynamic>) fromJson,
    required Map<String, dynamic> Function(T) toJson,
    QueryBuilder<ParseObject>? liveQuery,
    bool enableLiveQuery = true,
    bool forceRefresh = false,
  }) async {
    try {
      // 1. Verificar cache em memória primeiro (mais rápido)
      if (!forceRefresh) {
        final cached = _getFromMemoryCache<T>(cacheKey, fromJson);
        if (cached != null) {
          _hits++;
          debugPrint('🟢 [CACHE] HIT memória: $cacheKey');

          // Configurar LiveQuery em background se não existir
          if (enableLiveQuery &&
              liveQuery != null &&
              !_liveQueries.containsKey(cacheKey)) {
            _setupLiveQuery(cacheKey, dataType, liveQuery, fromJson, toJson);
          }

          return cached;
        }
      }

      // 2. Verificar cache persistente
      if (!forceRefresh) {
        final persisted = await _getFromPersistentCache<T>(cacheKey, fromJson);
        if (persisted != null) {
          // Repovoar cache em memória
          final ttl = _ttlConfigs[dataType] ?? const Duration(minutes: 5);
          _memoryCache[cacheKey] = CacheEntry(
            data: toJson(persisted),
            timestamp: DateTime.now(),
            ttl: ttl,
          );

          _hits++;
          debugPrint('🟡 [CACHE] HIT persistente: $cacheKey');

          // Configurar LiveQuery em background
          if (enableLiveQuery &&
              liveQuery != null &&
              !_liveQueries.containsKey(cacheKey)) {
            _setupLiveQuery(cacheKey, dataType, liveQuery, fromJson, toJson);
          }

          return persisted;
        }
      }

      // 3. Buscar dados do servidor
      _misses++;
      debugPrint('🔴 [CACHE] MISS: $cacheKey - Buscando do servidor');

      final data = await fetchFunction();

      // 4. Salvar em ambos os caches
      await _saveToCache(cacheKey, dataType, data, toJson);

      // 5. Configurar LiveQuery para atualizações futuras
      if (enableLiveQuery && liveQuery != null) {
        _setupLiveQuery(cacheKey, dataType, liveQuery, fromJson, toJson);
      }

      return data;
    } catch (e) {
      debugPrint('❌ [CACHE] Erro em fetchWithCache: $e');

      // Em caso de erro, tentar retornar cache expirado como fallback
      final fallback =
          _getFromMemoryCache<T>(cacheKey, fromJson, ignoreExpiry: true);
      if (fallback != null) {
        debugPrint('🟠 [CACHE] Retornando cache expirado como fallback');
        return fallback;
      }

      rethrow;
    }
  }

  /// Configurar LiveQuery para um endpoint
  void _setupLiveQuery<T>(
    String cacheKey,
    String dataType,
    QueryBuilder<ParseObject> query,
    T Function(Map<String, dynamic>) fromJson,
    Map<String, dynamic> Function(T) toJson,
  ) async {
    try {
      if (_liveQueries.containsKey(cacheKey)) {
        debugPrint('🔄 [LIVEQUERY] Já existe para $cacheKey');
        return;
      }

      debugPrint('🚀 [LIVEQUERY] Configurando para $cacheKey');

      final liveQuery = LiveQuery();
      final subscription = await liveQuery.client.subscribe(query);

      _liveQueries[cacheKey] = LiveQuerySubscription(
        subscription: subscription,
        lastUpdate: DateTime.now(),
      );

      // Handlers para eventos do LiveQuery
      subscription.on(LiveQueryEvent.create, (object) {
        _handleLiveQueryUpdate(
            cacheKey, dataType, 'create', object, fromJson, toJson);
      });

      subscription.on(LiveQueryEvent.update, (object) {
        _handleLiveQueryUpdate(
            cacheKey, dataType, 'update', object, fromJson, toJson);
      });

      subscription.on(LiveQueryEvent.delete, (object) {
        _handleLiveQueryUpdate(
            cacheKey, dataType, 'delete', object, fromJson, toJson);
      });

      subscription.on(LiveQueryEvent.enter, (object) {
        _handleLiveQueryUpdate(
            cacheKey, dataType, 'enter', object, fromJson, toJson);
      });

      subscription.on(LiveQueryEvent.leave, (object) {
        _handleLiveQueryUpdate(
            cacheKey, dataType, 'leave', object, fromJson, toJson);
      });

      debugPrint('✅ [LIVEQUERY] Configurado com sucesso para $cacheKey');
    } catch (e) {
      debugPrint('❌ [LIVEQUERY] Erro ao configurar $cacheKey: $e');
    }
  }

  /// Lidar com atualizações do LiveQuery
  void _handleLiveQueryUpdate<T>(
    String cacheKey,
    String dataType,
    String event,
    ParseObject object,
    T Function(Map<String, dynamic>) fromJson,
    Map<String, dynamic> Function(T) toJson,
  ) {
    try {
      _liveQueryUpdates++;
      debugPrint('🔄 [LIVEQUERY] Evento $event para $cacheKey');

      // Invalidar cache para forçar nova busca na próxima requisição
      _memoryCache.remove(cacheKey);
      _removePersistentCache(cacheKey);

      // Ou implementar lógica mais sofisticada para atualizar o cache
      // baseado no tipo de evento (create, update, delete, etc.)
    } catch (e) {
      debugPrint('❌ [LIVEQUERY] Erro ao processar evento $event: $e');
    }
  }

  /// Obter dados do cache em memória
  T? _getFromMemoryCache<T>(
    String key,
    T Function(Map<String, dynamic>) fromJson, {
    bool ignoreExpiry = false,
  }) {
    final entry = _memoryCache[key];
    if (entry == null) return null;

    if (!ignoreExpiry && entry.isExpired) {
      _memoryCache.remove(key);
      return null;
    }

    try {
      return fromJson(entry.data);
    } catch (e) {
      debugPrint('❌ [CACHE] Erro ao deserializar cache em memória: $e');
      _memoryCache.remove(key);
      return null;
    }
  }

  /// Obter dados do cache persistente
  Future<T?> _getFromPersistentCache<T>(
    String key,
    T Function(Map<String, dynamic>) fromJson,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString('cache_$key');

      if (jsonString == null) return null;

      final cacheData = json.decode(jsonString) as Map<String, dynamic>;
      final timestamp = DateTime.parse(cacheData['timestamp']);
      final ttlMinutes = cacheData['ttl_minutes'] as int;

      // Verificar se expirou
      if (DateTime.now().difference(timestamp).inMinutes > ttlMinutes) {
        prefs.remove('cache_$key');
        return null;
      }

      return fromJson(cacheData['data']);
    } catch (e) {
      debugPrint('❌ [CACHE] Erro ao ler cache persistente: $e');
      return null;
    }
  }

  /// Salvar dados em ambos os caches
  Future<void> _saveToCache<T>(
    String cacheKey,
    String dataType,
    T data,
    Map<String, dynamic> Function(T) toJson,
  ) async {
    try {
      final jsonData = toJson(data);
      final ttl = _ttlConfigs[dataType] ?? const Duration(minutes: 5);

      // Cache em memória
      _memoryCache[cacheKey] = CacheEntry(
        data: jsonData,
        timestamp: DateTime.now(),
        ttl: ttl,
      );

      // Cache persistente
      final prefs = await SharedPreferences.getInstance();
      final cacheData = {
        'data': jsonData,
        'timestamp': DateTime.now().toIso8601String(),
        'ttl_minutes': ttl.inMinutes,
      };

      await prefs.setString('cache_$cacheKey', json.encode(cacheData));

      debugPrint('💾 [CACHE] Salvo: $cacheKey (TTL: ${ttl.inMinutes}min)');
    } catch (e) {
      debugPrint('❌ [CACHE] Erro ao salvar: $e');
    }
  }

  /// Remover do cache persistente
  Future<void> _removePersistentCache(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('cache_$key');
    } catch (e) {
      debugPrint('❌ [CACHE] Erro ao remover cache persistente: $e');
    }
  }

  /// Invalidar cache específico
  void invalidateCache(String key) {
    _memoryCache.remove(key);
    _removePersistentCache(key);
    debugPrint('🗑️ [CACHE] Invalidado: $key');
  }

  /// Invalidar cache por padrão
  void invalidateCachePattern(String pattern) {
    final keysToRemove =
        _memoryCache.keys.where((key) => key.contains(pattern)).toList();
    for (final key in keysToRemove) {
      _memoryCache.remove(key);
      _removePersistentCache(key);
    }
    debugPrint(
        '🗑️ [CACHE] Invalidados ${keysToRemove.length} caches com padrão: $pattern');
  }

  /// Limpar cache expirado
  void cleanExpiredCache() {
    final expiredKeys = _memoryCache.entries
        .where((entry) => entry.value.isExpired)
        .map((entry) => entry.key)
        .toList();

    for (final key in expiredKeys) {
      _memoryCache.remove(key);
      _removePersistentCache(key);
    }

    if (expiredKeys.isNotEmpty) {
      debugPrint('🧹 [CACHE] Removidos ${expiredKeys.length} caches expirados');
    }
  }

  /// Fechar todas as conexões LiveQuery
  void closeAllLiveQueries() {
    for (final entry in _liveQueries.entries) {
      try {
        LiveQuery().client.unSubscribe(entry.value.subscription);
        debugPrint('🔌 [LIVEQUERY] Fechado: ${entry.key}');
      } catch (e) {
        debugPrint('❌ [LIVEQUERY] Erro ao fechar ${entry.key}: $e');
      }
    }
    _liveQueries.clear();
  }

  /// Estatísticas do cache
  Map<String, dynamic> getStats() {
    final total = _hits + _misses;
    final hitRate =
        total > 0 ? (_hits / total * 100).toStringAsFixed(1) : '0.0';

    return {
      'cache_hits': _hits,
      'cache_misses': _misses,
      'hit_rate': '$hitRate%',
      'memory_cache_size': _memoryCache.length,
      'livequery_subscriptions': _liveQueries.length,
      'livequery_updates': _liveQueryUpdates,
    };
  }

  /// Limpar tudo
  void clearAll() {
    _memoryCache.clear();
    closeAllLiveQueries();
    _hits = 0;
    _misses = 0;
    _liveQueryUpdates = 0;
    debugPrint('🧹 [CACHE] Cache limpo completamente');
  }
}

/// Entrada do cache em memória
class CacheEntry {
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final Duration ttl;

  CacheEntry({
    required this.data,
    required this.timestamp,
    required this.ttl,
  });

  bool get isExpired => DateTime.now().difference(timestamp) > ttl;
}

/// Subscription do LiveQuery
class LiveQuerySubscription {
  final Subscription subscription;
  final DateTime lastUpdate;

  LiveQuerySubscription({
    required this.subscription,
    required this.lastUpdate,
  });
}
