import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Sistema de paginação inteligente com lazy loading
class SmartPagination<T> extends GetxController {
  final RxList<T> items = <T>[].obs;
  final RxBool isLoading = false.obs;
  final RxBool isLoadingMore = false.obs;
  final RxBool hasMore = true.obs;
  final RxString error = ''.obs;

  int _currentPage = 0;
  final int pageSize;
  final Future<List<T>> Function(int page, int limit) fetchData;
  final ScrollController scrollController = ScrollController();

  SmartPagination({
    required this.fetchData,
    this.pageSize = 20,
  }) {
    _setupScrollListener();
  }

  void _setupScrollListener() {
    scrollController.addListener(() {
      // Trigger load more when 80% scrolled
      if (scrollController.position.pixels >=
          scrollController.position.maxScrollExtent * 0.8) {
        if (!isLoadingMore.value && hasMore.value) {
          loadMore();
        }
      }
    });
  }

  /// Carrega primeira página
  Future<void> loadInitial() async {
    if (isLoading.value) return;

    try {
      isLoading.value = true;
      error.value = '';
      _currentPage = 0;

      final newItems = await fetchData(_currentPage, pageSize);

      items.value = newItems;
      hasMore.value = newItems.length >= pageSize;
      _currentPage++;
    } catch (e) {
      error.value = e.toString();
      debugPrint('Erro ao carregar página inicial: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// Carrega próxima página
  Future<void> loadMore() async {
    if (isLoadingMore.value || !hasMore.value) return;

    try {
      isLoadingMore.value = true;

      final newItems = await fetchData(_currentPage, pageSize);

      if (newItems.isNotEmpty) {
        items.addAll(newItems);
        hasMore.value = newItems.length >= pageSize;
        _currentPage++;
      } else {
        hasMore.value = false;
      }
    } catch (e) {
      error.value = e.toString();
      debugPrint('Erro ao carregar mais itens: $e');
    } finally {
      isLoadingMore.value = false;
    }
  }

  /// Refresh completo
  Future<void> refresh() async {
    items.clear();
    await loadInitial();
  }

  /// Reset da paginação
  void reset() {
    items.clear();
    _currentPage = 0;
    hasMore.value = true;
    error.value = '';
  }

  @override
  void onClose() {
    scrollController.dispose();
    super.onClose();
  }
}

/// Widget para lista paginada inteligente
class SmartPaginatedListView<T> extends StatelessWidget {
  final SmartPagination<T> pagination;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final Widget? loadingWidget;
  final Widget? errorWidget;
  final Widget? emptyWidget;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const SmartPaginatedListView({
    super.key,
    required this.pagination,
    required this.itemBuilder,
    this.loadingWidget,
    this.errorWidget,
    this.emptyWidget,
    this.shrinkWrap = false,
    this.physics,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // Loading inicial
      if (pagination.isLoading.value && pagination.items.isEmpty) {
        return loadingWidget ??
            const Center(child: CircularProgressIndicator());
      }

      // Estado de erro
      if (pagination.error.value.isNotEmpty && pagination.items.isEmpty) {
        return errorWidget ??
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error, size: 64, color: Colors.grey),
                  const SizedBox(height: 16),
                  Text(
                    'Erro: ${pagination.error.value}',
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: pagination.loadInitial,
                    child: const Text('Tentar Novamente'),
                  ),
                ],
              ),
            );
      }

      // Lista vazia
      if (pagination.items.isEmpty) {
        return emptyWidget ??
            const Center(
              child: Text('Nenhum item encontrado'),
            );
      }

      // Lista com itens
      return ListView.builder(
        controller: pagination.scrollController,
        shrinkWrap: shrinkWrap,
        physics: physics,
        itemCount: pagination.items.length + (pagination.hasMore.value ? 1 : 0),
        itemBuilder: (context, index) {
          // Item normal da lista
          if (index < pagination.items.length) {
            return itemBuilder(context, pagination.items[index], index);
          }

          // Loading indicator para próxima página
          return Container(
            padding: const EdgeInsets.all(16),
            alignment: Alignment.center,
            child: pagination.isLoadingMore.value
                ? const CircularProgressIndicator()
                : const SizedBox.shrink(),
          );
        },
      );
    });
  }
}

/// Sistema de virtual scrolling para listas muito grandes
class VirtualScrollList<T> extends StatefulWidget {
  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final double itemHeight;
  final int visibleItemCount;

  const VirtualScrollList({
    super.key,
    required this.items,
    required this.itemBuilder,
    required this.itemHeight,
    this.visibleItemCount = 20,
  });

  @override
  State<VirtualScrollList<T>> createState() => _VirtualScrollListState<T>();
}

class _VirtualScrollListState<T> extends State<VirtualScrollList<T>> {
  late ScrollController _scrollController;
  int _firstVisibleIndex = 0;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    final newFirstIndex =
        (_scrollController.offset / widget.itemHeight).floor();

    if (newFirstIndex != _firstVisibleIndex) {
      setState(() {
        _firstVisibleIndex = newFirstIndex.clamp(0, widget.items.length - 1);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final visibleItems = _getVisibleItems();

    return CustomScrollView(
      controller: _scrollController,
      slivers: [
        // Espaço antes dos itens visíveis
        SliverToBoxAdapter(
          child: SizedBox(
            height: _firstVisibleIndex * widget.itemHeight,
          ),
        ),

        // Itens visíveis
        SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              final itemIndex = _firstVisibleIndex + index;
              return SizedBox(
                height: widget.itemHeight,
                child: widget.itemBuilder(
                  context,
                  widget.items[itemIndex],
                  itemIndex,
                ),
              );
            },
            childCount: visibleItems.length,
          ),
        ),

        // Espaço após os itens visíveis
        SliverToBoxAdapter(
          child: SizedBox(
            height: (widget.items.length -
                    _firstVisibleIndex -
                    visibleItems.length) *
                widget.itemHeight,
          ),
        ),
      ],
    );
  }

  List<T> _getVisibleItems() {
    final lastIndex = (_firstVisibleIndex + widget.visibleItemCount)
        .clamp(0, widget.items.length);

    return widget.items.sublist(_firstVisibleIndex, lastIndex);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}
