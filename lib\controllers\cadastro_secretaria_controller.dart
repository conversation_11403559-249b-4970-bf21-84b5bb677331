import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:uuid/uuid.dart';
import 'package:fila_app/utils/date_utils.dart';

class CadastroSecretariaController extends GetxController {
  final RxBool isLoading = false.obs;
  final RxString error = ''.obs;
  final RxString successMessage = ''.obs;
  final RxList<ParseObject> secretarias = <ParseObject>[].obs;
  ParseObject? currentHospital;

  // Controllers para o formulário
  final nomeController = TextEditingController();
  final emailController = TextEditingController();
  final telefoneController = TextEditingController();
  final cpfController = TextEditingController();
  final senhaController = TextEditingController();
  final RxBool ativo = true.obs;

  @override
  void onInit() {
    super.onInit();
    carregarDadosHospital();
    gerarSenhaPadrao();
  }

  @override
  void onClose() {
    nomeController.dispose();
    emailController.dispose();
    telefoneController.dispose();
    cpfController.dispose();
    senhaController.dispose();
    super.onClose();
  }

  // Gerar senha aleatória para facilitar cadastro
  void gerarSenhaPadrao() {
    final uuid = const Uuid().v4();
    senhaController.text = uuid.substring(0, 8);
  }

  Future<void> carregarDadosHospital() async {
    try {
      isLoading.value = true;
      error.value = '';

      final currentUser = await ParseUser.currentUser() as ParseUser?;
      if (currentUser == null) throw Exception('Usuário não encontrado');

      final queryConsultorio =
          QueryBuilder<ParseObject>(ParseObject('consultorio'))
            ..whereEqualTo('user_consultorio', currentUser.toPointer());

      final response = await queryConsultorio.query();

      if (!response.success ||
          response.results == null ||
          response.results!.isEmpty) {
        throw Exception('Hospital não encontrado');
      }

      currentHospital = response.results!.first;
      debugPrint("Hospital encontrado: ${currentHospital?.objectId}");
      await carregarSecretarias();
    } catch (e) {
      error.value = e.toString();
      debugPrint('Erro ao carregar dados do hospital: $e');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> carregarSecretarias() async {
    try {
      if (currentHospital == null) {
        await carregarDadosHospital();
        if (currentHospital == null) return;
      }

      isLoading.value = true;

      final querySecretarias =
          QueryBuilder<ParseObject>(ParseObject('Secretaria'))
            ..whereEqualTo('consultorio', currentHospital)
            ..includeObject(['user_secretaria']);

      final response = await querySecretarias.query();

      if (response.success && response.results != null) {
        secretarias.value = response.results!.cast<ParseObject>();
        debugPrint("Secretárias carregadas: ${secretarias.length}");
      } else {
        secretarias.clear();
        debugPrint("Nenhuma secretária encontrada");
      }
    } catch (e) {
      error.value = 'Erro ao carregar secretárias: $e';
      debugPrint(error.value);
    } finally {
      isLoading.value = false;
    }
  }

  void limparFormulario() {
    nomeController.clear();
    emailController.clear();
    telefoneController.clear();
    cpfController.clear();
    gerarSenhaPadrao();
    ativo.value = true;
  }

  Future<bool> cadastrarSecretaria() async {
    try {
      if (currentHospital == null) {
        await carregarDadosHospital();
        if (currentHospital == null) {
          error.value = 'Hospital não encontrado';
          return false;
        }
      }

      // Imprimir informações de diagnóstico
      debugPrint("====== INICIANDO CADASTRO DE SECRETÁRIA ======");
      debugPrint("Hospital ID: ${currentHospital?.objectId}");
      debugPrint("Nome: ${nomeController.text}");
      debugPrint("Email: ${emailController.text}");
      debugPrint("CPF: ${cpfController.text}");

      isLoading.value = true;
      error.value = '';

      // Validação mais rigorosa dos campos
      if (nomeController.text.trim().isEmpty) {
        error.value = 'O nome é obrigatório';
        return false;
      }

      if (emailController.text.trim().isEmpty) {
        error.value = 'O email é obrigatório';
        return false;
      }

      if (!GetUtils.isEmail(emailController.text.trim())) {
        error.value = 'Digite um email válido';
        return false;
      }

      if (senhaController.text.trim().isEmpty) {
        error.value = 'A senha é obrigatória';
        return false;
      }

      if (cpfController.text.trim().isEmpty) {
        error.value = 'O CPF é obrigatório';
        return false;
      }

      // Verificar se o e-mail já está em uso
      final queryEmail = QueryBuilder<ParseUser>(ParseUser.forQuery())
        ..whereEqualTo('email', emailController.text.trim());

      final emailResponse = await queryEmail.query();

      if (emailResponse.success &&
          emailResponse.results != null &&
          emailResponse.results!.isNotEmpty) {
        error.value = 'Este e-mail já está em uso';
        return false;
      }

      // Criar usuário no Parse - garantindo que o email seja definido corretamente
      final String email = emailController.text.trim();
      final user = ParseUser(
        email, // username
        senhaController.text.trim(), // password
        email, // email
      );
      // Confirmar que o email está realmente definido
      user.emailAddress = email;
      user.set('email', email);
      user.set('tipo', 'secretaria');
      user.set<DateTime>('dataCadastro', TimezoneUtils.nowForBackend());

      final userResult = await user.signUp();

      if (!userResult.success) {
        throw Exception(userResult.error?.message ?? 'Erro ao criar usuário');
      }

      debugPrint("Usuário criado com sucesso: ${user.objectId}");

      // Criar o perfil da secretária com todos os campos necessários
      final secretaria = ParseObject('Secretaria');

      // Definir campos obrigatórios conforme o schema
      secretaria.set('nome', nomeController.text.trim());
      secretaria.set(
          'email',
          emailController.text
              .trim()); // Mantemos um campo de e-mail específico
      secretaria.set('telefone', telefoneController.text.trim());
      secretaria.set('ativo', ativo.value);
      secretaria.set('consultorio', currentHospital);
      secretaria.set<DateTime>('dataCadastro', TimezoneUtils.nowForBackend());
      secretaria.set<ParseUser>('user_secretaria', user);

      // Adicionar o CPF para completude dos dados
      if (cpfController.text.isNotEmpty) {
        secretaria.set('cpf', cpfController.text.trim());
      }

      debugPrint(
          "Criando perfil de secretária com campos: ${secretaria.toJson()}");

      final secretariaResult = await secretaria.save();

      if (!secretariaResult.success) {
        // Se falhar, tentar excluir o usuário criado para evitar inconsistências
        await user.destroy();
        throw Exception(secretariaResult.error?.message ??
            'Erro ao criar perfil de secretária');
      }

      debugPrint("Secretária criada com sucesso: ${secretaria.objectId}");

      // NOVO: Criar a relação bidirecional - usuário apontando para secretária
      try {
        // Criar a relação do usuário para a secretária
        final userSecretariaRelation =
            user.getRelation<ParseObject>('user_secretaria');
        userSecretariaRelation.add(secretaria);
        await user.save();
        debugPrint('Relação bidirecional criada com sucesso');
      } catch (relationError) {
        debugPrint(
            'Erro ao criar relação bidirecional: $relationError - continuando mesmo assim');
        // Não interromper o fluxo, já que a secretária foi criada com sucesso
      }

      // Limpar campos do formulário
      limparFormulario();

      // Recarregar lista de secretárias
      await carregarSecretarias();

      successMessage.value = 'Secretária cadastrada com sucesso!';
      return true;
    } catch (e) {
      error.value = e.toString().replaceAll('Exception: ', '');
      debugPrint('Erro ao cadastrar secretária: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<bool> atualizarStatusSecretaria(
      String secretariaId, bool novoStatus) async {
    try {
      isLoading.value = true;

      final secretaria = ParseObject('Secretaria')
        ..objectId = secretariaId
        ..set('ativo', novoStatus);

      final result = await secretaria.save();

      if (result.success) {
        // Atualizar a lista local
        final index = secretarias.indexWhere((s) => s.objectId == secretariaId);
        if (index >= 0) {
          secretarias[index].set('ativo', novoStatus);
          secretarias.refresh();
        }

        successMessage.value = novoStatus
            ? 'Secretária ativada com sucesso!'
            : 'Secretária desativada com sucesso!';
        return true;
      } else {
        throw Exception(result.error?.message ?? 'Erro ao atualizar status');
      }
    } catch (e) {
      error.value = e.toString().replaceAll('Exception: ', '');
      debugPrint('Erro ao atualizar status: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<bool> reenviarCredenciais(String secretariaId) async {
    try {
      isLoading.value = true;
      error.value = '';

      // Buscar a secretária com o usuário
      final querySecretaria =
          QueryBuilder<ParseObject>(ParseObject('Secretaria'))
            ..whereEqualTo('objectId', secretariaId)
            ..includeObject(['user_secretaria']);

      final response = await querySecretaria.query();

      if (!response.success ||
          response.results == null ||
          response.results!.isEmpty) {
        throw Exception('Secretária não encontrada');
      }

      final secretaria = response.results!.first;
      final user = secretaria.get<ParseUser>('user_secretaria');

      if (user == null) {
        throw Exception('Usuário(a) do(a) secretário(a) não encontrado(a)');
      }

      // Enviar email de redefinição de senha
      final resetRequest = ParseUser(user.username, null, user.emailAddress);
      final resetResponse = await resetRequest.requestPasswordReset();

      if (!resetResponse.success) {
        throw Exception(resetResponse.error?.message ??
            'Erro ao enviar email de redefinição');
      }

      successMessage.value =
          'Email de redefinição de senha enviado com sucesso!';
      return true;
    } catch (e) {
      error.value = e.toString().replaceAll('Exception: ', '');
      debugPrint('Erro ao reenviar credenciais: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<bool> removerSecretaria(String secretariaId) async {
    try {
      isLoading.value = true;
      error.value = '';
      debugPrint('=== INICIANDO PROCESSO DE EXCLUSÃO DA SECRETÁRIA ===');
      debugPrint('ID da secretária a ser excluída: $secretariaId');

      // Usar a nova Cloud Function que remove secretária e usuário em uma única operação
      debugPrint('Tentando exclusão unificada via excluirSecretariaComUsuario');
      final cloudFunction = ParseCloudFunction('excluirSecretariaComUsuario');
      final result = await cloudFunction
          .execute(parameters: {'secretariaId': secretariaId});

      if (result.success) {
        debugPrint('Exclusão realizada com sucesso: ${result.result}');

        // Verificar se houve remoção parcial (apenas secretária, não usuário)
        if (result.result != null && result.result['partial'] == true) {
          debugPrint(
              'ATENÇÃO: Apenas a secretária foi removida, o usuário pode ainda existir');
        }

        // Recarregar lista de secretárias
        await carregarSecretarias();

        successMessage.value = 'Secretária removida com sucesso!';
        return true;
      } else {
        // Se a Cloud Function falhar, usar o método tradicional como fallback
        debugPrint('Falha na função unificada: ${result.error?.message}');
        debugPrint('Tentando método tradicional como fallback...');

        return await _removerSecretariaMetodoTradicional(secretariaId);
      }
    } catch (e) {
      // Se houver erro na Cloud Function, tentar método tradicional
      debugPrint('Erro na função unificada: $e');
      debugPrint('Tentando método tradicional como fallback...');

      try {
        return await _removerSecretariaMetodoTradicional(secretariaId);
      } catch (e2) {
        error.value = e2.toString().replaceAll('Exception: ', '');
        debugPrint('ERRO FINAL: $e2');
        return false;
      }
    } finally {
      isLoading.value = false;
      debugPrint('=== PROCESSO DE EXCLUSÃO FINALIZADO ===');
    }
  }

  // Método tradicional de remoção como fallback
  Future<bool> _removerSecretariaMetodoTradicional(String secretariaId) async {
    try {
      debugPrint('Executando método tradicional de remoção...');

      // 1. Buscar a secretária com o usuário associado
      final querySecretaria =
          QueryBuilder<ParseObject>(ParseObject('Secretaria'))
            ..whereEqualTo('objectId', secretariaId)
            ..includeObject(['user_secretaria']);

      final response = await querySecretaria.query();

      if (!response.success ||
          response.results == null ||
          response.results!.isEmpty) {
        throw Exception('Secretária não encontrada');
      }

      final secretaria = response.results!.first;
      final userPointer = secretaria.get<ParseUser>('user_secretaria');
      String? userId = userPointer?.objectId;

      // Fazer backup dos dados do usuário antes da exclusão
      String? userEmail = userPointer?.emailAddress ??
          userPointer?.get<String>('email') ??
          userPointer?.username;
      debugPrint('Email do usuário para verificação: $userEmail');

      // 2. Remover o objeto Secretaria
      debugPrint('Removendo objeto Secretaria...');
      final secretariaResult = await secretaria.delete();

      if (!secretariaResult.success) {
        throw Exception(
            'Erro ao excluir secretária: ${secretariaResult.error?.message}');
      }

      debugPrint('Objeto Secretaria excluído com sucesso');

      // 3. Se temos um ID de usuário, remover o usuário da classe _User
      if (userId != null) {
        try {
          // Tentar excluirUsuarioRaw primeiro (método mais eficaz para Back4App)
          debugPrint('ESTRATÉGIA 1: Tentando exclusão com excluirUsuarioRaw');
          final rawFunction = ParseCloudFunction('excluirUsuarioRaw');
          final rawResult =
              await rawFunction.execute(parameters: {'userId': userId});

          if (rawResult.success) {
            debugPrint('SUCESSO! Usuário excluído com excluirUsuarioRaw');
          } else {
            debugPrint('FALHA na ESTRATÉGIA 1: ${rawResult.error?.message}');

            // Tentar excluirUsuarioCascade
            debugPrint('ESTRATÉGIA 2: Tentando com excluirUsuarioCascade');
            final cascadeFunction = ParseCloudFunction('excluirUsuarioCascade');
            final cascadeResult =
                await cascadeFunction.execute(parameters: {'userId': userId});

            if (cascadeResult.success) {
              debugPrint('SUCESSO! Usuário excluído com excluirUsuarioCascade');
            } else {
              debugPrint(
                  'FALHA na ESTRATÉGIA 2: ${cascadeResult.error?.message}');

              // Tentar com excluirUsuarioMasterKey
              debugPrint('ESTRATÉGIA 3: Tentando com excluirUsuarioMasterKey');
              final masterKeyFunction =
                  ParseCloudFunction('excluirUsuarioMasterKey');
              final masterKeyResult = await masterKeyFunction
                  .execute(parameters: {'userId': userId});

              if (masterKeyResult.success) {
                debugPrint(
                    'SUCESSO! Usuário excluído com excluirUsuarioMasterKey');
              } else {
                debugPrint(
                    'FALHA na ESTRATÉGIA 3: ${masterKeyResult.error?.message}');

                // Última tentativa: exclusão direta
                debugPrint(
                    'ESTRATÉGIA 4: Tentando exclusão direta do objeto _User');
                final userObject = ParseObject('_User')..objectId = userId;
                final directResult = await userObject.delete();

                if (directResult.success) {
                  debugPrint('SUCESSO! Usuário excluído com método direto');
                } else {
                  debugPrint(
                      'FALHA na ESTRATÉGIA 4: ${directResult.error?.message}');
                  // Mesmo falhando, vamos continuar com o processo
                  debugPrint(
                      'Continuando após tentativas de exclusão do usuário');
                }
              }
            }
          }
        } catch (e) {
          debugPrint('ERRO NAS TENTATIVAS DE EXCLUSÃO: $e');
          // Continuar mesmo se falhar a exclusão do usuário, pelo menos a secretária foi removida
        }

        // Verificação para confirmar se o usuário foi realmente excluído
        try {
          debugPrint('Verificando se o usuário foi efetivamente excluído...');
          final verifyQuery = QueryBuilder<ParseUser>(ParseUser.forQuery())
            ..whereEqualTo('objectId', userId);

          final verifyResult = await verifyQuery.query();
          if (verifyResult.success &&
              verifyResult.results != null &&
              verifyResult.results!.isNotEmpty) {
            debugPrint('ATENÇÃO: O usuário ainda existe na base! ID: $userId');
          } else {
            debugPrint(
                'SUCESSO na verificação: Usuário não encontrado na base');
          }

          // Verificar também por email
          if (userEmail != null && userEmail.isNotEmpty) {
            final verifyEmailQuery =
                QueryBuilder<ParseUser>(ParseUser.forQuery())
                  ..whereEqualTo('email', userEmail);

            final verifyEmailResult = await verifyEmailQuery.query();
            if (verifyEmailResult.success &&
                verifyEmailResult.results != null &&
                verifyEmailResult.results!.isNotEmpty) {
              debugPrint(
                  'ATENÇÃO: Ainda existe usuário com o email $userEmail');
            } else {
              debugPrint(
                  'SUCESSO na verificação por email: Nenhum usuário encontrado com email $userEmail');
            }
          }
        } catch (e) {
          debugPrint('Erro na verificação pós-exclusão: $e');
        }
      }

      // 4. Recarregar lista de secretárias
      await carregarSecretarias();

      successMessage.value = 'Secretária removida com sucesso!';
      return true;
    } catch (e) {
      error.value = e.toString().replaceAll('Exception: ', '');
      debugPrint('ERRO NO MÉTODO TRADICIONAL: $e');
      return false;
    }
  }
}
