# Gradle properties for Flutter with Java support - OTIMIZADO PARA PERFORMANCE

# ===== CONFIGURAÇÕES DE MEMÓRIA E JVM =====
# Configurações movidas para seção JAVA abaixo para evitar duplicação

# Java home path - usa configuração automática do sistema via JAVA_HOME
# org.gradle.java.home removido para compatibilidade cross-platform

# ===== CONFIGURAÇÕES DE PERFORMANCE OTIMIZADAS PARA RYZEN 7 5700X =====
# Daemon para manter o Gradle em memória
org.gradle.daemon=true

# Cache para reutilizar builds anteriores
org.gradle.caching=true

# Paralelismo para usar múltiplos cores
org.gradle.parallel=true

# Configure on demand para builds mais rápidos
org.gradle.configureondemand=true

# Máximo de workers para usar todos os cores (8 cores = 8 workers)
org.gradle.workers.max=8

# File system watching para detecção rápida de mudanças
org.gradle.vfs.watch=true

# Build cache local para acelerar rebuilds
org.gradle.unsafe.configuration-cache=true

# ===== CONFIGURAÇÕES DO KOTLIN OTIMIZADAS =====
# Desativar validação estrita do JVM target
kotlin.jvm.target.validation.mode=warning

# Kotlin incremental compilation
kotlin.incremental=true

# Kotlin incremental compilation para Android
kotlin.incremental.android=true

# Kotlin incremental compilation para JVM
kotlin.incremental.jvm=true

# Kotlin code style
kotlin.code.style=official

# Kotlin compiler daemon para reutilizar compilações
kotlin.compiler.execution.strategy=daemon

# Kotlin parallel compilation
kotlin.parallel.tasks.in.project=true

# ===== CONFIGURAÇÕES DO ANDROID OTIMIZADAS =====
# AndroidX support
android.useAndroidX=true
android.enableJetifier=true

# Non-final resource IDs para bibliotecas
android.nonFinalResIds=false

# Build config (corrigido para não usar deprecated)
android.buildFeatures.buildConfig=true

# New recommended option
android.useFullClasspathForDexingTransform=true

# R8 optimizations para builds mais rápidos
android.enableR8.fullMode=true

# Incremental annotation processing
android.enableSeparateAnnotationProcessing=true

# Incremental dexing para builds incrementais mais rápidos
android.enableIncrementalDesugaring=true

# Build cache para Android
android.enableBuildCache=true

# ===== CONFIGURAÇÕES DE REDE =====
# Timeout para downloads mais rápidos
systemProp.org.gradle.internal.http.connectionTimeout=60000
systemProp.org.gradle.internal.http.socketTimeout=60000

# ===== CONFIGURAÇÕES JAVA =====
# Usar o JDK 21 que está instalado (Eclipse Adoptium)
org.gradle.java.home=C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot

# Configurações JVM OTIMIZADAS
org.gradle.jvmargs=-Xmx6144m -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -XX:+UseG1GC -XX:+ExplicitGCInvokesConcurrent -XX:G1HeapRegionSize=16m -XX:G1NewSizePercent=23 -XX:G1MaxNewSizePercent=40 -XX:+UnlockExperimentalVMOptions -XX:+UseJVMCICompiler --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED