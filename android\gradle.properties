# Gradle properties for Flutter with Java support - OTIMIZADO PARA PERFORMANCE

# ===== CONFIGURAÇÕES DE MEMÓRIA E JVM =====
# Otimizado para 3GB para balancear performance e estabilidade
org.gradle.jvmargs=-Xmx3072m -XX:MaxMetaspaceSize=768m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -XX:+UseG1GC -XX:+ExplicitGCInvokesConcurrent --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED

# Java home path - usa configuração automática do sistema via JAVA_HOME
# org.gradle.java.home removido para compatibilidade cross-platform

# ===== CONFIGURAÇÕES DE PERFORMANCE ESTÁVEIS =====
# Daemon para manter o Gradle em memória
org.gradle.daemon=true

# Cache para reutilizar builds anteriores
org.gradle.caching=true

# Paralelismo para usar múltiplos cores
org.gradle.parallel=true

# Configure on demand para builds mais rápidos
org.gradle.configureondemand=true

# Número moderado de workers para estabilidade
org.gradle.workers.max=2

# ===== CONFIGURAÇÕES DO KOTLIN =====
# Desativar validação estrita do JVM target
kotlin.jvm.target.validation.mode=warning

# Kotlin incremental compilation
kotlin.incremental=true

# Kotlin code style
kotlin.code.style=official

# ===== CONFIGURAÇÕES DO ANDROID =====
# AndroidX support
android.useAndroidX=true
android.enableJetifier=true

# Non-final resource IDs para bibliotecas
android.nonFinalResIds=false

# Build config (corrigido para não usar deprecated)
android.buildFeatures.buildConfig=true

# New recommended option
android.useFullClasspathForDexingTransform=true

# ===== CONFIGURAÇÕES DE REDE =====
# Timeout para downloads mais rápidos
systemProp.org.gradle.internal.http.connectionTimeout=60000
systemProp.org.gradle.internal.http.socketTimeout=60000