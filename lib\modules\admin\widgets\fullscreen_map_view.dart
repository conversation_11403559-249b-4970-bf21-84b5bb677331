import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:fila_app/theme/theme.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';

class FullscreenMapView extends StatefulWidget {
  final double? initialLatitude;
  final double? initialLongitude;
  final Function(double lat, double lng) onLocationSelected;
  final String title;

  const FullscreenMapView({
    super.key,
    this.initialLatitude,
    this.initialLongitude,
    required this.onLocationSelected,
    this.title = 'Selecionar Localização',
  });

  @override
  State<FullscreenMapView> createState() => _FullscreenMapViewState();
}

class _FullscreenMapViewState extends State<FullscreenMapView> {
  GoogleMapController? _mapController;
  Set<Marker> _markers = {};
  LatLng? _selectedLocation;
  bool _isGettingLocation = false;

  @override
  void initState() {
    super.initState();
    _initializeLocation();
  }

  void _initializeLocation() {
    if (widget.initialLatitude != null && widget.initialLongitude != null) {
      // Validação anti-NaN
      if (!widget.initialLatitude!.isNaN &&
          !widget.initialLongitude!.isNaN &&
          widget.initialLatitude!.isFinite &&
          widget.initialLongitude!.isFinite) {
        setState(() {
          _selectedLocation =
              LatLng(widget.initialLatitude!, widget.initialLongitude!);
          _updateMarkers();
        });
      }
    }
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
    _centerMap();
  }

  void _centerMap() {
    if (_mapController != null && _selectedLocation != null) {
      _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(_selectedLocation!, 16),
      );
    }
  }

  void _onMapTap(LatLng position) {
    // Validação anti-NaN
    if (!position.latitude.isNaN &&
        !position.longitude.isNaN &&
        position.latitude.isFinite &&
        position.longitude.isFinite) {
      setState(() {
        _selectedLocation = position;
        _updateMarkers();
      });

      widget.onLocationSelected(position.latitude, position.longitude);
    }
  }

  void _updateMarkers() {
    if (_selectedLocation != null) {
      setState(() {
        _markers = {
          Marker(
            markerId: const MarkerId('selected_location'),
            position: _selectedLocation!,
            draggable: true,
            icon:
                BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
            infoWindow: InfoWindow(
              title: 'Localização Selecionada',
              snippet: 'Toque em "Confirmar" para usar esta localização',
            ),
            onDragEnd: (newPosition) {
              // Validação anti-NaN
              if (!newPosition.latitude.isNaN &&
                  !newPosition.longitude.isNaN &&
                  newPosition.latitude.isFinite &&
                  newPosition.longitude.isFinite) {
                setState(() {
                  _selectedLocation = newPosition;
                });
                widget.onLocationSelected(
                    newPosition.latitude, newPosition.longitude);
              }
            },
          ),
        };
      });
    } else {
      setState(() {
        _markers = {};
      });
    }
  }

  Future<void> _getCurrentLocation() async {
    setState(() {
      _isGettingLocation = true;
    });

    try {
      // Verificar permissões
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          _showMessage('Permissão de localização negada', isError: true);
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _showMessage('Permissão de localização negada permanentemente',
            isError: true);
        return;
      }

      // Verificar se GPS está habilitado
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        _showMessage('Por favor, ative o GPS', isError: true);
        return;
      }

      // Obter localização atual
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      // Validar coordenadas obtidas
      if (!position.latitude.isNaN &&
          !position.longitude.isNaN &&
          position.latitude.isFinite &&
          position.longitude.isFinite) {
        setState(() {
          _selectedLocation = LatLng(position.latitude, position.longitude);
          _updateMarkers();
        });

        _centerMap();
        widget.onLocationSelected(position.latitude, position.longitude);
        _showMessage('Localização obtida com sucesso!');
      } else {
        _showMessage('Coordenadas inválidas obtidas', isError: true);
      }
    } catch (e) {
      _showMessage('Erro ao obter localização: ${e.toString()}', isError: true);
    } finally {
      setState(() {
        _isGettingLocation = false;
      });
    }
  }

  void _confirmLocation() {
    if (_selectedLocation != null) {
      widget.onLocationSelected(
          _selectedLocation!.latitude, _selectedLocation!.longitude);
      Get.back();
      _showMessage('Localização confirmada!');
    } else {
      _showMessage('Por favor, selecione uma localização no mapa',
          isError: true);
    }
  }

  void _showMessage(String message, {bool isError = false}) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          if (_selectedLocation != null)
            TextButton.icon(
              onPressed: _confirmLocation,
              icon: const Icon(Icons.check, color: Colors.white),
              label: const Text(
                'Confirmar',
                style: TextStyle(color: Colors.white),
              ),
            ),
        ],
      ),
      body: Stack(
        children: [
          // Google Map ocupando toda a tela
          GoogleMap(
            onMapCreated: _onMapCreated,
            initialCameraPosition: CameraPosition(
              target: _selectedLocation ?? const LatLng(-15.827, -47.921),
              zoom: _selectedLocation != null ? 16 : 5,
            ),
            markers: _markers,
            onTap: _onMapTap,
            mapType: MapType.normal,
            compassEnabled: true,
            myLocationEnabled: false,
            myLocationButtonEnabled: false,
            zoomControlsEnabled: true,
            mapToolbarEnabled: false,
            scrollGesturesEnabled: true,
            zoomGesturesEnabled: true,
            rotateGesturesEnabled: true,
            tiltGesturesEnabled: true,
          ),

          // Informações da localização selecionada
          if (_selectedLocation != null)
            Positioned(
              bottom: 100,
              left: 16,
              right: 16,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.95),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.location_on,
                          color: AppTheme.primaryColor,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          'Localização Selecionada',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            'Latitude: ${_selectedLocation!.latitude.toStringAsFixed(6)}',
                            style: const TextStyle(fontSize: 12),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Text(
                            'Longitude: ${_selectedLocation!.longitude.toStringAsFixed(6)}',
                            style: const TextStyle(fontSize: 12),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

          // Instruções se nenhuma localização estiver selecionada
          if (_selectedLocation == null)
            Positioned(
              bottom: 100,
              left: 16,
              right: 16,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.9),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'Toque no mapa para selecionar uma localização',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),

          // Loading overlay
          if (_isGettingLocation)
            Positioned.fill(
              child: Container(
                color: Colors.black.withOpacity(0.3),
                child: const Center(
                  child: Card(
                    child: Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 16),
                          Text('Obtendo localização atual...')
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        heroTag: "btn_my_location",
        onPressed: _isGettingLocation ? null : _getCurrentLocation,
        backgroundColor:
            _isGettingLocation ? Colors.grey : AppTheme.primaryColor,
        child: _isGettingLocation
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Icon(Icons.my_location, color: Colors.white),
        tooltip: 'Minha localização',
      ),
    );
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }
}
