// lib/views/tela_inicial_paciente.dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:fila_app/views/gradient_background.dart';
import '../controllers/paciente_controller.dart';
import '../widgets/user_data_dialog.dart';
import '../services/push_notification_service.dart';
import 'dart:convert';

class TelaInicialPaciente extends StatefulWidget {
  const TelaInicialPaciente({super.key});

  @override
  State<TelaInicialPaciente> createState() => _TelaInicialPacienteState();
}

class _TelaInicialPacienteState extends State<TelaInicialPaciente>
    with TickerProviderStateMixin {
  final PacienteController controller = Get.find<PacienteController>();
  late AnimationController _animationController;
  late AnimationController _pulseController;
  late AnimationController _slideController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    // Animação principal de escala
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Animação de pulso para o botão
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    // Animação de slide para os elementos
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _scaleAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeIn,
    );

    // Iniciar animações
    _animationController.forward();
    _slideController.forward();
    _pulseController.repeat(reverse: true);

    // Verificar dados do usuário após o frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkUserData();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pulseController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  Future<void> _checkUserData() async {
    final userDataController = controller.userDataController;

    // Verificar se já temos dados carregados, caso contrário inicializar
    if (!userDataController.userDataExists.value) {
      // Primeiro tentar carregar dados existentes sem reinicializar tudo
      final userData = await userDataController.getUserData();
      if (userData != null) {
        userDataController.nomeController.text = userData['nome'] ?? '';
        userDataController.telefoneController.text = userData['telefone'] ?? '';
        userDataController.formattedUserId.value = userData['userId'] ?? '';
        userDataController.userDataExists.value = true;
        debugPrint('Dados do usuário carregados da memória local');
      } else {
        // Se não há dados, inicializar do zero
        await userDataController.checkUserData();
      }
    }

    // Se ainda não há dados, mostrar o diálogo
    if (!userDataController.userDataExists.value) {
      Get.dialog(
        UserDataDialog(
          controller: userDataController,
          onComplete: () {
            // Apenas fecha o diálogo e permanece na tela do paciente
            Get.back();
            // Atualiza a interface para mostrar os dados do usuário
            userDataController.userDataExists.value = true;
          },
        ),
        barrierDismissible: false,
      );
    }
  }

  void _showEditDialog() {
    final userDataController = controller.userDataController;
    Get.dialog(
      UserDataDialog(
        controller: userDataController,
        onComplete: () => Get.back(),
        isEditing: true,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: GradientBackground(
        child: GetX<PacienteController>(
          builder: (controller) => Stack(
            children: [
              // Partículas decorativas de fundo
              ..._buildBackgroundParticles(),

              // Conteúdo principal
              SafeArea(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: Column(
                      children: [
                        const SizedBox(height: 40),

                        // Header com logo e título
                        SlideTransition(
                          position: _slideAnimation,
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: _buildHeader(),
                          ),
                        ),

                        const SizedBox(height: 30),

                        // Card de dados do usuário
                        ScaleTransition(
                          scale: _scaleAnimation,
                          child: _buildUserCard(),
                        ),

                        const SizedBox(height: 40),

                        // Mensagem de erro ou botão de scanner
                        if (controller.errorMessage.isNotEmpty)
                          SlideTransition(
                            position: _slideAnimation,
                            child: _buildErrorMessage(),
                          )
                        else
                          AnimatedBuilder(
                            animation: _pulseAnimation,
                            builder: (context, child) {
                              return Transform.scale(
                                scale: _pulseAnimation.value,
                                child: _buildScannerButton(),
                              );
                            },
                          ),

                        const SizedBox(height: 50),

                        // Instruções e botão voltar
                        SlideTransition(
                          position: _slideAnimation,
                          child: _buildInstructions(),
                        ),

                        const SizedBox(height: 30),

                        FadeTransition(
                          opacity: _fadeAnimation,
                          child: _buildBackButton(),
                        ),

                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ),

              if (controller.showScanner.value) _buildQRScanner(),
            ],
          ),
        ),
      ),
    );
  }

  List<Widget> _buildBackgroundParticles() {
    return List.generate(8, (index) {
      return Positioned(
        left: (index * 50.0) % Get.width,
        top: (index * 100.0) % Get.height,
        child: TweenAnimationBuilder(
          duration: Duration(seconds: 3 + index),
          tween: Tween<double>(begin: 0, end: 1),
          builder: (context, double value, child) {
            return Transform.translate(
              offset: Offset(
                20 * value * (index.isEven ? 1 : -1),
                30 * value,
              ),
              child: Opacity(
                opacity: 0.1,
                child: Container(
                  width: 6 + (index * 2.0),
                  height: 6 + (index * 2.0),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.5),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            );
          },
          onEnd: () {
            // Reiniciar animação
            setState(() {});
          },
        ),
      );
    });
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // Logo com efeito brilho
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              colors: [
                Colors.teal.shade400,
                Colors.teal.shade600,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.teal.withOpacity(0.3),
                blurRadius: 20,
                spreadRadius: 5,
              ),
            ],
          ),
          child: const Icon(
            Icons.qr_code_scanner,
            size: 60,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),

        // Título principal
        ShaderMask(
          shaderCallback: (bounds) => LinearGradient(
            colors: [Colors.teal.shade600, Colors.teal.shade800],
          ).createShader(bounds),
          child: const Text(
            'Scanner QR',
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
        const SizedBox(height: 8),

        // Subtítulo
        Text(
          'Escaneie o código para entrar na fila',
          style: TextStyle(
            fontFamily: 'Georgia',
            fontSize: 16,
            color: Colors.grey.shade700,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildUserCard() {
    return Obx(() {
      if (controller.userDataController.userDataExists.value) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 10),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.white,
                Colors.grey.shade50,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                spreadRadius: 0,
                offset: const Offset(0, 10),
              ),
            ],
            border: Border.all(
              color: Colors.teal.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Column(
            children: [
              // Avatar e nome
              Row(
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.teal.shade400, Colors.teal.shade600],
                      ),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.person,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),
                  const SizedBox(width: 15),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Olá, ${controller.userDataController.nomeController.text}',
                          style: const TextStyle(
                            fontFamily: 'Georgia',
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 4),
                        FutureBuilder<Map<String, dynamic>?>(
                          future: controller.userDataController.getUserData(),
                          builder: (context, snapshot) {
                            if (snapshot.hasData && snapshot.data != null) {
                              final userId = snapshot.data!['userId'] ?? '';
                              return Text(
                                'ID: $userId',
                                style: TextStyle(
                                  fontFamily: 'Georgia',
                                  fontSize: 14,
                                  color: Colors.grey.shade600,
                                  fontWeight: FontWeight.w500,
                                ),
                              );
                            }
                            return const SizedBox.shrink();
                          },
                        ),
                      ],
                    ),
                  ),
                  // Botão de editar
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.teal.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      icon: Icon(
                        Icons.edit,
                        size: 20,
                        color: Colors.teal.shade700,
                      ),
                      tooltip: 'Editar seus dados',
                      onPressed: _showEditDialog,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // Status card
              Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.green.withOpacity(0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.check_circle_outline,
                      color: Colors.green.shade600,
                      size: 20,
                    ),
                    const SizedBox(width: 10),
                    const Expanded(
                      child: Text(
                        'Dados confirmados • Pronto para escanear',
                        style: TextStyle(
                          fontFamily: 'Georgia',
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      }
      return const SizedBox.shrink();
    });
  }

  Widget _buildErrorMessage() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.red.shade50,
            Colors.red.shade100,
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.red.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.red.withOpacity(0.2),
            blurRadius: 10,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red.shade600,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              controller.errorMessage.value,
              style: TextStyle(
                color: Colors.red.shade700,
                fontFamily: 'Georgia',
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScannerButton() {
    return GestureDetector(
      onTap: controller.toggleScanner,
      child: Container(
        width: Get.width * 0.85,
        padding: const EdgeInsets.symmetric(vertical: 30),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.teal.shade400,
              Colors.teal.shade600,
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.teal.withOpacity(0.4),
              blurRadius: 20,
              spreadRadius: 0,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          children: [
            // Ícone QR Code animado
            TweenAnimationBuilder(
              duration: const Duration(seconds: 2),
              tween: Tween<double>(begin: 0, end: 1),
              builder: (context, double value, child) {
                return Transform.rotate(
                  angle: value * 0.1,
                  child: Container(
                    width: 90,
                    height: 90,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.3),
                        width: 2,
                      ),
                    ),
                    child: Center(
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          // Círculo de fundo
                          Container(
                            width: 60,
                            height: 60,
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              shape: BoxShape.circle,
                            ),
                          ),
                          // Ícone principal
                          Icon(
                            Icons.qr_code_scanner_rounded,
                            size: 45,
                            color: Colors.white,
                          ),
                          // Efeito de brilho
                          Positioned(
                            top: 15,
                            right: 20,
                            child: Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.6),
                                shape: BoxShape.circle,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
              onEnd: () => setState(() {}),
            ),

            const SizedBox(height: 20),

            // Texto principal
            const Text(
              'Toque para Escanear',
              style: TextStyle(
                fontFamily: 'Georgia',
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),

            const SizedBox(height: 8),

            // Texto secundário
            Text(
              'Abrir câmera do dispositivo',
              style: TextStyle(
                fontFamily: 'Georgia',
                fontSize: 14,
                color: Colors.white.withOpacity(0.8),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.7),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.grey.withOpacity(0.2),
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.info_outline,
            color: Colors.teal.shade600,
            size: 28,
          ),
          const SizedBox(height: 12),
          const Text(
            'Como usar',
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          ..._buildInstructionSteps(),
        ],
      ),
    );
  }

  List<Widget> _buildInstructionSteps() {
    final steps = [
      {
        'icon': Icons.qr_code,
        'text': 'Encontre o QR Code no hospital ou consultório'
      },
      {'icon': Icons.camera_alt, 'text': 'Toque no botão para abrir a câmera'},
      {
        'icon': Icons.center_focus_strong,
        'text': 'Posicione o código na área de escaneamento'
      },
      {'icon': Icons.check_circle, 'text': 'Aguarde a confirmação automática'},
    ];

    return steps.asMap().entries.map((entry) {
      int index = entry.key;
      Map<String, dynamic> step = entry.value;

      return Padding(
        padding: const EdgeInsets.only(bottom: 12),
        child: Row(
          children: [
            Container(
              width: 35,
              height: 35,
              decoration: BoxDecoration(
                color: Colors.teal.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                step['icon'],
                size: 18,
                color: Colors.teal.shade700,
              ),
            ),
            const SizedBox(width: 15),
            Expanded(
              child: Text(
                '${index + 1}. ${step['text']}',
                style: const TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 14,
                  color: Colors.black87,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      );
    }).toList();
  }

  Widget _buildBackButton() {
    return GestureDetector(
      onTap: () => Get.back(),
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.white, Colors.grey.shade100],
          ),
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.15),
              spreadRadius: 0,
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Center(
          child: SizedBox(
            width: 28,
            height: 28,
            child: Image.asset(
              'assets/left.png',
              fit: BoxFit.contain,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildQRScanner() {
    return Positioned.fill(
      child: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.black87, Colors.black],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Stack(
          children: [
            MobileScanner(
              controller: controller.scannerController,
              onDetect: (capture) {
                final List<Barcode> barcodes = capture.barcodes;
                for (final barcode in barcodes) {
                  if (barcode.rawValue == null) continue;

                  try {
                    final qrData = json.decode(barcode.rawValue!);
                    controller.toggleScanner();
                    controller.showQRDialog(qrData);
                    break;
                  } catch (e) {
                    controller.errorMessage.value =
                        'QR Code inválido. Tente novamente.';
                    controller.toggleScanner();
                  }
                }
              },
            ),

            // Overlay com área de escaneamento delimitada
            _buildScannerOverlay(),

            // Header com botão fechar
            Positioned(
              top: 50,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.5),
                        borderRadius: BorderRadius.circular(25),
                      ),
                      child: IconButton(
                        icon: const Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 28,
                        ),
                        onPressed: controller.toggleScanner,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.5),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: const Text(
                        'Scanner QR',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontFamily: 'Georgia',
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Instruções na parte inferior
            Positioned(
              bottom: 120,
              left: 20,
              right: 20,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.7),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.qr_code_scanner,
                      color: Colors.teal.shade400,
                      size: 32,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Posicione o QR Code dentro da área delimitada',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontFamily: 'Georgia',
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'O escaneamento será automático',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 14,
                        fontFamily: 'Georgia',
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScannerOverlay() {
    final scanArea = MediaQuery.of(Get.context!).size.width * 0.75;

    return Container(
      decoration: ShapeDecoration(
        shape: QrScannerOverlayShape(
          borderColor: Colors.teal.shade400,
          borderRadius: 20,
          borderLength: 50,
          borderWidth: 6,
          cutOutSize: scanArea,
        ),
      ),
    );
  }
}

// Classe personalizada para criar o overlay do scanner
class QrScannerOverlayShape extends ShapeBorder {
  final Color borderColor;
  final double borderWidth;
  final Color overlayColor;
  final double borderRadius;
  final double borderLength;
  final double cutOutSize;

  const QrScannerOverlayShape({
    this.borderColor = Colors.white,
    this.borderWidth = 3.0,
    this.overlayColor = const Color.fromRGBO(0, 0, 0, 80),
    this.borderRadius = 0,
    this.borderLength = 40,
    required this.cutOutSize,
  });

  @override
  EdgeInsetsGeometry get dimensions => const EdgeInsets.all(10);

  @override
  Path getInnerPath(Rect rect, {TextDirection? textDirection}) {
    return Path()
      ..fillType = PathFillType.evenOdd
      ..addPath(getOuterPath(rect), Offset.zero);
  }

  @override
  Path getOuterPath(Rect rect, {TextDirection? textDirection}) {
    Path _getLeftTopPath(Rect rect) {
      return Path()
        ..moveTo(rect.left, rect.bottom)
        ..lineTo(rect.left, rect.top + borderRadius)
        ..quadraticBezierTo(
            rect.left, rect.top, rect.left + borderRadius, rect.top)
        ..lineTo(rect.right, rect.top);
    }

    return _getLeftTopPath(rect)
      ..lineTo(rect.right, rect.bottom)
      ..lineTo(rect.left, rect.bottom)
      ..lineTo(rect.left, rect.top);
  }

  @override
  void paint(Canvas canvas, Rect rect, {TextDirection? textDirection}) {
    final width = rect.width;
    final borderWidthSize = width / 2;
    final height = rect.height;
    final borderHeightSize = height / 2;
    final cutOutWidth = cutOutSize < width ? cutOutSize : width - borderWidth;
    final cutOutHeight =
        cutOutSize < height ? cutOutSize : height - borderWidth;

    final backgroundPaint = Paint()
      ..color = overlayColor
      ..style = PaintingStyle.fill;

    final boxPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth;

    final cutOutRect = Rect.fromLTWH(
      borderWidthSize - cutOutWidth / 2,
      borderHeightSize - cutOutHeight / 2,
      cutOutWidth,
      cutOutHeight,
    );

    // Desenhar fundo escuro com área de corte transparente
    canvas.saveLayer(
      rect,
      backgroundPaint,
    );
    canvas.drawRect(rect, backgroundPaint);
    canvas.drawRRect(
      RRect.fromRectAndRadius(cutOutRect, Radius.circular(borderRadius)),
      Paint()..blendMode = BlendMode.clear,
    );
    canvas.restore();

    // Desenhar cantos da moldura
    final cornerLength = borderLength;
    final cornerPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth
      ..strokeCap = StrokeCap.round;

    // Canto superior esquerdo
    canvas.drawLine(
      Offset(cutOutRect.left, cutOutRect.top + cornerLength),
      Offset(cutOutRect.left, cutOutRect.top),
      cornerPaint,
    );
    canvas.drawLine(
      Offset(cutOutRect.left, cutOutRect.top),
      Offset(cutOutRect.left + cornerLength, cutOutRect.top),
      cornerPaint,
    );

    // Canto superior direito
    canvas.drawLine(
      Offset(cutOutRect.right - cornerLength, cutOutRect.top),
      Offset(cutOutRect.right, cutOutRect.top),
      cornerPaint,
    );
    canvas.drawLine(
      Offset(cutOutRect.right, cutOutRect.top),
      Offset(cutOutRect.right, cutOutRect.top + cornerLength),
      cornerPaint,
    );

    // Canto inferior esquerdo
    canvas.drawLine(
      Offset(cutOutRect.left, cutOutRect.bottom - cornerLength),
      Offset(cutOutRect.left, cutOutRect.bottom),
      cornerPaint,
    );
    canvas.drawLine(
      Offset(cutOutRect.left, cutOutRect.bottom),
      Offset(cutOutRect.left + cornerLength, cutOutRect.bottom),
      cornerPaint,
    );

    // Canto inferior direito
    canvas.drawLine(
      Offset(cutOutRect.right - cornerLength, cutOutRect.bottom),
      Offset(cutOutRect.right, cutOutRect.bottom),
      cornerPaint,
    );
    canvas.drawLine(
      Offset(cutOutRect.right, cutOutRect.bottom),
      Offset(cutOutRect.right, cutOutRect.bottom - cornerLength),
      cornerPaint,
    );

    // Linha animada de escaneamento (opcional)
    final scanLinePaint = Paint()
      ..color = borderColor.withOpacity(0.8)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    final scanLineY = cutOutRect.top + (cutOutRect.height * 0.5);
    canvas.drawLine(
      Offset(cutOutRect.left + 10, scanLineY),
      Offset(cutOutRect.right - 10, scanLineY),
      scanLinePaint,
    );
  }

  @override
  ShapeBorder scale(double t) {
    return QrScannerOverlayShape(
      borderColor: borderColor,
      borderWidth: borderWidth,
      overlayColor: overlayColor,
      borderRadius: borderRadius,
      borderLength: borderLength,
      cutOutSize: cutOutSize,
    );
  }
}
