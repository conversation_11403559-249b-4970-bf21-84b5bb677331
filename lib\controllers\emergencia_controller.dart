import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:fila_app/utils/date_utils.dart';
import 'package:fila_app/controllers/secretaria_controller.dart';

/// 🚨 CONTROLLER DE EMERGÊNCIAS
/// Gerencia todas as situações de emergência e pausas na fila
class EmergenciaController extends GetxController {
  static const String _tag = '[EMERGENCIA]';

  // Estado reativo da emergência
  final RxBool filaEmEmergencia = false.obs;
  final RxString motivoEmergencia = ''.obs;
  final RxString tipoEmergencia = ''.obs;
  final Rxn<DateTime> inicioEmergencia = Rxn<DateTime>();
  final RxString medicoEmergencia = ''.obs;
  final RxString hospitalEmergencia = ''.obs;

  /// 🚨 EMERGÊNCIA DA SECRETÁRIA: Pausar toda a fila
  Future<bool> ativarEmergenciaSecretaria({
    required String medicoId,
    required String hospitalId,
    required String motivo,
    String? mensagemPersonalizada,
  }) async {
    try {
      debugPrint('$_tag Ativando emergência da secretária...');

      // Criar registro de emergência no servidor usando a classe existente
      final emergencia = ParseObject('Emergencia')
        ..set('tipo', 'secretaria_pausa')
        ..set('detalhes', motivo)
        ..set('status', 'ativa')
        ..set('data_registro', TimezoneUtils.getNowBrasil());

      // Campos adicionais para identificar médico e hospital
      final medicoObj = ParseObject('Medico')..objectId = medicoId;
      final consultorioObj = ParseObject('consultorio')..objectId = hospitalId;

      emergencia.set('medico_id', medicoObj);
      emergencia.set('consultorio_id', consultorioObj);

      if (mensagemPersonalizada?.isNotEmpty == true) {
        emergencia.set('mensagem_personalizada', mensagemPersonalizada);
      }

      final response = await emergencia.save();

      if (response.success) {
        // Pausar todas as filas do médico
        await _pausarFilasMedico(medicoId, motivo);

        // Atualizar estado local
        _atualizarEstadoEmergencia('secretaria', motivo, medicoId, hospitalId);

        debugPrint('$_tag ✅ Emergência da secretária ativada com sucesso');
        return true;
      } else {
        debugPrint(
            '$_tag ❌ Erro ao salvar emergência: ${response.error?.message}');
        return false;
      }
    } catch (e) {
      debugPrint('$_tag ❌ Erro na emergência da secretária: $e');
      return false;
    }
  }

  /// 🏥 EMERGÊNCIA DO MÉDICO: Pausar para cirurgia/emergência médica
  Future<bool> ativarEmergenciaMedico({
    required String medicoId,
    required String hospitalId,
    required String tipoEmergencia, // 'cirurgia', 'emergencia_medica', 'outros'
    required String motivo,
    required int tempoEstimadoMinutos,
  }) async {
    try {
      debugPrint('$_tag Ativando emergência do médico...');

      final emergencia = ParseObject('Emergencia')
        ..set('tipo', 'medico_$tipoEmergencia')
        ..set('detalhes', motivo)
        ..set('status', 'ativa')
        ..set('data_registro', TimezoneUtils.getNowBrasil());

      // Campos adicionais
      final medicoObj = ParseObject('Medico')..objectId = medicoId;
      final consultorioObj = ParseObject('consultorio')..objectId = hospitalId;

      emergencia.set('medico_id', medicoObj);
      emergencia.set('consultorio_id', consultorioObj);
      emergencia.set('tempo_estimado_minutos', tempoEstimadoMinutos);

      final response = await emergencia.save();

      if (response.success) {
        // Pausar filas e notificar com tempo estimado
        final mensagemCompleta = _gerarMensagemEmergenciaMedico(
            tipoEmergencia, motivo, tempoEstimadoMinutos);
        await _pausarFilasMedico(medicoId, mensagemCompleta);

        _atualizarEstadoEmergencia('medico', motivo, medicoId, hospitalId);

        debugPrint('$_tag ✅ Emergência do médico ativada com sucesso');
        return true;
      } else {
        debugPrint(
            '$_tag ❌ Erro ao salvar emergência médica: ${response.error?.message}');
        return false;
      }
    } catch (e) {
      debugPrint('$_tag ❌ Erro na emergência do médico: $e');
      return false;
    }
  }

  /// 🟢 FINALIZAR EMERGÊNCIA
  Future<bool> finalizarEmergencia(String medicoId) async {
    try {
      debugPrint('$_tag Finalizando emergência...');

      // Buscar emergência ativa usando a classe existente
      final queryEmergencia = QueryBuilder<ParseObject>(
          ParseObject('Emergencia'))
        ..whereEqualTo('medico_id', ParseObject('Medico')..objectId = medicoId)
        ..whereEqualTo('status', 'ativa')
        ..orderByDescending('createdAt')
        ..setLimit(1);

      final response = await queryEmergencia.query();

      if (response.success && response.results?.isNotEmpty == true) {
        final emergencia = response.results!.first;

        // Finalizar emergência
        emergencia.set('status', 'finalizada');
        emergencia.set('data_finalizacao', TimezoneUtils.getNowBrasil());

        final saveResponse = await emergencia.save();

        if (saveResponse.success) {
          // Reativar filas
          await _reativarFilasMedico(medicoId);

          // Limpar estado local
          _limparEstadoEmergencia();

          debugPrint('$_tag ✅ Emergência finalizada com sucesso');
          return true;
        }
      }

      return false;
    } catch (e) {
      debugPrint('$_tag ❌ Erro ao finalizar emergência: $e');
      return false;
    }
  }

  /// 📢 MENSAGEM PERSONALIZADA PARA TODOS OS PACIENTES
  Future<bool> enviarMensagemPersonalizada({
    required String medicoId,
    required String titulo,
    required String mensagem,
    bool pausarFila = false,
  }) async {
    try {
      debugPrint('$_tag Enviando mensagem personalizada...');

      // ✅ CORREÇÃO: Obter o consultório do médico primeiro
      final queryMedico = QueryBuilder<ParseObject>(ParseObject('Medico'))
        ..whereEqualTo('objectId', medicoId)
        ..includeObject(['consultorio']);

      final medicoResponse = await queryMedico.query();

      if (!medicoResponse.success || medicoResponse.results?.isEmpty == true) {
        debugPrint('$_tag ❌ Médico não encontrado: $medicoId');
        return false;
      }

      final medico = medicoResponse.results!.first;

      // Tentar obter consultório do médico através de relação
      ParseObject? consultorioMedico;

      // Primeira tentativa: consultório direto
      consultorioMedico = medico.get<ParseObject>('consultorio');

      // Segunda tentativa: através de MedicoConsultorio
      if (consultorioMedico == null) {
        final queryMedicoConsultorio =
            QueryBuilder<ParseObject>(ParseObject('MedicoConsultorio'))
              ..whereEqualTo('medico', medico)
              ..includeObject(['consultorio']);

        final medicoConsultorioResponse = await queryMedicoConsultorio.query();

        if (medicoConsultorioResponse.success &&
            medicoConsultorioResponse.results?.isNotEmpty == true) {
          consultorioMedico = medicoConsultorioResponse.results!.first
              .get<ParseObject>('consultorio');
        }
      }

      // Terceira tentativa: usar controller de secretaria se disponível
      if (consultorioMedico == null) {
        final secretariaController = Get.find<SecretariaController>();
        consultorioMedico = secretariaController.currentHospital;
      }

      if (consultorioMedico == null) {
        debugPrint(
            '$_tag ❌ Consultório não encontrado para o médico: $medicoId');
        return false;
      }

      debugPrint(
          '$_tag ✅ Consultório encontrado: ${consultorioMedico.objectId}');

      // ✅ AGORA: Criar mensagem com consultorio_id obrigatório
      final mensagemObj = ParseObject('MensagemFila')
        ..set('medico_id', ParseObject('Medico')..objectId = medicoId)
        ..set('consultorio_id',
            consultorioMedico) // ✅ CAMPO OBRIGATÓRIO ADICIONADO
        ..set('titulo', titulo)
        ..set('mensagem', mensagem)
        ..set('tipo', pausarFila ? 'pausa_personalizada' : 'informativa')
        ..set('data_envio', TimezoneUtils.getNowBrasil())
        ..set('ativa', true);

      final response = await mensagemObj.save();

      if (response.success) {
        // Se for para pausar fila, fazer isso primeiro
        if (pausarFila) {
          await _pausarFilasMedico(medicoId, mensagem);
        }

        debugPrint('$_tag ✅ Mensagem personalizada enviada com sucesso');
        return true;
      } else {
        debugPrint(
            '$_tag ❌ Erro ao salvar mensagem: ${response.error?.message}');
        return false;
      }

      return false;
    } catch (e) {
      debugPrint('$_tag ❌ Erro ao enviar mensagem personalizada: $e');
      return false;
    }
  }

  /// 🔄 VERIFICAR ESTADO ATUAL DE EMERGÊNCIA
  Future<void> verificarEstadoEmergencia(String medicoId) async {
    try {
      final queryEmergencia = QueryBuilder<ParseObject>(
          ParseObject('Emergencia'))
        ..whereEqualTo('medico_id', ParseObject('Medico')..objectId = medicoId)
        ..whereEqualTo('status', 'ativa')
        ..orderByDescending('createdAt')
        ..setLimit(1);

      final response = await queryEmergencia.query();

      if (response.success && response.results?.isNotEmpty == true) {
        final emergencia = response.results!.first;
        final tipo = emergencia.get<String>('tipo') ?? '';
        final detalhes = emergencia.get<String>('detalhes') ?? '';

        // Obter consultorio_id se existir
        final consultorioObj = emergencia.get<ParseObject>('consultorio_id');
        final hospitalId = consultorioObj?.objectId ?? '';

        _atualizarEstadoEmergencia(tipo, detalhes, medicoId, hospitalId);
      } else {
        _limparEstadoEmergencia();
      }
    } catch (e) {
      debugPrint('$_tag ❌ Erro ao verificar estado de emergência: $e');
    }
  }

  // ========== MÉTODOS PRIVADOS ==========

  Future<void> _pausarFilasMedico(String medicoId, String motivo) async {
    try {
      debugPrint('$_tag 🚨 Pausando filas do médico $medicoId...');

      final queryFilas = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('medico', ParseObject('Medico')..objectId = medicoId)
        ..whereContainedIn('status', ['aguardando', 'em_atendimento']);

      final response = await queryFilas.query();

      if (response.success && response.results?.isNotEmpty == true) {
        int filasPausadas = 0;

        for (final fila in response.results!) {
          // ⚠️ IMPORTANTE: Salvar status anterior para poder restaurar
          final statusAtual = fila.get<String>('status');
          fila.set('status_anterior', statusAtual);
          fila.set('status', 'pausado_emergencia');
          fila.set('motivo_pausa', motivo);
          fila.set('data_pausa', TimezoneUtils.getNowBrasil());

          final saveResult = await fila.save();
          if (saveResult.success) {
            filasPausadas++;
            debugPrint(
                '$_tag ✅ Fila ${fila.objectId} pausada (status: $statusAtual → pausado_emergencia)');
          } else {
            debugPrint(
                '$_tag ❌ Erro ao pausar fila ${fila.objectId}: ${saveResult.error?.message}');
          }
        }

        debugPrint(
            '$_tag ✅ $filasPausadas de ${response.results!.length} filas pausadas com sucesso');

        // 🔄 IMPORTANTE: NÃO alterar o campo em_fila dos pacientes
        // Os pacientes permanecem na fila, apenas pausados temporariamente
        debugPrint(
            '$_tag ℹ️ Campo em_fila dos pacientes mantido (pacientes permanecem na fila pausada)');
      } else {
        debugPrint('$_tag ⚠️ Nenhuma fila ativa encontrada para pausar');
      }
    } catch (e) {
      debugPrint('$_tag ❌ Erro ao pausar filas: $e');
    }
  }

  Future<void> _reativarFilasMedico(String medicoId) async {
    try {
      debugPrint('$_tag 🟢 Reativando filas do médico $medicoId...');

      final queryFilas = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('medico', ParseObject('Medico')..objectId = medicoId)
        ..whereEqualTo('status', 'pausado_emergencia');

      final response = await queryFilas.query();

      if (response.success && response.results?.isNotEmpty == true) {
        int filasReativadas = 0;

        for (final fila in response.results!) {
          final statusAnterior =
              fila.get<String>('status_anterior') ?? 'aguardando';

          // Restaurar status anterior
          fila.set('status', statusAnterior);
          fila.unset('status_anterior');
          fila.unset('motivo_pausa');
          fila.unset('data_pausa');

          final saveResult = await fila.save();
          if (saveResult.success) {
            filasReativadas++;
            debugPrint(
                '$_tag ✅ Fila ${fila.objectId} reativada (pausado_emergencia → $statusAnterior)');
          } else {
            debugPrint(
                '$_tag ❌ Erro ao reativar fila ${fila.objectId}: ${saveResult.error?.message}');
          }
        }

        debugPrint(
            '$_tag ✅ $filasReativadas de ${response.results!.length} filas reativadas com sucesso');

        // 🔄 IMPORTANTE: Campo em_fila dos pacientes já estava correto
        // Os pacientes nunca saíram da fila, apenas estavam pausados
        debugPrint(
            '$_tag ℹ️ Pacientes retomam atendimento normal (em_fila mantido durante pausa)');
      } else {
        debugPrint('$_tag ⚠️ Nenhuma fila pausada encontrada para reativar');
      }
    } catch (e) {
      debugPrint('$_tag ❌ Erro ao reativar filas: $e');
    }
  }

  String _gerarMensagemEmergenciaMedico(String tipo, String motivo, int tempo) {
    switch (tipo) {
      case 'cirurgia':
        return 'Atendimentos pausados para cirurgia de emergência. Previsão: ${tempo}min. Motivo: $motivo';
      case 'emergencia_medica':
        return 'Atendimentos pausados para emergência médica. Previsão: ${tempo}min. Motivo: $motivo';
      default:
        return 'Atendimentos pausados temporariamente. Previsão: ${tempo}min. Motivo: $motivo';
    }
  }

  void _atualizarEstadoEmergencia(
      String tipo, String motivo, String medicoId, String hospitalId) {
    filaEmEmergencia.value = true;
    tipoEmergencia.value = tipo;
    motivoEmergencia.value = motivo;
    medicoEmergencia.value = medicoId;
    hospitalEmergencia.value = hospitalId;
    inicioEmergencia?.value = DateTime.now();
  }

  void _limparEstadoEmergencia() {
    filaEmEmergencia.value = false;
    tipoEmergencia.value = '';
    motivoEmergencia.value = '';
    medicoEmergencia.value = '';
    hospitalEmergencia.value = '';
    inicioEmergencia?.value = null;
  }
}
