import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/session_manager.dart';
import '../services/auth_service.dart';

/// 🔐 Widget de Configurações de Sessão
///
/// Permite ao usuário:
/// - Ver status da sessão
/// - Ativar/desativar auto-login
/// - Ver informações de última atividade
/// - Forçar refresh da sessão
class SessionSettingsWidget extends StatelessWidget {
  const SessionSettingsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SessionManager>(
      init: Get.find<SessionManager>(),
      builder: (sessionManager) {
        return Card(
          margin: const EdgeInsets.all(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                const SizedBox(height: 16),
                _buildSessionStatus(sessionManager),
                const Divider(height: 24),
                _buildAutoLoginSetting(sessionManager),
                const Divider(height: 24),
                _buildSessionInfo(sessionManager),
                const SizedBox(height: 16),
                _buildActions(sessionManager),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.security,
          color: Colors.blue,
          size: 24,
        ),
        const SizedBox(width: 8),
        Text(
          'Configurações de Sessão',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildSessionStatus(SessionManager sessionManager) {
    return Obx(() {
      final isActive = sessionManager.isSessionActive.value;
      final isRefreshing = sessionManager.isRefreshingSession.value;

      return Row(
        children: [
          Icon(
            isActive ? Icons.check_circle : Icons.error,
            color: isActive ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Status da Sessão',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
                Text(
                  isRefreshing
                      ? 'Atualizando...'
                      : isActive
                          ? 'Ativa e sincronizada'
                          : 'Inativa',
                  style: TextStyle(
                    color: isRefreshing
                        ? Colors.orange
                        : isActive
                            ? Colors.green
                            : Colors.red,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          if (isRefreshing)
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
              ),
            ),
        ],
      );
    });
  }

  Widget _buildAutoLoginSetting(SessionManager sessionManager) {
    return Obx(() {
      final isEnabled = sessionManager.isAutoLoginEnabled.value;

      return Row(
        children: [
          Icon(
            Icons.login,
            color: Colors.blue,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Login Automático',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
                Text(
                  isEnabled
                      ? 'Você entrará automaticamente no app'
                      : 'Será necessário fazer login sempre',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: isEnabled,
            onChanged: (value) async {
              await sessionManager.setAutoLoginEnabled(value);

              Get.snackbar(
                'Configuração Atualizada',
                value
                    ? 'Login automático ativado'
                    : 'Login automático desativado',
                duration: const Duration(seconds: 2),
                backgroundColor: Colors.green.shade100,
                colorText: Colors.green.shade800,
              );
            },
            activeColor: Colors.green,
          ),
        ],
      );
    });
  }

  Widget _buildSessionInfo(SessionManager sessionManager) {
    return Obx(() {
      final sessionInfo = sessionManager.getSessionInfo();
      final lastActive = sessionManager.lastActiveTime.value;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Informações da Sessão',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 8),
          _buildInfoRow('Usuário', sessionInfo['userId'] ?? 'N/A'),
          _buildInfoRow('Tipo', sessionInfo['userType'] ?? 'N/A'),
          if (lastActive != null)
            _buildInfoRow(
              'Última atividade',
              _formatDateTime(lastActive),
            ),
          if (sessionManager.isSessionNearExpiry())
            Container(
              margin: const EdgeInsets.only(top: 8),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.orange.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.shade300),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.warning,
                    color: Colors.orange.shade700,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Sua sessão expira em breve',
                      style: TextStyle(
                        color: Colors.orange.shade700,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      );
    });
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActions(SessionManager sessionManager) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () async {
              final success = await sessionManager.forceRefreshSession();

              Get.snackbar(
                success ? 'Sucesso' : 'Erro',
                success
                    ? 'Sessão atualizada com sucesso'
                    : 'Erro ao atualizar sessão',
                duration: const Duration(seconds: 2),
                backgroundColor:
                    success ? Colors.green.shade100 : Colors.red.shade100,
                colorText:
                    success ? Colors.green.shade800 : Colors.red.shade800,
              );
            },
            icon: Icon(Icons.refresh),
            label: Text('Atualizar Sessão'),
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () => _showSessionDetails(sessionManager),
            icon: Icon(Icons.info_outline),
            label: Text('Detalhes Técnicos'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.grey[600],
            ),
          ),
        ),
      ],
    );
  }

  void _showSessionDetails(SessionManager sessionManager) {
    final sessionInfo = sessionManager.getSessionInfo();

    Get.dialog(
      AlertDialog(
        title: Text('Detalhes da Sessão'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailItem('Status Ativo', '${sessionInfo['isActive']}'),
              _buildDetailItem(
                  'Auto-Login', '${sessionInfo['autoLoginEnabled']}'),
              _buildDetailItem('Atualizando', '${sessionInfo['isRefreshing']}'),
              _buildDetailItem(
                  'Última Atividade', sessionInfo['lastActive'] ?? 'N/A'),
              _buildDetailItem('ID do Usuário', sessionInfo['userId'] ?? 'N/A'),
              _buildDetailItem(
                  'Tipo de Usuário', sessionInfo['userType'] ?? 'N/A'),
              if (sessionManager.isSessionNearExpiry())
                Container(
                  margin: const EdgeInsets.only(top: 8),
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.orange.shade100,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    'Sessão próxima do vencimento',
                    style: TextStyle(
                      color: Colors.orange.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('Fechar'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return 'Há ${difference.inDays} dia${difference.inDays > 1 ? 's' : ''}';
    } else if (difference.inHours > 0) {
      return 'Há ${difference.inHours} hora${difference.inHours > 1 ? 's' : ''}';
    } else if (difference.inMinutes > 0) {
      return 'Há ${difference.inMinutes} minuto${difference.inMinutes > 1 ? 's' : ''}';
    } else {
      return 'Agora mesmo';
    }
  }
}
