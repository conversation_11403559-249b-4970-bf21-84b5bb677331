import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:fila_app/utils/hybrid_cache_manager.dart';

/// Sistema Ponte para Notificações
/// Garante que mensagens da secretária cheguem na tela do paciente
class NotificationBridge {
  static final NotificationBridge _instance = NotificationBridge._internal();
  factory NotificationBridge() => _instance;
  NotificationBridge._internal();

  final HybridCacheManager _cache = HybridCacheManager();
  final Map<String, StreamController<Map<String, dynamic>>> _patientStreams =
      {};
  final Map<String, Timer> _pollingTimers = {};
  final Map<String, DateTime> _lastChecked = {};

  /// Configurar listener para um paciente específico
  void setupPatientListener(String patientId, String filaId) {
    if (_patientStreams.containsKey(patientId)) {
      debugPrint(
          '🔔 [NOTIFICATION_BRIDGE] Listener já existe para paciente: $patientId');
      return;
    }

    debugPrint(
        '🔔 [NOTIFICATION_BRIDGE] Configurando listener para paciente: $patientId');

    // Criar stream para este paciente
    _patientStreams[patientId] =
        StreamController<Map<String, dynamic>>.broadcast();
    _lastChecked[patientId] = DateTime.now().subtract(const Duration(hours: 1));

    // ✅ NOVO: Buscar dados da fila primeiro para obter médico e consultório
    _initializePatientListener(patientId, filaId);
  }

  /// ✅ NOVO: Inicializar listener com dados da fila
  Future<void> _initializePatientListener(
      String patientId, String filaId) async {
    try {
      // Buscar dados da fila para obter médico e consultório
      final filaQuery = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('objectId', filaId)
        ..includeObject(['medico', 'consultorio']);

      final filaResponse = await filaQuery.query();

      if (filaResponse.success && filaResponse.results?.isNotEmpty == true) {
        final fila = filaResponse.results!.first;
        final medico = fila.get<ParseObject>('medico');
        final consultorio = fila.get<ParseObject>('consultorio');

        if (medico != null && consultorio != null) {
          final medicoId = medico.objectId!;
          final consultorioId = consultorio.objectId!;

          debugPrint(
              '🔔 [NOTIFICATION_BRIDGE] Dados da fila obtidos - Médico: $medicoId, Consultório: $consultorioId');

          // Configurar LiveQuery para mensagens
          _setupMessageLiveQuery(patientId, medicoId, consultorioId);

          // Configurar polling como backup (a cada 30 segundos)
          _setupPollingBackup(patientId, medicoId, consultorioId);

          // Buscar mensagens imediatamente
          await _checkForNewMessages(patientId, medicoId, consultorioId);
        }
      }
    } catch (e) {
      debugPrint('❌ [NOTIFICATION_BRIDGE] Erro ao inicializar listener: $e');
    }
  }

  /// Configurar LiveQuery para mensagens
  void _setupMessageLiveQuery(
      String patientId, String medicoId, String consultorioId) async {
    try {
      // ✅ CORRIGIDO: Query para mensagens usando os campos corretos
      final query = QueryBuilder<ParseObject>(ParseObject('MensagemFila'))
        ..whereEqualTo('medico_id', ParseObject('Medico')..objectId = medicoId)
        ..whereEqualTo('consultorio_id',
            ParseObject('consultorio')..objectId = consultorioId)
        ..whereGreaterThan(
            'createdAt', DateTime.now().subtract(const Duration(hours: 2)))
        ..orderByDescending('createdAt');

      final liveQuery = LiveQuery();
      final subscription = await liveQuery.client.subscribe(query);

      subscription.on(LiveQueryEvent.create, (mensagem) {
        debugPrint('🔔 [LIVEQUERY] Nova mensagem detectada via LiveQuery');
        _handleNewMessage(patientId, mensagem);
      });

      subscription.on(LiveQueryEvent.update, (mensagem) {
        debugPrint('🔔 [LIVEQUERY] Mensagem atualizada via LiveQuery');
        _handleNewMessage(patientId, mensagem);
      });

      debugPrint(
          '✅ [NOTIFICATION_BRIDGE] LiveQuery configurado para paciente: $patientId');
    } catch (e) {
      debugPrint('❌ [NOTIFICATION_BRIDGE] Erro ao configurar LiveQuery: $e');
    }
  }

  /// Configurar polling como backup
  void _setupPollingBackup(
      String patientId, String medicoId, String consultorioId) {
    _pollingTimers[patientId] =
        Timer.periodic(const Duration(seconds: 30), (_) async {
      await _checkForNewMessages(patientId, medicoId, consultorioId);
    });
  }

  /// ✅ CORRIGIDO: Verificar novas mensagens via polling
  Future<void> _checkForNewMessages(
      String patientId, String medicoId, String consultorioId) async {
    try {
      final lastCheck = _lastChecked[patientId] ??
          DateTime.now().subtract(const Duration(hours: 1));

      // ✅ CORRIGIDO: Query usando os campos corretos da MensagemFila
      final query = QueryBuilder<ParseObject>(ParseObject('MensagemFila'))
        ..whereEqualTo('medico_id', ParseObject('Medico')..objectId = medicoId)
        ..whereEqualTo('consultorio_id',
            ParseObject('consultorio')..objectId = consultorioId)
        ..whereGreaterThan('createdAt', lastCheck)
        ..orderByDescending('createdAt');

      final response = await query.query();

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        debugPrint(
            '🔔 [POLLING] Encontradas ${response.results!.length} mensagens novas');

        for (final mensagem in response.results!) {
          _handleNewMessage(patientId, mensagem);
        }

        // Atualizar timestamp da última verificação
        _lastChecked[patientId] = DateTime.now();
      } else {
        debugPrint('🔔 [POLLING] Nenhuma mensagem nova encontrada');
      }
    } catch (e) {
      debugPrint('❌ [NOTIFICATION_BRIDGE] Erro no polling: $e');
    }
  }

  /// ✅ CORRIGIDO: Lidar com nova mensagem do LiveQuery
  void _handleNewMessage(String patientId, ParseObject mensagem) {
    try {
      final messageData = {
        'id': mensagem.objectId ??
            'unknown_${DateTime.now().millisecondsSinceEpoch}',
        'titulo': mensagem.get<String>('titulo') ?? 'Aviso',
        'mensagem': mensagem.get<String>('mensagem') ??
            mensagem.get<String>('texto') ??
            'Nova mensagem da secretaria',
        'prioridade': mensagem.get<String>('prioridade') ?? 'media',
        'icone': mensagem.get<String>('icone') ?? 'notification',
        'timestamp': mensagem.get<DateTime>('createdAt')?.toIso8601String() ??
            DateTime.now().toIso8601String(),
        'source': 'notification_bridge',
      };

      debugPrint(
          '🔔 [NOTIFICATION_BRIDGE] Processando mensagem: ${messageData['titulo']}');
      _notifyPatient(patientId, messageData);
    } catch (e) {
      debugPrint('❌ [NOTIFICATION_BRIDGE] Erro ao processar mensagem: $e');
    }
  }

  /// Notificar paciente sobre nova mensagem
  void _notifyPatient(String patientId, Map<String, dynamic> messageData) {
    final stream = _patientStreams[patientId];
    if (stream != null && !stream.isClosed) {
      stream.add({
        'type': 'new_message',
        'data': messageData,
        'timestamp': DateTime.now().toIso8601String(),
      });

      debugPrint(
          '📨 [NOTIFICATION_BRIDGE] Mensagem enviada para paciente: $patientId - ${messageData['titulo']}');
    } else {
      debugPrint(
          '❌ [NOTIFICATION_BRIDGE] Stream não disponível para paciente: $patientId');
    }
  }

  /// Obter stream de notificações para um paciente
  Stream<Map<String, dynamic>>? getPatientStream(String patientId) {
    return _patientStreams[patientId]?.stream;
  }

  /// ✅ MELHORADO: Remover listener de um paciente
  void removePatientListener(String patientId) {
    debugPrint(
        '🔔 [NOTIFICATION_BRIDGE] Removendo listener para paciente: $patientId');

    // Cancelar timer de polling
    _pollingTimers[patientId]?.cancel();
    _pollingTimers.remove(patientId);

    // Fechar stream
    _patientStreams[patientId]?.close();
    _patientStreams.remove(patientId);

    // Limpar timestamp
    _lastChecked.remove(patientId);

    debugPrint(
        '✅ [NOTIFICATION_BRIDGE] Listener removido para paciente: $patientId');
  }

  /// ✅ NOVO: Método para forçar verificação de mensagens
  Future<void> forceCheckMessages(String patientId) async {
    try {
      final fila = await _getPatientQueueData(patientId);
      if (fila != null) {
        final medico = fila['medico'] as ParseObject?;
        final consultorio = fila['consultorio'] as ParseObject?;

        if (medico?.objectId != null && consultorio?.objectId != null) {
          await _checkForNewMessages(
              patientId, medico!.objectId!, consultorio!.objectId!);
        }
      }
    } catch (e) {
      debugPrint('❌ [NOTIFICATION_BRIDGE] Erro ao forçar verificação: $e');
    }
  }

  /// ✅ NOVO: Obter dados da fila do paciente
  Future<Map<String, dynamic>?> _getPatientQueueData(String patientId) async {
    try {
      final query = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('idPaciente', patientId)
        ..whereContainedIn('status', ['aguardando', 'em_atendimento'])
        ..includeObject(['medico', 'consultorio'])
        ..orderByDescending('createdAt');

      final response = await query.query();

      if (response.success && response.results?.isNotEmpty == true) {
        final fila = response.results!.first;
        return {
          'fila': fila,
          'medico': fila.get<ParseObject>('medico'),
          'consultorio': fila.get<ParseObject>('consultorio'),
        };
      }
    } catch (e) {
      debugPrint('❌ [NOTIFICATION_BRIDGE] Erro ao buscar dados da fila: $e');
    }
    return null;
  }

  /// ✅ NOVO: Limpar todos os listeners
  void dispose() {
    debugPrint('🔔 [NOTIFICATION_BRIDGE] Limpando todos os listeners...');

    for (final timer in _pollingTimers.values) {
      timer.cancel();
    }
    _pollingTimers.clear();

    for (final stream in _patientStreams.values) {
      stream.close();
    }
    _patientStreams.clear();

    _lastChecked.clear();

    debugPrint('✅ [NOTIFICATION_BRIDGE] Todos os listeners removidos');
  }
}
