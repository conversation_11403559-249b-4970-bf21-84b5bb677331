{"cmake.ignoreCMakeListsMissing": true, "java.jdt.ls.vmargs": "-XX:+UseG1GC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx4G -Xms512m -Xlog:disable", "java.configuration.updateBuildConfiguration": "automatic", "java.compile.nullAnalysis.mode": "disabled", "java.import.gradle.enabled": true, "java.import.gradle.wrapper.enabled": true, "java.import.gradle.java.home": "C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot", "java.import.gradle.offline.enabled": false, "java.server.launchMode": "Standard", "java.maxConcurrentBuilds": 8, "files.watcherExclude": {"**/.git/objects/**": true, "**/.git/subtree-cache/**": true, "**/node_modules/*/**": true, "**/.hg/store/**": true, "**/build/**": true, "**/.dart_tool/**": true, "**/android/build/**": true, "**/ios/build/**": true, "**/windows/build/**": true, "**/linux/build/**": true, "**/macos/build/**": true}, "search.exclude": {"**/build": true, "**/.dart_tool": true, "**/android/build": true, "**/ios/build": true, "**/windows/build": true, "**/linux/build": true, "**/macos/build": true, "**/.gradle": true}, "dart.flutterHotReloadOnSave": "allIfDirty", "dart.buildRunnerAdditionalArgs": ["--delete-conflicting-outputs"], "dart.previewFlutterUiGuides": true, "dart.previewFlutterUiGuidesCustomTracking": true, "dart.flutterCreateAndroidLanguage": "kotlin", "dart.flutterCreateIOSLanguage": "swift", "editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "editor.formatOnSave": true, "editor.formatOnType": true}