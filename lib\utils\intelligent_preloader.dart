import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Sistema de preload inteligente baseado em padrões de uso
class IntelligentPreloader extends GetxService {
  static final IntelligentPreloader _instance =
      IntelligentPreloader._internal();
  factory IntelligentPreloader() => _instance;
  IntelligentPreloader._internal();

  final Map<String, dynamic> _preloadedData = {};
  final Map<String, DateTime> _accessPatterns = {};
  final Map<String, int> _accessFrequency = {};
  final Map<String, Future> _ongoingPreloads = {};

  /// Registra acesso a uma tela/dados
  void registerAccess(String key) {
    _accessPatterns[key] = DateTime.now();
    _accessFrequency[key] = (_accessFrequency[key] ?? 0) + 1;

    // Analisar padrões e fazer preload inteligente
    _analyzeAndPreload(key);
  }

  /// Analisa padrões e faz preload de dados relacionados
  void _analyzeAndPreload(String currentKey) {
    // Mapear relacionamentos comuns
    final relatedKeys = _getRelatedKeys(currentKey);

    for (final relatedKey in relatedKeys) {
      // Só fazer preload se não está sendo carregado e não existe
      if (!_ongoingPreloads.containsKey(relatedKey) &&
          !_preloadedData.containsKey(relatedKey)) {
        _preloadInBackground(relatedKey);
      }
    }
  }

  /// Mapear chaves relacionadas baseado em padrões
  List<String> _getRelatedKeys(String key) {
    final relationships = {
      'fila_medico': ['metricas_atendimento', 'pacientes_hoje'],
      'dashboard_admin': ['hospitais_ativos', 'usuarios_online'],
      'paciente_form': ['especialidades', 'horarios_disponiveis'],
      'mensagens': ['conversas_recentes', 'notificacoes'],
    };

    return relationships[key] ?? [];
  }

  /// Preload de dados em background
  Future<void> _preloadInBackground(String key) async {
    if (_ongoingPreloads.containsKey(key)) return;

    final preloadFunction = _getPreloadFunction(key);
    if (preloadFunction == null) return;

    final future = preloadFunction().then((data) {
      _preloadedData[key] = data;
      _ongoingPreloads.remove(key);
      return data;
    }).catchError((error) {
      _ongoingPreloads.remove(key);
      return null;
    });

    _ongoingPreloads[key] = future;
  }

  /// Mapear funções de preload por chave
  Future<dynamic> Function()? _getPreloadFunction(String key) {
    final preloadFunctions = <String, Future<dynamic> Function()>{
      'metricas_atendimento': () => _mockLoadMetricas(),
      'pacientes_hoje': () => _mockLoadPacientes(),
      'hospitais_ativos': () => _mockLoadHospitais(),
      'especialidades': () => _mockLoadEspecialidades(),
      'horarios_disponiveis': () => _mockLoadHorarios(),
    };

    return preloadFunctions[key];
  }

  /// Mock functions - substituir pelas reais
  Future<Map<String, dynamic>> _mockLoadMetricas() async {
    await Future.delayed(const Duration(milliseconds: 500));
    return {
      'tempo_medio': 15.5,
      'pacientes_atendidos': 45,
      'eficiencia': 0.85,
    };
  }

  Future<List<Map<String, dynamic>>> _mockLoadPacientes() async {
    await Future.delayed(const Duration(milliseconds: 300));
    return [
      {'nome': 'João Silva', 'hora': '14:30'},
      {'nome': 'Maria Santos', 'hora': '15:00'},
    ];
  }

  Future<List<Map<String, dynamic>>> _mockLoadHospitais() async {
    await Future.delayed(const Duration(milliseconds: 400));
    return [
      {'nome': 'Hospital Central', 'status': 'ativo'},
      {'nome': 'Clínica Norte', 'status': 'ativo'},
    ];
  }

  Future<List<String>> _mockLoadEspecialidades() async {
    await Future.delayed(const Duration(milliseconds: 200));
    return ['Cardiologia', 'Dermatologia', 'Neurologia'];
  }

  Future<List<String>> _mockLoadHorarios() async {
    await Future.delayed(const Duration(milliseconds: 250));
    return ['08:00', '09:00', '10:00', '11:00'];
  }

  /// Obtém dados preloaded ou carrega sob demanda
  Future<T?> getPreloadedOrFetch<T>(
    String key,
    Future<T> Function() fetcher,
  ) async {
    // Se já tem preloaded, retorna imediatamente
    if (_preloadedData.containsKey(key)) {
      return _preloadedData[key] as T?;
    }

    // Se está sendo carregado, aguarda
    if (_ongoingPreloads.containsKey(key)) {
      final result = await _ongoingPreloads[key];
      return result as T?;
    }

    // Carrega sob demanda
    return await fetcher();
  }

  /// Preload manual de dados específicos
  void manualPreload(String key, Future<dynamic> Function() loader) {
    if (!_ongoingPreloads.containsKey(key) &&
        !_preloadedData.containsKey(key)) {
      _preloadInBackground(key);
    }
  }

  /// Limpa dados preloaded com base na frequência de acesso
  void optimizeCache() {
    final now = DateTime.now();
    final keysToRemove = <String>[];

    for (final entry in _accessPatterns.entries) {
      final key = entry.key;
      final lastAccess = entry.value;
      final frequency = _accessFrequency[key] ?? 0;

      // Remove se não foi acessado por mais de 1 hora e tem baixa frequência
      if (now.difference(lastAccess).inHours > 1 && frequency < 3) {
        keysToRemove.add(key);
      }
    }

    for (final key in keysToRemove) {
      _preloadedData.remove(key);
      _accessPatterns.remove(key);
      _accessFrequency.remove(key);
    }

    // Cache otimizado silenciosamente
  }

  /// Relatório de eficiência do preload
  Map<String, dynamic> getPreloadReport() {
    final hitRate = _preloadedData.isNotEmpty
        ? (_accessFrequency.values.where((freq) => freq > 1).length /
                _preloadedData.length) *
            100
        : 0.0;

    return {
      'preloaded_items': _preloadedData.length,
      'access_patterns': _accessPatterns.length,
      'hit_rate_percent': hitRate,
      'ongoing_preloads': _ongoingPreloads.length,
      'total_accesses':
          _accessFrequency.values.fold(0, (sum, freq) => sum + freq),
    };
  }

  /// Reset completo
  void reset() {
    _preloadedData.clear();
    _accessPatterns.clear();
    _accessFrequency.clear();
    _ongoingPreloads.clear();
  }
}

/// Mixin para widgets que usam preload inteligente
mixin IntelligentPreloadMixin<T extends StatefulWidget> on State<T> {
  String get preloadKey => T.toString();

  @override
  void initState() {
    super.initState();
    final preloader = Get.find<IntelligentPreloader>();
    preloader.registerAccess(preloadKey);
  }
}

/// Widget wrapper que registra acesso automaticamente
class PreloadAwareWidget extends StatefulWidget {
  final Widget child;
  final String preloadKey;
  final Map<String, Future Function()>? relatedPreloads;

  const PreloadAwareWidget({
    super.key,
    required this.child,
    required this.preloadKey,
    this.relatedPreloads,
  });

  @override
  State<PreloadAwareWidget> createState() => _PreloadAwareWidgetState();
}

class _PreloadAwareWidgetState extends State<PreloadAwareWidget> {
  @override
  void initState() {
    super.initState();
    final preloader = Get.find<IntelligentPreloader>();

    // Registrar acesso
    preloader.registerAccess(widget.preloadKey);

    // Preload manual de dados relacionados
    widget.relatedPreloads?.forEach((key, loader) {
      preloader.manualPreload(key, loader);
    });
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
