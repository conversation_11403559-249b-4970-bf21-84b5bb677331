{"flutter": {"platforms": {"android": {"default": {"projectId": "saudesemesperaa", "appId": "1:224042695582:android:bac16e5a6e4153fe69b48f", "fileOutput": "android/app/google-services.json"}}, "dart": {"lib/firebase_options.dart": {"projectId": "saudesemesperaa", "configurations": {"android": "1:224042695582:android:bac16e5a6e4153fe69b48f", "ios": "1:224042695582:ios:d666cffd3b9088de69b48f", "macos": "1:224042695582:ios:de34e6953007a2b969b48f", "web": "1:224042695582:web:d40e6cef52738d2669b48f", "windows": "1:224042695582:web:3ce497ae6469db6e69b48f"}}}, "ios": {"default": {"projectId": "saudesemesperaa", "appId": "1:224042695582:ios:d666cffd3b9088de69b48f", "uploadDebugSymbols": false, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "macos": {"default": {"projectId": "saudesemesperaa", "appId": "1:224042695582:ios:de34e6953007a2b969b48f", "uploadDebugSymbols": false, "fileOutput": "macos/Runner/GoogleService-Info.plist"}}}}}