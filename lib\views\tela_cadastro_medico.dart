import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../controllers/registration_controller.dart';
import 'package:fila_app/views/gradient_background.dart';
import 'package:fila_app/widgets/registro_profissional_formatter.dart'; // Nova importação
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:flutter_svg/flutter_svg.dart';

class CRMInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    final String text = newValue.text;

    // Remover caracteres não numéricos e letras para UF
    String cleanedText = text.replaceAll(RegExp(r'[^0-9A-Z\-]'), '');

    // Formato padrão CRM: XXXXX-UF (onde UF é opcional)
    if (cleanedText.length <= 5) {
      return TextEditingValue(
        text: cleanedText,
        selection: TextSelection.collapsed(offset: cleanedText.length),
      );
    } else if (cleanedText.length > 5 && !cleanedText.contains('-')) {
      // Adiciona hífen após os 5 primeiros dígitos
      String formatted =
          '${cleanedText.substring(0, 5)}-${cleanedText.substring(5)}';
      return TextEditingValue(
        text: formatted,
        selection: TextSelection.collapsed(offset: formatted.length),
      );
    }

    return TextEditingValue(
      text: cleanedText,
      selection: TextSelection.collapsed(offset: cleanedText.length),
    );
  }
}

class CPFInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    final String text = newValue.text;

    // Remove todos os caracteres não numéricos
    String numericOnly = text.replaceAll(RegExp(r'[^0-9]'), '');

    if (numericOnly.length <= 3) {
      return TextEditingValue(
        text: numericOnly,
        selection: TextSelection.collapsed(offset: numericOnly.length),
      );
    } else if (numericOnly.length <= 6) {
      return TextEditingValue(
        text: '${numericOnly.substring(0, 3)}.${numericOnly.substring(3)}',
        selection: TextSelection.collapsed(offset: numericOnly.length + 1),
      );
    } else if (numericOnly.length <= 9) {
      return TextEditingValue(
        text:
            '${numericOnly.substring(0, 3)}.${numericOnly.substring(3, 6)}.${numericOnly.substring(6)}',
        selection: TextSelection.collapsed(offset: numericOnly.length + 2),
      );
    } else {
      return TextEditingValue(
        text:
            '${numericOnly.substring(0, 3)}.${numericOnly.substring(3, 6)}.${numericOnly.substring(6, 9)}-${numericOnly.substring(9, min(numericOnly.length, 11))}',
        selection:
            TextSelection.collapsed(offset: min(numericOnly.length, 11) + 3),
      );
    }
  }

  int min(int a, int b) => a < b ? a : b;
}

class CadastroMedicoScreen extends StatefulWidget {
  const CadastroMedicoScreen({super.key});

  @override
  State<CadastroMedicoScreen> createState() => _CadastroMedicoScreenState();
}

class _CadastroMedicoScreenState extends State<CadastroMedicoScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController crmController = TextEditingController();
  final TextEditingController cpfController = TextEditingController();
  final TextEditingController nomeCompletoController = TextEditingController();
  bool _isLoading = false;
  bool _isValidatingCPF = false;
  Map<String, String>? _dadosCadastro;
  String _selectedEspecialidade = 'Selecione uma especialidade';
  String _cpfErrorText = '';
  String _selectedAreaProfissional = 'Medicina';
  String _registroLabel = 'CRM';

  // Novas variáveis para pesquisa de satisfação
  bool _pesquisaSatisfacaoAtiva = false;
  List<String> _perguntasPersonalizadas = [];
  final TextEditingController _novaPerguntaController = TextEditingController();

  // Mapeamento das áreas profissionais para seus conselhos
  final Map<String, String> _conselhosPorArea = {
    'Medicina': 'CRM',
    'Odontologia': 'CRO',
    'Fisioterapia': 'CREFITO',
    'Psicologia': 'CRP',
    'Nutrição': 'CRN',
    'Biomedicina': 'CRBM',
    'Enfermagem': 'COREN',
    'Fonoaudiologia': 'CREFONO',
    'Técnico em Radiologia': 'CRTR',
  };

  // Lista das áreas profissionais disponíveis
  final List<String> _areasProfissionais = [
    'Medicina',
    'Odontologia',
    'Fisioterapia',
    'Psicologia',
    'Nutrição',
    'Biomedicina',
    'Enfermagem',
    'Fonoaudiologia',
    'Técnico em Radiologia',
  ];

  // Mapeamento das especialidades por área profissional
  final Map<String, List<String>> _especialidadesPorArea = {
    'Medicina': [
      'Selecione uma especialidade',
      'Alergologia',
      'Cardiologia',
      'Clínico Geral',
      'Dermatologia',
      'Endocrinologia',
      'Gastroenterologia',
      'Geriatria',
      'Ginecologia',
      'Hematologia',
      'Infectologia',
      'Nefrologia',
      'Neurologia',
      'Oftalmologia',
      'Oncologia',
      'Ortopedia',
      'Otorrinolaringologia',
      'Pediatria',
      'Pneumologia',
      'Psiquiatria',
      'Reumatologia',
      'Urologia',
      'Acupuntura',
      'Anestesiologia',
      'Angiologia',
      'Cirurgia Cardiovascular',
      'Cirurgia Geral',
      'Cirurgia Plástica',
      'Cirurgia Torácica',
      'Cirurgia Vascular',
      'Coloproctologia',
      'Emergência Médica',
      'Genética Médica',
      'Homeopatia',
      'Imunologia',
      'Medicina de Família e Comunidade',
      'Medicina do Trabalho',
      'Medicina Esportiva',
      'Medicina Física e Reabilitação',
      'Medicina Intensiva',
      'Medicina Legal',
      'Medicina Nuclear',
      'Nutrigenética',
      'Nutrigenômica',
      'Nutrologia',
      'Patologia',
      'Patologia Clínica',
      'Radiologia',
      'Radioterapia',
      'Terapia Intensiva',
      'Tricologia',
    ],
    'Odontologia': [
      'Selecione uma especialidade',
      'Dentista Geral',
      'Ortodontia',
      'Endodontia',
      'Periodontia',
      'Implantodontia',
      'Odontopediatria',
      'Cirurgia Bucomaxilofacial',
      'Prótese Dentária',
      'Estomatologia',
      'Radiologia Odontológica',
    ],
    'Fisioterapia': [
      'Selecione uma especialidade',
      'Fisioterapia Ortopédica',
      'Fisioterapia Neurológica',
      'Fisioterapia Respiratória',
      'Fisioterapia Pediátrica',
      'Fisioterapia Esportiva',
      'Fisioterapia Aquática',
      'Fisioterapia Geriátrica',
      'Fisioterapia Dermatofuncional',
      'Fisioterapia Cardiovascular',
      'Acupuntura',
    ],
    'Psicologia': [
      'Selecione uma especialidade',
      'Psicologia Clínica',
      'Psicologia Infantil',
      'Neuropsicologia',
      'Psicologia Organizacional',
      'Psicologia Esportiva',
      'Psicologia Educacional',
      'Psicologia Hospitalar',
      'Psicologia Jurídica',
      'Psicologia Social',
      'Avaliação Psicológica',
    ],
    'Nutrição': [
      'Selecione uma especialidade',
      'Nutrição Clínica',
      'Nutrição Esportiva',
      'Nutrição Materno-Infantil',
      'Nutrição Funcional',
      'Nutrição Hospitalar',
      'Nutrição Comportamental',
      'Nutrição Vegetariana/Vegana',
      'Consultoria em Alimentação',
      'Gastronomia Funcional',
    ],
    'Biomedicina': [
      'Selecione uma especialidade',
      'Análises Clínicas',
      'Imagenologia',
      'Acupuntura',
      'Citologia Oncótica',
      'Estética',
      'Reprodução Humana',
      'Genética',
      'Microbiologia',
      'Parasitologia',
      'Biologia Molecular',
    ],
    'Enfermagem': [
      'Selecione uma especialidade',
      'Enfermagem Clínica',
      'Enfermagem Obstétrica',
      'Enfermagem Pediátrica',
      'Enfermagem em Saúde Mental',
      'Enfermagem do Trabalho',
      'Enfermagem em Hemodiálise',
      'Enfermagem em Oncologia',
      'Enfermagem em UTI',
      'Enfermagem em Home Care',
      'Estomaterapia',
    ],
    'Fonoaudiologia': [
      'Selecione uma especialidade',
      'Audiologia',
      'Linguagem',
      'Motricidade Orofacial',
      'Voz',
      'Disfagia',
      'Fonoaudiologia Educacional',
      'Fonoaudiologia Neurofuncional',
      'Gerontologia',
      'Fonoaudiologia do Trabalho',
      'Neuropsicologia',
    ],
    'Técnico em Radiologia': [
      'Selecione uma especialidade',
      'Radiologia Geral',
      'Tomografia Computadorizada',
      'Ressonância Magnética',
      'Mamografia',
      'Densitometria Óssea',
      'Medicina Nuclear',
      'Hemodinâmica',
      'Radiologia Intervencionista',
      'Radioterapia',
      'Ultrassonografia',
    ],
  };

  // Lista atual das especialidades baseada na área profissional selecionada
  List<String> get _especialidades =>
      _especialidadesPorArea[_selectedAreaProfissional] ??
      _especialidadesPorArea['Medicina']!;

  @override
  void dispose() {
    crmController.dispose();
    cpfController.dispose();
    nomeCompletoController.dispose();
    _novaPerguntaController
        .dispose(); // Dispose do controlador da nova pergunta
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final args = Get.arguments;
    if (args != null) {
      _dadosCadastro = (args as Map<String, dynamic>).map(
        (key, value) => MapEntry(key.toString(), value.toString()),
      );

      // Não preenchemos mais automaticamente o nome com o username
      // O nome será preenchido pela consulta do CPF
    }

    if (_dadosCadastro == null || _dadosCadastro!.isEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Navigator.of(context).pushReplacementNamed('/primeiroAcesso');
      });
    }
  }

  // Verifica se CPF é válido (sem buscar o nome)
  Future<bool> _verificarCPFValido(String cpf) async {
    cpf = cpf.replaceAll(RegExp(r'[^0-9]'), '');
    if (cpf.length != 11 || !isValidCPF(cpf)) {
      setState(() {
        _cpfErrorText = 'CPF inválido';
      });
      return false;
    }

    setState(() {
      _isValidatingCPF = true;
      _cpfErrorText = '';
    });

    try {
      // Em um cenário real, você faria uma verificação com uma API apropriada
      await Future.delayed(Duration(milliseconds: 500));

      // Simulação: CPFs que terminam com "99" já estão cadastrados
      if (cpf.endsWith("99")) {
        setState(() {
          _cpfErrorText = 'Este CPF já está cadastrado no sistema';
        });
        return false;
      }

      setState(() {
        _cpfErrorText = '';
      });
      return true;
    } catch (e) {
      setState(() {
        _cpfErrorText = 'Erro ao validar CPF: ${e.toString()}';
      });
      return false;
    } finally {
      setState(() {
        _isValidatingCPF = false;
      });
    }
  }

  Future<void> _handleRegistration() async {
    if (!_formKey.currentState!.validate() ||
        _isLoading ||
        _dadosCadastro == null) {
      return;
    }

    if (_selectedEspecialidade == 'Selecione uma especialidade') {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'Por favor, selecione uma especialidade',
            style: TextStyle(fontFamily: 'Georgia'),
          ),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Verifica se está em processo de validação
    if (_isValidatingCPF) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'Aguarde a conclusão da validação do CPF',
            style: TextStyle(fontFamily: 'Georgia'),
          ),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // Validação final apenas do CPF antes do cadastro
    bool cpfValido = await _verificarCPFValido(
        cpfController.text.replaceAll(RegExp(r'[^0-9]'), ''));

    if (!cpfValido) {
      // ignore: use_build_context_synchronously
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            _cpfErrorText,
            style: TextStyle(fontFamily: 'Georgia'),
          ),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      // Cria os parâmetros base para o registro
      final Map<String, dynamic> params = {
        'username': nomeCompletoController.text.trim(),
        'email': _dadosCadastro!['email']!,
        'senha': _dadosCadastro!['senha']!,
        'tipo':
            'medico', // MODIFICAR AQUI: usar "medico" em vez da área profissional
        'crm': crmController.text.trim(),
        'cpf': cpfController.text.replaceAll(RegExp(r'[^0-9]'), ''),
        'especializacao': _selectedEspecialidade,
        'nome': nomeCompletoController.text.trim(),
        'ativo': true,
      };

      // Tenta registrar o usuário com o parâmetro adicional
      try {
        await registerUser(
          username: params['username'],
          email: params['email'],
          senha: params['senha'],
          tipo: params['tipo'],
          crm: params['crm'],
          cpf: params['cpf'],
          especializacao: params['especializacao'],
          nome: params['nome'],
          ativo: params['ativo'],
          areaProfissional: _selectedAreaProfissional,
        );
      } catch (e) {
        // Se ocorrer erro que mencione areaProfissional, tenta sem ele
        if (e.toString().contains('areaProfissional')) {
          await registerUser(
            username: params['username'],
            email: params['email'],
            senha: params['senha'],
            tipo: params['tipo'],
            crm: params['crm'],
            cpf: params['cpf'],
            especializacao: params['especializacao'],
            nome: params['nome'],
            ativo: params['ativo'],
          );
        } else {
          rethrow; // Propaga outros erros
        }
      }

      if (!mounted) return;

      await _showSuccessDialog();
    } catch (e) {
      if (!mounted) return;
      _showErrorDialog(e.toString());
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  Future<void> _showSuccessDialog() {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: const Text(
            'Cadastro realizado com sucesso!',
            style: TextStyle(
              fontFamily: 'Georgia',
              fontWeight: FontWeight.bold,
            ),
          ),
          content: const Text(
            'Por favor, valide seu e-mail clicando no link enviado para sua caixa de entrada e, em seguida, faça o login no aplicativo para continuar',
            style: TextStyle(
              fontFamily: 'Georgia',
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pushNamedAndRemoveUntil(
                  '/login',
                  (Route<dynamic> route) => false,
                );
              },
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Erro'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  String? _validateCRM(String? value) {
    if (value == null || value.isEmpty) {
      return 'Por favor, insira o $_registroLabel';
    }

    return null;
  }

  String? _validateCPF(String? value) {
    if (value == null || value.isEmpty) {
      return 'Por favor, insira o CPF';
    }

    if (_cpfErrorText.isNotEmpty) {
      return _cpfErrorText;
    }

    // Validação básica do CPF
    final numeros = value.replaceAll(RegExp(r'[^0-9]'), '');
    if (numeros.length != 11) {
      return 'CPF deve conter 11 dígitos';
    }

    if (!isValidCPF(numeros)) {
      return 'CPF inválido';
    }

    return null;
  }

  bool isValidCPF(String cpf) {
    // Remove caracteres não numéricos
    cpf = cpf.replaceAll(RegExp(r'[^0-9]'), '');

    // Verifica se tem 11 dígitos
    if (cpf.length != 11) return false;

    // Verifica se todos os dígitos são iguais
    if (RegExp(r'^(\d)\1*$').hasMatch(cpf)) return false;

    // Calcula o primeiro dígito verificador
    int soma = 0;
    for (int i = 0; i < 9; i++) {
      soma += int.parse(cpf[i]) * (10 - i);
    }
    int digito1 = 11 - (soma % 11);
    if (digito1 > 9) digito1 = 0;

    // Calcula o segundo dígito verificador
    soma = 0;
    for (int i = 0; i < 10; i++) {
      soma += int.parse(cpf[i]) * (11 - i);
    }
    int digito2 = 11 - (soma % 11);
    if (digito2 > 9) digito2 = 0;

    // Verifica se os dígitos calculados são iguais aos informados
    return (digito1 == int.parse(cpf[9]) && digito2 == int.parse(cpf[10]));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientBackground(
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const SizedBox(height: 30),
                SvgPicture.asset(
                  'assets/logo1.svg',
                  width: MediaQuery.of(context).size.width * 0.8,
                ),
                const SizedBox(height: 20),
                const Text(
                  'Cadastro Profissional de Saúde',
                  style: TextStyle(
                    fontFamily: 'Georgia',
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 50),
                // Novo campo: Área Profissional
                _buildAreaProfissionalDropdown(),
                const SizedBox(height: 30),
                // Nome Completo
                _buildTextField(
                  label: "Nome Completo",
                  hint: "Digite seu nome completo",
                  controller: nomeCompletoController,
                  validator: (value) => value == null || value.isEmpty
                      ? 'Por favor, insira seu nome completo'
                      : null,
                  textCapitalization: TextCapitalization.words,
                  readOnly: false,
                ),
                const SizedBox(height: 30),
                // CPF
                _buildTextField(
                  label: "CPF",
                  hint: "Digite seu CPF",
                  controller: cpfController,
                  validator: _validateCPF,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    CPFInputFormatter(),
                    LengthLimitingTextInputFormatter(14),
                  ],
                  onChanged: (value) {
                    if (_cpfErrorText.isNotEmpty) {
                      setState(() {
                        _cpfErrorText = '';
                      });
                    }

                    String cpfNumerico =
                        value.replaceAll(RegExp(r'[^0-9]'), '');
                    if (cpfNumerico.length == 11) {
                      _verificarCPFValido(cpfNumerico);
                    }
                  },
                ),
                if (_isValidatingCPF)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Row(
                      children: [
                        SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                        SizedBox(width: 10),
                        Text(
                          'Validando CPF...',
                          style: TextStyle(fontFamily: 'Georgia', fontSize: 14),
                        ),
                      ],
                    ),
                  ),
                if (_cpfErrorText.isNotEmpty && !_isValidatingCPF)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      _cpfErrorText,
                      style:
                          TextStyle(color: Colors.red, fontFamily: 'Georgia'),
                    ),
                  ),
                const SizedBox(height: 30),
                // Registro Profissional (adaptável conforme a área selecionada)
                _buildTextField(
                  label: _registroLabel,
                  hint: "Insira seu $_registroLabel",
                  controller: crmController,
                  validator: _validateCRM,
                  keyboardType: TextInputType.text,
                  inputFormatters: [
                    CRMInputFormatter(), // Pode ser necessário criar formatters específicos para cada tipo de registro
                  ],
                ),
                const SizedBox(height: 30),
                // Especialidade (dinâmica conforme a área selecionada)
                _buildEspecialidadeDropdown(),
                const SizedBox(height: 30),
                // Pesquisa de Satisfação
                _buildPesquisaSatisfacao(),
                const SizedBox(height: 50),
                // Botões de navegação
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildNavigationButton(
                      icon: Icons.arrow_back,
                      label: 'Voltar',
                      onTap: () => Navigator.maybePop(context),
                    ),
                    _buildNavigationButton(
                      icon: Icons.check,
                      label: 'Confirmar',
                      isLoading: _isLoading,
                      onTap: _handleRegistration,
                    ),
                  ],
                ),
                const SizedBox(height: 30),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Novo widget para seleção da área profissional
  Widget _buildAreaProfissionalDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "Área Profissional",
          style: TextStyle(
            fontFamily: 'Georgia',
            fontSize: 20,
            fontWeight: FontWeight.normal,
            color: Colors.black,
          ),
        ),
        Container(
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: Colors.black.withOpacity(0.5),
                width: 1.0,
              ),
            ),
          ),
          child: DropdownButton<String>(
            value: _selectedAreaProfissional,
            isExpanded: true,
            underline: Container(),
            style: const TextStyle(
              fontFamily: 'Georgia',
              fontSize: 18,
              color: Colors.black,
            ),
            items: _areasProfissionais.map((String value) {
              return DropdownMenuItem<String>(
                value: value,
                child: Text(value),
              );
            }).toList(),
            onChanged: (String? newValue) {
              setState(() {
                _selectedAreaProfissional = newValue!;
                _selectedEspecialidade = 'Selecione uma especialidade';
                _registroLabel =
                    _conselhosPorArea[_selectedAreaProfissional] ?? 'CRM';
              });
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEspecialidadeDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "Especialidade",
          style: TextStyle(
            fontFamily: 'Georgia',
            fontSize: 20,
            fontWeight: FontWeight.normal,
            color: Colors.black,
          ),
        ),
        Container(
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: Colors.black.withOpacity(0.5),
                width: 1.0,
              ),
            ),
          ),
          child: DropdownButton<String>(
            value: _selectedEspecialidade,
            isExpanded: true,
            underline: Container(),
            style: const TextStyle(
              fontFamily: 'Georgia',
              fontSize: 18,
              color: Colors.black,
            ),
            items: _especialidades.map((String value) {
              return DropdownMenuItem<String>(
                value: value,
                child: Text(value),
              );
            }).toList(),
            onChanged: (String? newValue) {
              setState(() {
                _selectedEspecialidade = newValue!;
              });
            },
          ),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required String label,
    required String hint,
    required TextEditingController controller,
    required String? Function(String?) validator,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    TextCapitalization textCapitalization = TextCapitalization.none,
    Function(String)? onChanged,
    bool readOnly = false,
  }) {
    // Se for o campo de registro profissional, use o formatador específico
    if (label == _registroLabel) {
      inputFormatters = [
        RegistroProfissionalFormatter(tipo: _registroLabel),
      ];
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontFamily: 'Georgia',
            fontSize: 20,
            fontWeight: FontWeight.normal,
            color: Colors.black,
          ),
        ),
        TextFormField(
          controller: controller,
          validator: validator,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          textCapitalization: textCapitalization,
          onChanged: onChanged,
          readOnly: readOnly,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: const TextStyle(
              color: Colors.black54,
              fontFamily: 'Georgia',
              fontSize: 20,
            ),
            enabledBorder: const UnderlineInputBorder(
              borderSide: BorderSide(color: Colors.black),
            ),
            focusedBorder: const UnderlineInputBorder(
              borderSide: BorderSide(color: Colors.black, width: 2),
            ),
            errorBorder: const UnderlineInputBorder(
              borderSide: BorderSide(color: Colors.red),
            ),
            focusedErrorBorder: const UnderlineInputBorder(
              borderSide: BorderSide(color: Colors.red, width: 2),
            ),
          ),
          style: const TextStyle(
            fontFamily: 'Georgia',
            fontSize: 18,
          ),
        ),
      ],
    );
  }

  Widget _buildNavigationButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    bool isLoading = false,
  }) {
    return GestureDetector(
      onTap: isLoading ? null : onTap,
      child: Column(
        children: [
          if (isLoading)
            const SizedBox(
              width: 32,
              height: 32,
              child: CircularProgressIndicator(strokeWidth: 3),
            )
          else
            Icon(
              icon,
              size: 32,
              color: Colors.black,
            ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(
              fontFamily: 'Georgia',
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  // Novo widget para pesquisa de satisfação
  Widget _buildPesquisaSatisfacao() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "Pesquisa de Satisfação",
          style: TextStyle(
            fontFamily: 'Georgia',
            fontSize: 20,
            fontWeight: FontWeight.normal,
            color: Colors.black,
          ),
        ),
        SwitchListTile(
          title: const Text(
            "Ativar pesquisa de satisfação",
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 18,
              color: Colors.black,
            ),
          ),
          value: _pesquisaSatisfacaoAtiva,
          onChanged: (bool value) {
            setState(() {
              _pesquisaSatisfacaoAtiva = value;
            });
          },
        ),
        if (_pesquisaSatisfacaoAtiva) ...[
          const SizedBox(height: 10),
          const Text(
            "Perguntas Personalizadas",
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 18,
              fontWeight: FontWeight.normal,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 10),
          for (String pergunta in _perguntasPersonalizadas)
            ListTile(
              title: Text(
                pergunta,
                style: const TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 16,
                  color: Colors.black,
                ),
              ),
              trailing: IconButton(
                icon: const Icon(Icons.delete, color: Colors.red),
                onPressed: () {
                  setState(() {
                    _perguntasPersonalizadas.remove(pergunta);
                  });
                },
              ),
            ),
          const SizedBox(height: 10),
          TextFormField(
            controller: _novaPerguntaController,
            decoration: const InputDecoration(
              hintText: "Digite uma nova pergunta",
              hintStyle: TextStyle(
                color: Colors.black54,
                fontFamily: 'Georgia',
                fontSize: 18,
              ),
              enabledBorder: UnderlineInputBorder(
                borderSide: BorderSide(color: Colors.black),
              ),
              focusedBorder: UnderlineInputBorder(
                borderSide: BorderSide(color: Colors.black, width: 2),
              ),
            ),
            style: const TextStyle(
              fontFamily: 'Georgia',
              fontSize: 18,
            ),
          ),
          const SizedBox(height: 10),
          ElevatedButton(
            onPressed: () {
              if (_novaPerguntaController.text.isNotEmpty) {
                setState(() {
                  _perguntasPersonalizadas.add(_novaPerguntaController.text);
                  _novaPerguntaController.clear();
                });
              }
            },
            child: const Text(
              "Adicionar Pergunta",
              style: TextStyle(
                fontFamily: 'Georgia',
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ],
    );
  }
}
