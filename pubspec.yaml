name: fila_app
description: Aplicativo de gerenciamento de filas para hospitais e médicos.
publish_to: none # Remove this line if you wish to publish to pub.dev
version: 1.0.0+1

environment:
  sdk: ^3.5.4

dependencies:
  flutter:
    sdk: flutter  
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.8
  flutter_polyline_points: ^2.1.0
  parse_server_sdk_flutter: ^9.0.0
  provider: ^6.1.2
  mobile_scanner: ^7.0.0
  uuid: ^4.5.1
  qr_flutter: ^4.1.0
  google_maps_flutter: ^2.9.0
  url_launcher: ^6.3.1
  font_awesome_flutter: ^10.8.0
  timezone: ^0.10.0
  badges: ^3.1.2
  http: ^1.3.0
  mask_text_input_formatter: ^2.9.0
  flutter_dotenv: ^5.0.2
  flutter_native_splash: ^2.4.5
  device_info_plus: ^11.3.3
  flutter_secure_storage: ^9.2.4
  intl: ^0.20.0
  printing: ^5.14.2
  pdf: ^3.11.3
  get: ^4.7.2
  shared_preferences: ^2.5.2
  geolocator: ^14.0.1
  geocoding: ^4.0.0
  map_launcher: ^3.5.0
  package_info_plus: ^8.3.0
  hive_flutter: ^1.1.0
  cached_network_image: ^3.4.1
  connectivity_plus: ^6.1.3  # Esta é uma versão recente que usa List<ConnectivityResult>
  path_provider: ^2.1.5
  phone_form_field: ^10.0.8
  image_picker: ^1.1.2
  # Firebase dependencies - apenas para notificações push (FCM)
  firebase_core: ^3.13.1
  firebase_messaging: ^15.2.6
  flutter_local_notifications: ^19.0.0
  web_socket_channel: ^2.4.0
  crypto: ^3.0.5
  flutter_svg: ^2.0.10+1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.3

# Override para resolver o conflito de dependências
dependency_overrides:
  package_info_plus: ^4.0.1
  geolocator_android: ^4.6.0
  uuid: ^3.0.7
  http: ^0.13.0
  timezone: ^0.9.4
  printing: ^5.13.3

flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/app_icon.png"
  adaptive_icon_background: "#00000000" # Transparente
  adaptive_icon_foreground: "assets/app_icon.png"
  min_sdk_android: 24
  remove_alpha_ios: false  # Manter transparência no iOS
  ios_content_insets: 0.125
  web:
    generate: true
    image_path: "assets/app_icon.png"
    background_color: "#00000000"  # Transparente para web também

# flutter_native_splash:
  # Desabilitado - usando tela de loading customizada no Flutter
  # color: "75CBBB"
  # image: "assets/logo-removebg-preview-1-28.png"
  # android: true
  # ios: true

flutter:
  fonts:
    - family: Georgia
      fonts:
        - asset: assets/fonts/Georgia.ttf
  uses-material-design: true

  assets:
    - .env
    - assets/
    - assets/fonts/
    - assets/logo1.svg
    - assets/logo2.svg
    - assets/logo3.svg
    - assets/logo4.svg

CachedNetworkImage:
  imageUrl: imageUrl
  placeholder: (context, url) => const CircularProgressIndicator()
  errorWidget: (context, url, error) => const Icon(Icons.error)
