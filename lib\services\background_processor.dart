import 'dart:async';
import 'dart:isolate';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

/// Serviço para processamento em background
class BackgroundProcessor extends GetxService {
  static final BackgroundProcessor _instance = BackgroundProcessor._internal();
  factory BackgroundProcessor() => _instance;
  BackgroundProcessor._internal();

  final Map<String, Isolate> _isolates = {};
  final Map<String, SendPort> _sendPorts = {};
  final Map<String, Completer> _completers = {};

  /// Inicia processamento em background
  Future<T> processInBackground<T>({
    required String taskId,
    required Map<String, dynamic> data,
    required T Function(Map<String, dynamic>) processor,
    Duration timeout = const Duration(minutes: 5),
  }) async {
    // No Flutter Web, processa na thread principal
    if (kIsWeb) {
      return processor(data);
    }

    try {
      final completer = Completer<T>();
      _completers[taskId] = completer;

      // Criar ReceivePort para comunicação
      final receivePort = ReceivePort();

      // Spawn isolate
      final isolate = await Isolate.spawn(
        _isolateEntryPoint,
        _IsolateMessage(
          sendPort: receivePort.sendPort,
          taskId: taskId,
          data: data,
        ),
      );

      _isolates[taskId] = isolate;

      // Escutar resultados do isolate
      receivePort.listen((message) {
        if (message is _IsolateResult) {
          if (message.taskId == taskId) {
            if (message.error != null) {
              completer.completeError(Exception(message.error));
            } else {
              completer.complete(message.result as T);
            }
            _cleanup(taskId);
          }
        }
      });

      // Timeout
      Timer(timeout, () {
        if (!completer.isCompleted) {
          completer.completeError(
              TimeoutException('Background task timeout', timeout));
          _cleanup(taskId);
        }
      });

      return await completer.future;
    } catch (e) {
      _cleanup(taskId);
      rethrow;
    }
  }

  /// Entry point do isolate
  static void _isolateEntryPoint(_IsolateMessage message) {
    try {
      // Simular processamento pesado
      final result = _processData(message.data);

      message.sendPort.send(_IsolateResult(
        taskId: message.taskId,
        result: result,
      ));
    } catch (e) {
      message.sendPort.send(_IsolateResult(
        taskId: message.taskId,
        error: e.toString(),
      ));
    }
  }

  /// Processamento de dados (exemplo)
  static dynamic _processData(Map<String, dynamic> data) {
    final type = data['type'];

    switch (type) {
      case 'sort_large_list':
        return _sortLargeList(data['list'] as List);
      case 'calculate_metrics':
        return _calculateComplexMetrics(data);
      case 'process_images':
        return _processImages(data['images'] as List);
      default:
        return data;
    }
  }

  /// Ordenação de lista grande
  static List _sortLargeList(List items) {
    final sorted = List.from(items);
    sorted.sort();
    return sorted;
  }

  /// Cálculo de métricas complexas
  static Map<String, dynamic> _calculateComplexMetrics(
      Map<String, dynamic> data) {
    // Simular cálculos pesados
    final numbers = data['numbers'] as List<num>? ?? [];

    return {
      'sum': numbers.fold<num>(0, (sum, n) => sum + n),
      'average': numbers.isNotEmpty
          ? numbers.fold<num>(0, (sum, n) => sum + n) / numbers.length
          : 0.0,
      'max': numbers.isNotEmpty
          ? numbers.reduce((a, b) => a.compareTo(b) > 0 ? a : b)
          : 0.0,
      'min': numbers.isNotEmpty
          ? numbers.reduce((a, b) => a.compareTo(b) < 0 ? a : b)
          : 0.0,
      'processed_at': DateTime.now().toIso8601String(),
    };
  }

  /// Processamento de imagens
  static List<Map<String, dynamic>> _processImages(List images) {
    return images
        .map((image) => {
              'processed': true,
              'timestamp': DateTime.now().toIso8601String(),
              'original': image,
            })
        .toList();
  }

  /// Limpa recursos do isolate
  void _cleanup(String taskId) {
    _isolates[taskId]?.kill();
    _isolates.remove(taskId);
    _sendPorts.remove(taskId);
    _completers.remove(taskId);
  }

  /// Finaliza todos os isolates
  void dispose() {
    for (final isolate in _isolates.values) {
      isolate.kill();
    }
    _isolates.clear();
    _sendPorts.clear();
    _completers.clear();
  }
}

/// Sistema de cache inteligente com worker em background
class IntelligentCache extends GetxService {
  final Map<String, dynamic> _cache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  final Map<String, int> _accessCounts = {};
  final BackgroundProcessor _processor = BackgroundProcessor();

  /// Cache com processamento em background
  Future<T> getCachedOrProcess<T>({
    required String key,
    required Future<T> Function() fetcher,
    Duration maxAge = const Duration(minutes: 30),
    bool useBackgroundProcessing = true,
  }) async {
    // Verificar cache existente
    if (_cache.containsKey(key) && _isValidCache(key, maxAge)) {
      _accessCounts[key] = (_accessCounts[key] ?? 0) + 1;
      return _cache[key] as T;
    }

    // Se usar background processing para operações pesadas
    if (useBackgroundProcessing && !kIsWeb) {
      try {
        // Para operações async, usar fallback diretamente
        final result = await fetcher();

        _updateCache(key, result);
        return result;
      } catch (e) {
        // Background processing falhou, usando fallback
      }
    }

    // Fallback: processar na thread principal
    final result = await fetcher();
    _updateCache(key, result);
    return result;
  }

  /// Pré-aquecimento de cache em background
  void preWarmCache(Map<String, Future Function()> prewarmTasks) {
    for (final entry in prewarmTasks.entries) {
      if (!_cache.containsKey(entry.key)) {
        // Processar em background sem bloquear UI
        entry.value().then((result) {
          _updateCache(entry.key, result);
        }).catchError((e) {
          // Cache pre-warm falhou silenciosamente
        });
      }
    }
  }

  bool _isValidCache(String key, Duration maxAge) {
    final timestamp = _cacheTimestamps[key];
    if (timestamp == null) return false;

    return DateTime.now().difference(timestamp) < maxAge;
  }

  void _updateCache(String key, dynamic value) {
    _cache[key] = value;
    _cacheTimestamps[key] = DateTime.now();
    _accessCounts[key] = (_accessCounts[key] ?? 0) + 1;
  }

  /// Limpa cache com base na frequência de acesso
  void optimizeCache() {
    final lowAccessItems = _accessCounts.entries
        .where((entry) => entry.value < 3)
        .map((entry) => entry.key)
        .toList();

    for (final key in lowAccessItems) {
      _cache.remove(key);
      _cacheTimestamps.remove(key);
      _accessCounts.remove(key);
    }

    // Cache otimizado silenciosamente
  }
}

/// Mensagem para comunicação com isolate
class _IsolateMessage {
  final SendPort sendPort;
  final String taskId;
  final Map<String, dynamic> data;

  _IsolateMessage({
    required this.sendPort,
    required this.taskId,
    required this.data,
  });
}

/// Resultado do processamento em isolate
class _IsolateResult {
  final String taskId;
  final dynamic result;
  final String? error;

  _IsolateResult({
    required this.taskId,
    this.result,
    this.error,
  });
}
