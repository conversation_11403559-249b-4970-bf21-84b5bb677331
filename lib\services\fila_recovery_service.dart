import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import '../controllers/user_data_controller.dart';
import '../controllers/fila_state_controller.dart';
import '../utils/date_utils.dart';

/// Serviço responsável por recuperar automaticamente o estado da fila
/// quando o paciente reabre o app após fechar durante uma espera
class FilaRecoveryService extends GetxService {
  static const String _tag = '[FILA_RECOVERY]';

  final UserDataController _userDataController = Get.find<UserDataController>();

  /// Verificar se o usuário tem uma fila ativa e restaurar automaticamente
  Future<Map<String, dynamic>> checkAndRecoverActiveQueue() async {
    try {
      debugPrint(
          '$_tag 🔍 Verificando fila ativa para recuperação automática...');

      // 1. Obter dados do usuário/dispositivo
      final userData = await _userDataController.getUserData();
      if (userData == null) {
        debugPrint('$_tag ❌ Nenhum userData encontrado');
        return {'hasActiveQueue': false};
      }

      // Usar deviceId como identificador principal
      final deviceId = _userDataController.deviceId.value;
      final telefone = userData['telefone'];
      final userId = userData['userId'];

      debugPrint(
          '$_tag 📱 DeviceID: $deviceId, Telefone: $telefone, UserID: $userId');

      // 2. Verificar se há fila ativa no servidor (múltiplas estratégias)
      ParseObject? activeQueue;

      // 2.1 Buscar por deviceId (mais confiável)
      if (deviceId.isNotEmpty) {
        activeQueue = await _checkActiveQueueByDeviceId(deviceId);
      }

      // 2.2 Se não encontrou por deviceId, buscar por telefone
      if (activeQueue == null && telefone != null && telefone.isNotEmpty) {
        activeQueue = await _checkActiveQueueByPhone(telefone);
      }

      // 2.3 Se não encontrou por telefone, buscar por userId
      if (activeQueue == null && userId != null && userId.isNotEmpty) {
        activeQueue = await _checkActiveQueueByUserId(userId);
      }

      if (activeQueue == null) {
        debugPrint('$_tag ❌ Nenhuma fila ativa encontrada no servidor');

        // ✅ LIMPAR CAMPO em_fila SE ESTIVER MARCADO INCORRETAMENTE
        await _limparEstadoFilaInconsistente();

        await _clearLocalQueueState();
        return {'hasActiveQueue': false};
      }

      // 3. Validar se a fila ainda está válida (não foi cancelada/atendida)
      final queueStatus = activeQueue.get<String>('status') ?? 'aguardando';
      if (!['aguardando', 'em_atendimento', 'pausado_emergencia']
          .contains(queueStatus)) {
        debugPrint(
            '$_tag ⚠️ Fila encontrada mas com status inválido: $queueStatus');

        // ✅ LIMPAR CAMPO em_fila TAMBÉM PARA STATUS INVÁLIDO
        await _limparEstadoFilaInconsistente();

        await _clearLocalQueueState();
        return {'hasActiveQueue': false};
      }

      // 4. Verificar se a fila não é muito antiga (máximo 24 horas)
      final dataEntrada = activeQueue.get<DateTime>('data_entrada');
      if (dataEntrada != null) {
        final horasDecorridas = TimezoneUtils.getDifferenceInMinutes(
                dataEntrada, TimezoneUtils.getNowBrasil()) /
            60;
        if (horasDecorridas > 24) {
          debugPrint(
              '$_tag ⚠️ Fila muito antiga (${horasDecorridas.toStringAsFixed(1)}h), ignorando...');

          // ✅ LIMPAR CAMPO em_fila PARA FILA MUITO ANTIGA
          await _limparEstadoFilaInconsistente();

          await _clearLocalQueueState();
          return {'hasActiveQueue': false};
        }
      }

      // 5. Extrair dados da fila para restauração
      final queueData = await _extractQueueData(activeQueue);

      // 6. Salvar estado local para sincronização
      await _saveLocalQueueState(queueData);

      debugPrint(
          '$_tag ✅ Fila ativa recuperada! Status: $queueStatus, Posição: ${queueData['posicao']}');

      // ✅ PREPARAR DADOS PARA NAVEGAÇÃO
      final filaArguments = {
        'filaId': queueData['filaId'],
        'medicoNome': queueData['medicoNome'] ?? 'Médico',
        'especialidade': queueData['medicoEspecialidade'] ?? 'Especialidade',
        'posicao': queueData['posicao'],
        'status': queueStatus,
        'hospitalNome': queueData['hospitalNome'] ?? 'Hospital',
        'medicoCRM': queueData['medicoCRM'] ?? 'CRM',
      };

      debugPrint('$_tag 📦 Dados preparados para navegação: $filaArguments');

      return {
        'hasActiveQueue': true,
        'targetRoute':
            '/fila_paciente', // ✅ PACIENTE SEMPRE VAI PARA TELA DO PACIENTE
        'queueData': queueData,
        'filaArguments': filaArguments, // ✅ NOVOS ARGUMENTOS
      };
    } catch (e) {
      debugPrint('$_tag ❌ Erro ao verificar fila ativa: $e');
      return {'hasActiveQueue': false, 'error': e.toString()};
    }
  }

  /// Verificar se existe fila ativa por deviceId
  Future<ParseObject?> _checkActiveQueueByDeviceId(String deviceId) async {
    try {
      final query = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('deviceId', deviceId)
        ..whereContainedIn(
            'status', ['aguardando', 'em_atendimento', 'pausado_emergencia'])
        ..includeObject(['medico', 'consultorio'])
        ..orderByDescending('data_entrada')
        ..setLimit(1);

      final response = await query.query();
      if (response.success && response.results?.isNotEmpty == true) {
        return response.results!.first;
      }
    } catch (e) {
      debugPrint('$_tag ❌ Erro ao buscar fila por deviceId: $e');
    }
    return null;
  }

  /// Verificar se existe fila ativa por telefone
  Future<ParseObject?> _checkActiveQueueByPhone(String telefone) async {
    try {
      final query = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('telefone', telefone)
        ..whereContainedIn(
            'status', ['aguardando', 'em_atendimento', 'pausado_emergencia'])
        ..includeObject(['medico', 'consultorio'])
        ..orderByDescending('data_entrada')
        ..setLimit(1);

      final response = await query.query();
      if (response.success && response.results?.isNotEmpty == true) {
        return response.results!.first;
      }
    } catch (e) {
      debugPrint('$_tag ❌ Erro ao buscar fila por telefone: $e');
    }
    return null;
  }

  /// Verificar se existe fila ativa por userId
  Future<ParseObject?> _checkActiveQueueByUserId(String userId) async {
    try {
      final query = QueryBuilder<ParseObject>(ParseObject('Fila'))
        ..whereEqualTo('idPaciente', userId)
        ..whereContainedIn(
            'status', ['aguardando', 'em_atendimento', 'pausado_emergencia'])
        ..includeObject(['medico', 'consultorio'])
        ..orderByDescending('data_entrada')
        ..setLimit(1);

      final response = await query.query();
      if (response.success && response.results?.isNotEmpty == true) {
        return response.results!.first;
      }
    } catch (e) {
      debugPrint('$_tag ❌ Erro ao buscar fila por userId: $e');
    }
    return null;
  }

  /// Extrair dados da fila para uso no app
  Future<Map<String, dynamic>> _extractQueueData(ParseObject fila) async {
    final medico = fila.get<ParseObject>('medico');
    final consultorio = fila.get<ParseObject>('consultorio');

    return {
      'filaId': fila.objectId!,
      'status': fila.get<String>('status') ?? 'aguardando',
      'posicao': fila.get<int>('posicao') ?? 0,
      'nome': fila.get<String>('nome') ?? 'Paciente',
      'telefone': fila.get<String>('telefone') ?? '',
      'dataEntrada': fila.get<DateTime>('data_entrada')?.toIso8601String(),
      'medicoNome': medico?.get<String>('nome') ?? 'Médico',
      'medicoEspecialidade':
          medico?.get<String>('especialidade') ?? 'Especialidade',
      'consultorioNome': consultorio?.get<String>('nome') ?? 'Consultório',
      'tempoEstimado': fila.get<int>('tempo_estimado_minutos') ?? 0,
      'recoveredAt': TimezoneUtils.nowToIso8601StringBrasil(),
      'hospitalNome': fila.get<String>('hospitalNome') ?? 'Hospital',
      'medicoCRM': fila.get<String>('medicoCRM') ?? 'CRM',
    };
  }

  /// Salvar estado da fila localmente para sincronização
  Future<void> _saveLocalQueueState(Map<String, dynamic> queueData) async {
    try {
      await FilaStateController.salvarEstadoFila(
        filaId: queueData['filaId'],
        medicoNome: queueData['medicoNome'],
        especialidade: queueData['medicoEspecialidade'],
        posicao: queueData['posicao'],
      );
      debugPrint('$_tag 💾 Estado da fila salvo localmente');
    } catch (e) {
      debugPrint('$_tag ⚠️ Erro ao salvar estado local: $e');
    }
  }

  /// Limpar estado local da fila
  Future<void> _clearLocalQueueState() async {
    try {
      await FilaStateController.limparEstadoFila();
      debugPrint('$_tag 🧹 Estado local da fila limpo');
    } catch (e) {
      debugPrint('$_tag ⚠️ Erro ao limpar estado local: $e');
    }
  }

  /// Verificar se a recuperação automática está habilitada
  bool isRecoveryEnabled() {
    return true;
  }

  /// Forçar verificação de fila ativa (para uso manual)
  Future<bool> forceQueueCheck() async {
    final result = await checkAndRecoverActiveQueue();
    return result['hasActiveQueue'] == true;
  }

  /// ✅ MÉTODO ROBUSTO: Limpa estado inconsistente do campo em_fila
  Future<void> _limparEstadoFilaInconsistente() async {
    try {
      debugPrint('$_tag 🔧 Iniciando limpeza de estado inconsistente...');

      // Usar múltiplas estratégias para encontrar o paciente
      final userController = Get.find<UserDataController>();
      final telefone = userController.telefoneController.text
          .replaceAll(RegExp(r'[^0-9]'), '');
      final userId = userController.formattedUserId.value;
      final deviceId = userController.deviceId.value;

      // Verificar se temos pelo menos um identificador
      if (telefone.isEmpty && userId.isEmpty && deviceId.isEmpty) {
        debugPrint('$_tag ⚠️ Nenhum identificador disponível para limpeza');
        return;
      }

      List<ParseObject> pacientesInconsistentes = [];

      // 1. Buscar por telefone
      if (telefone.isNotEmpty) {
        try {
          final queryTelefone =
              QueryBuilder<ParseObject>(ParseObject('Paciente'))
                ..whereEqualTo('telefone', telefone)
                ..whereEqualTo('em_fila', true);

          final response = await queryTelefone.query();
          if (response.success && response.results?.isNotEmpty == true) {
            pacientesInconsistentes
                .addAll(response.results!.cast<ParseObject>());
            debugPrint(
                '$_tag 📱 Encontrados ${response.results!.length} pacientes por telefone');
          }
        } catch (e) {
          debugPrint('$_tag ⚠️ Erro ao buscar por telefone: $e');
        }
      }

      // 2. Buscar por userId (se não encontrou por telefone)
      if (pacientesInconsistentes.isEmpty && userId.isNotEmpty) {
        try {
          final queryUserId = QueryBuilder<ParseObject>(ParseObject('Paciente'))
            ..whereEqualTo('userId', userId)
            ..whereEqualTo('em_fila', true);

          final response = await queryUserId.query();
          if (response.success && response.results?.isNotEmpty == true) {
            pacientesInconsistentes
                .addAll(response.results!.cast<ParseObject>());
            debugPrint(
                '$_tag 🆔 Encontrados ${response.results!.length} pacientes por userId');
          }
        } catch (e) {
          debugPrint('$_tag ⚠️ Erro ao buscar por userId: $e');
        }
      }

      // 3. Buscar por deviceId (se ainda não encontrou)
      if (pacientesInconsistentes.isEmpty && deviceId.isNotEmpty) {
        try {
          final queryDeviceId =
              QueryBuilder<ParseObject>(ParseObject('Paciente'))
                ..whereEqualTo('deviceId', deviceId)
                ..whereEqualTo('em_fila', true);

          final response = await queryDeviceId.query();
          if (response.success && response.results?.isNotEmpty == true) {
            pacientesInconsistentes
                .addAll(response.results!.cast<ParseObject>());
            debugPrint(
                '$_tag 📱 Encontrados ${response.results!.length} pacientes por deviceId');
          }
        } catch (e) {
          debugPrint('$_tag ⚠️ Erro ao buscar por deviceId: $e');
        }
      }

      // 4. Corrigir cada paciente encontrado
      for (final paciente in pacientesInconsistentes) {
        try {
          debugPrint(
              '$_tag 🔧 Corrigindo paciente ${paciente.objectId} - removendo flag em_fila');

          // Atualizar campos de forma segura
          paciente.set('em_fila', false);
          paciente.set('ultima_fila', null);
          paciente.set('status_fila', 'fora_da_fila');
          paciente.set(
              'ultimoAcesso',
              TimezoneUtils
                  .getNowBrasil()); // ✅ USAR DATETIME AO INVÉS DE STRING

          final saveResponse = await paciente.save();

          if (saveResponse.success) {
            debugPrint(
                '$_tag ✅ Paciente ${paciente.objectId} corrigido com sucesso');
          } else {
            debugPrint(
                '$_tag ❌ Erro ao salvar paciente ${paciente.objectId}: ${saveResponse.error?.message}');
          }
        } catch (e) {
          debugPrint(
              '$_tag ❌ Erro ao corrigir paciente ${paciente.objectId}: $e');
        }
      }

      if (pacientesInconsistentes.isEmpty) {
        debugPrint('$_tag ✅ Nenhum estado inconsistente encontrado');
      } else {
        debugPrint(
            '$_tag ✅ Limpeza concluída - ${pacientesInconsistentes.length} pacientes corrigidos');
      }
    } catch (e) {
      debugPrint('$_tag ❌ Erro geral na limpeza de estado inconsistente: $e');
      debugPrint('$_tag 🔍 Stack trace: ${StackTrace.current}');
    }
  }
}
