import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import '../services/push_notification_service.dart';
import '../services/auth_service.dart';
import '../controllers/user_data_controller.dart';

class LoginController extends GetxController {
  // Observables
  final _isLoading = false.obs;
  final _obscureText = true.obs;
  final _emailError = RxString('');
  final _passwordError = RxString('');

  // Getters
  bool get isLoading => _isLoading.value;
  bool get obscureText => _obscureText.value;
  String get emailError => _emailError.value;
  String get passwordError => _passwordError.value;

  // Methods
  void togglePasswordVisibility() => _obscureText.value = !_obscureText.value;
  void clearErrors() {
    _emailError.value = '';
    _passwordError.value = '';
  }

  // Método para resetar o estado de carregamento
  void resetLoadingState() {
    _isLoading.value = false;
  }

  /// Login usando AuthService com SessionManager
  Future<void> handleLogin(String email, String password) async {
    if (!_validateInputs(email, password)) return;

    _isLoading.value = true;

    try {
      debugPrint('🔐 Iniciando login via AuthService...');

      final result = await AuthService.login(
        email: email,
        password: password,
      );

      if (result['success'] == true) {
        final userType = result['tipo'];
        final route = result['route'];

        debugPrint('✅ Login bem-sucedido: $userType -> $route');

        // Registrar para notificações push após login bem-sucedido
        await _registerForPushNotifications();

        // Navegar para rota apropriada
        Get.offAllNamed(route);
      } else {
        final error = result['error'] ?? 'Erro desconhecido no login';
        throw Exception(error);
      }
    } catch (e) {
      _handleError(e.toString());
    } finally {
      _isLoading.value = false;
    }
  }

  // Método simplificado para registrar notificações após login
  Future<void> _registerForPushNotifications() async {
    try {
      final pushService = Get.find<PushNotificationService>();
      await pushService.registerAfterLogin();
      debugPrint('✅ Notificações registradas após login');
    } catch (e) {
      debugPrint('⚠️ Erro ao registrar notificações após login: $e');
      // Não falhar o login por causa de notificações
    }
  }

  bool _validateInputs(String email, String password) {
    clearErrors();
    bool isValid = true;

    if (email.isEmpty) {
      _emailError.value = 'Por favor, insira seu e-mail';
      isValid = false;
    } else if (!GetUtils.isEmail(email)) {
      _emailError.value = 'Por favor, insira um e-mail válido';
      isValid = false;
    }

    if (password.isEmpty) {
      _passwordError.value = 'Por favor, insira sua senha';
      isValid = false;
    }

    return isValid;
  }

  void _handleError(String error) {
    Get.snackbar(
      'Erro',
      error.replaceAll('Exception:', '').trim(),
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Get.theme.colorScheme.error,
      colorText: Get.theme.colorScheme.onError,
      duration: const Duration(seconds: 3),
    );
  }

  /// Logout usando AuthService com SessionManager
  Future<void> logout() async {
    try {
      _isLoading.value = true;

      // Limpar dados de notificação antes do logout
      try {
        final pushService = Get.find<PushNotificationService>();
        await pushService.clearNotificationData();
        debugPrint('✅ Dados de notificação limpos com sucesso');
      } catch (notifError) {
        debugPrint('❌ Erro ao limpar dados de notificação: $notifError');
      }

      // Logout via AuthService (que usa SessionManager)
      await AuthService.logout();

      // Atualizar UserDataController após logout
      try {
        final userDataController = Get.find<UserDataController>();
        await userDataController.clearUserData();
        await userDataController.checkUserData();
      } catch (e) {
        debugPrint('❌ Erro ao atualizar dados de usuário após logout: $e');
      }

      // Navegar para tela de login
      Get.offAllNamed('/login');

      debugPrint('✅ Logout realizado com sucesso');
    } catch (e) {
      debugPrint('❌ Erro ao fazer logout: $e');
      _handleError('Erro ao fazer logout: $e');
    } finally {
      _isLoading.value = false;
    }
  }
}
