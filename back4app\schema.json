[{"className": "_Session", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "user": {"type": "Pointer", "targetClass": "_User"}, "installationId": {"type": "String"}, "sessionToken": {"type": "String"}, "expiresAt": {"type": "Date"}, "createdWith": {"type": "Object"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "AdminActionLog", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "admin": {"type": "Pointer", "targetClass": "_User"}, "action": {"type": "String"}, "details": {"type": "String"}, "ip": {"type": "String"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}}, {"className": "Paciente", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "nome": {"type": "String", "required": true}, "telefone": {"type": "String", "required": false}, "paciente_atendimento": {"type": "Relation", "targetClass": "Atendimento", "required": false}, "paciente_notificacao": {"type": "Relation", "targetClass": "Notificacao", "required": false}, "notificado": {"type": "Boolean"}, "pushToken": {"type": "String"}, "dataCadastro": {"type": "Date"}, "ultimoAcesso": {"type": "Date"}, "userId": {"type": "String"}, "deviceInfo": {"type": "Object"}, "ultima_fila": {"type": "Date"}, "em_fila": {"type": "Boolean"}, "ultimo_qrcode": {"type": "Pointer", "targetClass": "QRCodeGerado"}, "deviceFingerprint": {"type": "String"}, "deviceId": {"type": "String"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "_User", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "username": {"type": "String"}, "password": {"type": "String"}, "email": {"type": "String"}, "emailVerified": {"type": "Boolean"}, "authData": {"type": "Object"}, "nome": {"type": "String"}, "dataCadastro": {"type": "Date", "required": true}, "user_medico": {"type": "Relation", "targetClass": "Medico", "required": false}, "areaProfissional": {"type": "String"}, "consultorio_user": {"type": "Relation", "targetClass": "consultorio"}, "medico_user": {"type": "Relation", "targetClass": "Medico"}, "user_secretaria": {"type": "Relation", "targetClass": "Secretaria"}, "roles": {"type": "Array"}, "isAdmin": {"type": "Boolean", "defaultValue": false}, "tipo": {"type": "String"}, "user_consultorio": {"type": "Relation", "targetClass": "consultorio", "required": false}, "ativo": {"type": "Boolean"}, "area": {"type": "String"}, "emailVerificado": {"type": "Boolean"}}, "classLevelPermissions": {"find": {"*": true, "requiresAuthentication": true}, "count": {"*": true, "requiresAuthentication": true}, "get": {"*": true, "requiresAuthentication": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}, "username_1": {"username": 1}, "case_insensitive_username": {"username": 1}, "email_1": {"email": 1}, "case_insensitive_email": {"email": 1}}}, {"className": "Notificacao", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "tipo": {"type": "String"}, "solicitacao_id": {"type": "Pointer", "targetClass": "FilaSolicitacao"}, "fila_id": {"type": "Pointer", "targetClass": "<PERSON><PERSON>"}, "medico_id": {"type": "Pointer", "targetClass": "Medico"}, "consultorio_id": {"type": "Pointer", "targetClass": "consultorio"}, "lida": {"type": "Boolean", "defaultValue": false}, "created_at": {"type": "Date"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}}, {"className": "FilaSolicitacao", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "medicoId": {"type": "Pointer", "targetClass": "Medico"}, "hospitalId": {"type": "Pointer", "targetClass": "consultorio"}, "status": {"type": "String"}, "timestamp": {"type": "Date"}, "idPaciente": {"type": "String"}, "solicitacao_id": {"type": "String", "required": true}, "telefone": {"type": "String"}, "nome": {"type": "String"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}, "Index_FilaSolicitacao": {"_p_hospitalId": 1, "status": 1, "_created_at": -1}}}, {"className": "MetricasAtendimento", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "medico_id": {"type": "Pointer", "targetClass": "Medico", "required": true}, "consultorio_id": {"type": "Pointer", "targetClass": "consultorio", "required": true}, "data": {"type": "Date", "required": true}, "tempo_medio_espera": {"type": "Number", "defaultValue": 0}, "tempo_medio_atendimento": {"type": "Number", "defaultValue": 0}, "total_pacientes": {"type": "Number", "defaultValue": 0}, "pacientes_atendidos": {"type": "Number", "defaultValue": 0}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}}, {"className": "LogDebug", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "tipo": {"type": "String"}, "data": {"type": "Date"}, "dados": {"type": "Object"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "level": {"type": "String"}, "message": {"type": "String"}, "deviceInfo": {"type": "String"}, "timestamp": {"type": "Date"}, "data": {"type": "String"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "FilaUpdate", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "filaId": {"type": "String"}, "consultorioId": {"type": "String"}, "medicoId": {"type": "String"}, "acao": {"type": "String"}, "timestamp": {"type": "Date"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}}, {"className": "Installation", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "channels": {"type": "Array"}, "deviceToken": {"type": "String"}, "GCMSenderId": {"type": "String"}, "userId": {"type": "String"}, "pushType": {"type": "String"}, "deviceType": {"type": "String"}, "userType": {"type": "String"}, "installationId": {"type": "String"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "HospitalMedicoUpdate", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "hospitalId": {"type": "String"}, "medicoId": {"type": "String"}, "action": {"type": "String"}, "timestamp": {"type": "Date"}, "ativo": {"type": "Boolean"}, "reason": {"type": "String"}, "medicoNome": {"type": "String"}, "isActive": {"type": "Boolean"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "InstallationLog", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "deviceId": {"type": "String"}, "userId": {"type": "String"}, "userType": {"type": "String"}, "channels": {"type": "Array"}, "data_registro": {"type": "Date"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}}, {"className": "Medico", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "especialidade": {"type": "String", "required": true}, "crm": {"type": "String", "required": true}, "medico_medicoConsultorio": {"type": "Relation", "targetClass": "MedicoConsultorio", "required": false}, "medico_user": {"type": "Relation", "targetClass": "_User", "required": false}, "cpf": {"type": "Number", "required": true}, "user_medico": {"type": "Pointer", "targetClass": "_User"}, "ativo": {"type": "Boolean"}, "dataCadastro": {"type": "Date"}, "nome": {"type": "String", "required": true}, "email": {"type": "String"}, "telefone": {"type": "String"}, "user": {"type": "Pointer", "targetClass": "_User"}, "especializacao": {"type": "String"}, "perguntasPersonalizadas": {"type": "Array"}, "pesquisaSatisfacaoAtiva": {"type": "Boolean", "defaultValue": false}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "_Installation", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "installationId": {"type": "String"}, "deviceToken": {"type": "String"}, "channels": {"type": "Array"}, "deviceType": {"type": "String"}, "pushType": {"type": "String"}, "GCMSenderId": {"type": "String"}, "timeZone": {"type": "String"}, "localeIdentifier": {"type": "String"}, "badge": {"type": "Number"}, "appVersion": {"type": "String"}, "appName": {"type": "String"}, "appIdentifier": {"type": "String"}, "parseVersion": {"type": "String"}, "userId": {"type": "String"}, "userType": {"type": "String"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "NotificationErrorLog", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "timestamp": {"type": "Date"}, "titulo": {"type": "String"}, "error": {"type": "String"}, "tipo": {"type": "String"}, "mensagem": {"type": "String"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "FilaFeedback", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "filaId": {"type": "Pointer", "targetClass": "<PERSON><PERSON>"}, "demoraAtendimento": {"type": "Boolean"}, "imprevistos": {"type": "Boolean"}, "atendimentoOutroLocal": {"type": "Boolean"}, "filaGrande": {"type": "Boolean"}, "semPrevisaoAtendimento": {"type": "Boolean"}, "semResposta": {"type": "Boolean"}, "outroMotivo": {"type": "String"}, "dataFeedback": {"type": "Date"}, "perguntasPersonalizadas": {"type": "Array"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "QRCodeGerado", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "qr_id": {"type": "String"}, "sequencial": {"type": "String"}, "medico_id": {"type": "Pointer", "targetClass": "Medico"}, "consultorio_id": {"type": "Pointer", "targetClass": "consultorio"}, "valido": {"type": "Boolean"}, "impresso": {"type": "Boolean"}, "data_impressao": {"type": "Date"}, "data_expiracao": {"type": "Date"}, "versao": {"type": "String"}, "status": {"type": "String"}, "ultimo_acesso": {"type": "Date"}, "data_criacao": {"type": "Date"}, "data_invalidacao": {"type": "Date"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "MensagemFila", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "mensagem": {"type": "String", "required": true}, "tipo": {"type": "String"}, "medico_id": {"type": "Pointer", "targetClass": "Medico", "required": true}, "consultorio_id": {"type": "Pointer", "targetClass": "consultorio", "required": true}, "secretaria_id": {"type": "Pointer", "targetClass": "Secretaria"}, "data_envio": {"type": "Date", "required": true}, "ativa": {"type": "Boolean", "defaultValue": true}, "consultorio": {"type": "Pointer", "targetClass": "consultorio"}, "titulo": {"type": "String"}, "icone": {"type": "String"}, "medico": {"type": "Pointer", "targetClass": "Medico"}, "texto": {"type": "String"}, "prioridade": {"type": "String"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}}, {"className": "MensagemPredefinida", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "titulo": {"type": "String"}, "texto": {"type": "String"}, "consultorio": {"type": "Pointer", "targetClass": "consultorio"}, "prioridade": {"type": "String"}, "icone": {"type": "String"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "Notification", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "body": {"type": "String"}, "read": {"type": "Boolean"}, "title": {"type": "String"}, "data": {"type": "Object"}, "sent": {"type": "Boolean"}, "userId": {"type": "String"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "consultorio", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "localizacao": {"type": "GeoPoint"}, "latitude": {"type": "Number", "required": true}, "longitude": {"type": "Number", "required": true}, "consultorio_medicoConsultorio": {"type": "Relation", "targetClass": "MedicoConsultorio"}, "telefone": {"type": "String"}, "consultorio_user": {"type": "Pointer", "targetClass": "_User"}, "tipo": {"type": "String"}, "dataCadastro": {"type": "Date"}, "cnpj": {"type": "String", "required": true}, "nome": {"type": "String"}, "user_consultorio": {"type": "Pointer", "targetClass": "_User"}, "ativo": {"type": "Boolean"}, "medicos_vinculados": {"type": "Array"}, "specialties": {"type": "Array"}, "email": {"type": "String"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}, "_rperm_1": {"_rperm": 1}, "_id_1__rperm_1": {"_id": 1, "_rperm": 1}, "nome_1": {"nome": 1}}}, {"className": "QRCodeImpressaoLog", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "qr_code": {"type": "Pointer", "targetClass": "QRCodeGerado"}, "data_impressao": {"type": "Date"}, "medico_id": {"type": "String"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}}, {"className": "PasswordResetToken", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "token": {"type": "String"}, "user": {"type": "Pointer", "targetClass": "_User"}, "expiresAt": {"type": "Date"}, "used": {"type": "Boolean"}}, "classLevelPermissions": {"find": {"requiresAuthentication": true}, "count": {"requiresAuthentication": true}, "get": {"requiresAuthentication": true}, "create": {"requiresAuthentication": true}, "update": {"requiresAuthentication": true}, "delete": {"requiresAuthentication": true}, "addField": {"requiresMasterKey": true}, "protectedFields": {"*": ["token"]}}}, {"className": "SolicitacaoCancelamento", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "motivo": {"type": "String"}, "solicitacao_id": {"type": "String"}, "tempo_espera": {"type": "Number"}, "data_cancelamento": {"type": "Date"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "_Role", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "name": {"type": "String"}, "users": {"type": "Relation", "targetClass": "_User"}, "roles": {"type": "Relation", "targetClass": "_Role"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}, "name_1": {"name": 1}}}, {"className": "<PERSON><PERSON>", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "status": {"type": "String", "defaultValue": "a<PERSON>ando", "required": true}, "data_entrada": {"type": "Date", "required": true}, "data_inicio_atendimento": {"type": "Date"}, "data_fim_atendimento": {"type": "Date"}, "data_saida": {"type": "Date"}, "tempo_estimado_minutos": {"type": "Number"}, "posicao": {"type": "Number", "required": true}, "medico": {"type": "Pointer", "targetClass": "Medico", "required": true}, "consultorio": {"type": "Pointer", "targetClass": "consultorio", "required": true}, "nome": {"type": "String", "required": true}, "telefone": {"type": "String"}, "idPaciente": {"type": "String", "required": true}, "solicitacao": {"type": "Pointer", "targetClass": "FilaSolicitacao"}, "tempo_atendimento": {"type": "Number"}, "data_atendimento": {"type": "Date"}, "usuario": {"type": "Pointer", "targetClass": "Usuario"}, "lastModified": {"type": "Date"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}, "Index_fila": {"_p_medico": 1, "_p_consultorio": 1, "status": 1, "posicao": 1}}}, {"className": "Secretaria", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "nome": {"type": "String", "required": true}, "email": {"type": "String", "required": true}, "telefone": {"type": "String"}, "user_secretaria": {"type": "Pointer", "targetClass": "_User", "required": true}, "consultorio": {"type": "Pointer", "targetClass": "consultorio", "required": true}, "ativo": {"type": "Boolean", "defaultValue": true}, "dataCadastro": {"type": "Date"}, "cpf": {"type": "String"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "NotificacaoLog", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "titulo": {"type": "String"}, "mensagem": {"type": "String"}, "tipo": {"type": "String"}, "destinatarios_count": {"type": "Number"}, "canais_count": {"type": "Number"}, "data_envio": {"type": "Date"}, "sucesso": {"type": "Boolean"}, "erro": {"type": "String"}, "data_erro": {"type": "Date"}, "erro_detalhes": {"type": "String"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "MensagemUpdate", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "mensagemId": {"type": "String"}, "filaId": {"type": "String"}, "consultorioId": {"type": "String"}, "medicoId": {"type": "String"}, "idPaciente": {"type": "String"}, "broadcast": {"type": "Boolean"}, "timestamp": {"type": "Date"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}}, {"className": "Hospital", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "address": {"type": "String"}, "name": {"type": "String"}, "phone": {"type": "String"}, "state": {"type": "String"}, "capacity": {"type": "String"}, "specialties": {"type": "Array"}, "city": {"type": "String"}, "waitTimeAvg": {"type": "String"}, "email": {"type": "String"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "MedicoConsultorio", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "medicoConsultorio_atendimento": {"type": "Relation", "targetClass": "Atendimento", "required": false}, "medicoConsultorio_medico": {"type": "Relation", "targetClass": "Medico", "required": false}, "medicoConsultorio_consultorio": {"type": "Relation", "targetClass": "consultorio", "required": false}, "ativo": {"type": "Boolean"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "QRCodeLog", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "action": {"type": "String"}, "qr_code": {"type": "Pointer", "targetClass": "QRCodeGerado"}, "details": {"type": "String"}, "timestamp": {"type": "Date"}, "medico_id": {"type": "Pointer", "targetClass": "Medico"}, "consultorio_id": {"type": "Pointer", "targetClass": "consultorio"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "SecurityLog", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "user": {"type": "Pointer", "targetClass": "_User"}, "details": {"type": "String"}, "action": {"type": "String"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "fila_id": {"type": "Pointer", "targetClass": "<PERSON><PERSON>"}, "motivo_saida": {"type": "String"}, "observacao": {"type": "String"}, "created_at": {"type": "Date"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "PushDiagnostics", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "deviceId": {"type": "String"}, "report": {"type": "String"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}, {"className": "Emergencia", "fields": {"objectId": {"type": "String"}, "createdAt": {"type": "Date"}, "updatedAt": {"type": "Date"}, "ACL": {"type": "ACL"}, "tipo": {"type": "String"}, "fila": {"type": "Pointer", "targetClass": "<PERSON><PERSON>"}, "detalhes": {"type": "String"}, "status": {"type": "String"}, "data_registro": {"type": "Date"}}, "classLevelPermissions": {"find": {"*": true}, "count": {"*": true}, "get": {"*": true}, "create": {"*": true}, "update": {"*": true}, "delete": {"*": true}, "addField": {"*": true}, "protectedFields": {"*": []}}, "indexes": {"_id_": {"_id": 1}}}]