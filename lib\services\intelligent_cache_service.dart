import 'dart:convert';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import '../models/mensagem_fila.dart';
import '../utils/date_utils.dart';

/// 💾 SERVIÇO DE CACHE INTELIGENTE
/// Reduz requisições e otimiza performance do app
class IntelligentCacheService {
  static const String _tag = '[CACHE_SERVICE]';

  // ✅ CHAVES DE CACHE
  static const String _filaDataPrefix = 'fila_data_';
  static const String _messagesPrefix = 'messages_';
  static const String _cacheMetadataPrefix = 'cache_meta_';

  // ✅ TEMPOS DE EXPIRAÇÃO (EM SEGUNDOS)
  static const int _defaultCacheExpiry = 300; // 5 minutos
  static const int _fastRefreshExpiry = 30; // 30 segundos para refresh rápido
  static const int _mediumRefreshExpiry = 120; // 2 minutos para médio
  static const int _slowRefreshExpiry = 600; // 10 minutos para lento

  late SharedPreferences _prefs;
  bool _isInitialized = false;

  /// ✅ INICIALIZAR SERVIÇO
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();
      _isInitialized = true;
      debugPrint('$_tag ✅ Serviço de cache inicializado');

      // ✅ LIMPAR CACHE EXPIRADO NA INICIALIZAÇÃO
      await _cleanExpiredCache();
    } catch (e) {
      debugPrint('$_tag ❌ Erro ao inicializar cache: $e');
    }
  }

  /// ✅ CACHE DE DADOS DA FILA
  Future<void> cacheFilaData(String filaId, Map<String, dynamic> data) async {
    await _ensureInitialized();

    try {
      final cacheKey = '$_filaDataPrefix$filaId';
      final metadataKey = '$_cacheMetadataPrefix$filaId';

      // ✅ PREPARAR DADOS PARA CACHE
      final cacheData = {
        'data': data,
        'timestamp':
            TimezoneUtils.getNowInDeviceTimezone().millisecondsSinceEpoch,
        'version': '1.0',
      };

      // ✅ SALVAR DADOS
      await _prefs.setString(cacheKey, jsonEncode(cacheData));

      // ✅ SALVAR METADATA
      final metadata = {
        'lastUpdate':
            TimezoneUtils.getNowInDeviceTimezone().millisecondsSinceEpoch,
        'strategy': 'normal_refresh',
        'hitCount': 0,
      };
      await _prefs.setString(metadataKey, jsonEncode(metadata));

      debugPrint('$_tag 💾 Dados da fila $filaId salvos no cache');
    } catch (e) {
      debugPrint('$_tag ❌ Erro ao salvar cache da fila: $e');
    }
  }

  /// ✅ OBTER DADOS DA FILA DO CACHE
  Future<Map<String, dynamic>?> getCachedFilaData(String filaId) async {
    await _ensureInitialized();

    try {
      final cacheKey = '$_filaDataPrefix$filaId';
      final metadataKey = '$_cacheMetadataPrefix$filaId';

      final cachedString = _prefs.getString(cacheKey);
      if (cachedString == null) {
        debugPrint('$_tag 🔍 Cache miss para fila $filaId');
        return null;
      }

      final cachedData = jsonDecode(cachedString) as Map<String, dynamic>;
      final timestamp = cachedData['timestamp'] as int;

      // ✅ VERIFICAR EXPIRAÇÃO
      final now = TimezoneUtils.getNowInDeviceTimezone().millisecondsSinceEpoch;
      final age = now - timestamp;

      if (age > (_defaultCacheExpiry * 1000)) {
        debugPrint(
            '$_tag ⏰ Cache expirado para fila $filaId (${age ~/ 1000}s)');
        await _removeCache(cacheKey, metadataKey);
        return null;
      }

      // ✅ ATUALIZAR ESTATÍSTICAS
      await _updateCacheHitCount(metadataKey);

      debugPrint(
          '$_tag 💾 Cache hit para fila $filaId (${age ~/ 1000}s de idade)');
      return cachedData['data'] as Map<String, dynamic>;
    } catch (e) {
      debugPrint('$_tag ❌ Erro ao obter cache da fila: $e');
      return null;
    }
  }

  /// ✅ VERIFICAR SE DEVE ATUALIZAR CACHE
  bool shouldRefreshCache(Map<String, dynamic>? cachedData, String strategy) {
    if (cachedData == null) return true;

    try {
      final timestamp = cachedData['timestamp'] as int?;
      if (timestamp == null) return true;

      final now = TimezoneUtils.getNowInDeviceTimezone().millisecondsSinceEpoch;
      final age = (now - timestamp) / 1000; // idade em segundos

      // ✅ ESTRATÉGIAS DE REFRESH BASEADAS NO CONTEXTO
      final expiry = _getExpiryForStrategy(strategy);

      final shouldRefresh = age > expiry;

      if (shouldRefresh) {
        debugPrint(
            '$_tag 🔄 Cache precisa refresh: ${age.toInt()}s > ${expiry}s (strategy: $strategy)');
      } else {
        debugPrint(
            '$_tag ✅ Cache ainda válido: ${age.toInt()}s < ${expiry}s (strategy: $strategy)');
      }

      return shouldRefresh;
    } catch (e) {
      debugPrint('$_tag ❌ Erro ao verificar refresh: $e');
      return true;
    }
  }

  /// ✅ CACHE DE MENSAGENS
  Future<void> cacheMessages(
      String filaId, List<MensagemFila> mensagens) async {
    await _ensureInitialized();

    try {
      final cacheKey = '$_messagesPrefix$filaId';

      // ✅ CONVERTER MENSAGENS PARA JSON
      final mensagensJson = mensagens
          .map((m) => {
                'id': m.id,
                'titulo': m.titulo,
                'texto': m.texto,
                'dataEnvio': m.dataEnvio.millisecondsSinceEpoch,
                'medicoId': m.medicoId,
                'prioridade': m.prioridade,
                'icone': m.icone,
              })
          .toList();

      final cacheData = {
        'mensagens': mensagensJson,
        'timestamp':
            TimezoneUtils.getNowInDeviceTimezone().millisecondsSinceEpoch,
        'count': mensagens.length,
      };

      await _prefs.setString(cacheKey, jsonEncode(cacheData));
      debugPrint(
          '$_tag 📨 ${mensagens.length} mensagens salvas no cache para fila $filaId');
    } catch (e) {
      debugPrint('$_tag ❌ Erro ao salvar mensagens no cache: $e');
    }
  }

  /// ✅ OBTER MENSAGENS DO CACHE
  Future<List<MensagemFila>?> getCachedMessages(
      String filaId, String medicoNome) async {
    await _ensureInitialized();

    try {
      final cacheKey = '$_messagesPrefix$filaId';
      final cachedString = _prefs.getString(cacheKey);

      if (cachedString == null) {
        debugPrint('$_tag 📨 Cache miss para mensagens da fila $filaId');
        return null;
      }

      final cachedData = jsonDecode(cachedString) as Map<String, dynamic>;
      final timestamp = cachedData['timestamp'] as int;

      // ✅ VERIFICAR EXPIRAÇÃO (MENSAGENS EXPIRAM MAIS RÁPIDO)
      final now = TimezoneUtils.getNowInDeviceTimezone().millisecondsSinceEpoch;
      final age = now - timestamp;

      if (age > (120 * 1000)) {
        // 2 minutos para mensagens
        debugPrint('$_tag ⏰ Cache de mensagens expirado para fila $filaId');
        await _prefs.remove(cacheKey);
        return null;
      }

      // ✅ CONVERTER DE VOLTA PARA OBJETOS
      final mensagensJson = cachedData['mensagens'] as List<dynamic>;
      final mensagens = mensagensJson
          .map((m) => MensagemFila(
                id: m['id'] ?? 'unknown',
                titulo: m['titulo'] ?? 'Aviso',
                texto: m['texto'] ?? '',
                dataEnvio:
                    DateTime.fromMillisecondsSinceEpoch(m['dataEnvio'] ?? 0),
                medicoId: m['medicoId'] ?? '',
                prioridade: m['prioridade'] ?? 'media',
                icone: m['icone'] ?? 'notification',
              ))
          .toList();

      debugPrint('$_tag 📨 ${mensagens.length} mensagens obtidas do cache');
      return mensagens;
    } catch (e) {
      debugPrint('$_tag ❌ Erro ao obter mensagens do cache: $e');
      return null;
    }
  }

  /// ✅ CACHE DE POSIÇÃO ANTERIOR (PARA DETECTAR MUDANÇAS)
  Future<void> cachePreviousPosition(String filaId, int posicao) async {
    await _ensureInitialized();

    try {
      await _prefs.setInt('pos_$filaId', posicao);
      debugPrint('$_tag 📍 Posição anterior $posicao salva para fila $filaId');
    } catch (e) {
      debugPrint('$_tag ❌ Erro ao salvar posição anterior: $e');
    }
  }

  /// ✅ OBTER POSIÇÃO ANTERIOR
  Future<int?> getPreviousPosition(String filaId) async {
    await _ensureInitialized();
    return _prefs.getInt('pos_$filaId');
  }

  /// ✅ CACHE DE DADOS DO MÉDICO E HOSPITAL
  Future<void> cacheMedicoHospitalData(
      String medicoId, Map<String, dynamic> data) async {
    await _ensureInitialized();

    try {
      final cacheKey = 'medico_$medicoId';
      final cacheData = {
        'data': data,
        'timestamp':
            TimezoneUtils.getNowInDeviceTimezone().millisecondsSinceEpoch,
      };

      await _prefs.setString(cacheKey, jsonEncode(cacheData));
      debugPrint('$_tag 👨‍⚕️ Dados do médico $medicoId salvos no cache');
    } catch (e) {
      debugPrint('$_tag ❌ Erro ao salvar dados do médico: $e');
    }
  }

  /// ✅ OBTER DADOS DO MÉDICO DO CACHE
  Future<Map<String, dynamic>?> getCachedMedicoData(String medicoId) async {
    await _ensureInitialized();

    try {
      final cacheKey = 'medico_$medicoId';
      final cachedString = _prefs.getString(cacheKey);

      if (cachedString == null) return null;

      final cachedData = jsonDecode(cachedString) as Map<String, dynamic>;
      final timestamp = cachedData['timestamp'] as int;

      // ✅ DADOS DO MÉDICO EXPIRAM EM 1 HORA
      final now = TimezoneUtils.getNowInDeviceTimezone().millisecondsSinceEpoch;
      final age = now - timestamp;

      if (age > (3600 * 1000)) {
        // 1 hora
        await _prefs.remove(cacheKey);
        return null;
      }

      return cachedData['data'] as Map<String, dynamic>;
    } catch (e) {
      debugPrint('$_tag ❌ Erro ao obter dados do médico: $e');
      return null;
    }
  }

  /// ✅ LIMPAR CACHE DE UMA FILA ESPECÍFICA
  Future<void> clearFilaCache(String filaId) async {
    await _ensureInitialized();

    try {
      final keys = [
        '$_filaDataPrefix$filaId',
        '$_messagesPrefix$filaId',
        '$_cacheMetadataPrefix$filaId',
        'pos_$filaId',
      ];

      for (final key in keys) {
        await _prefs.remove(key);
      }

      debugPrint('$_tag 🧹 Cache limpo para fila $filaId');
    } catch (e) {
      debugPrint('$_tag ❌ Erro ao limpar cache da fila: $e');
    }
  }

  /// ✅ LIMPAR TODO O CACHE
  Future<void> clearAllCache() async {
    await _ensureInitialized();

    try {
      final keys = _prefs.getKeys();
      final cacheKeys = keys
          .where((key) =>
              key.startsWith(_filaDataPrefix) ||
              key.startsWith(_messagesPrefix) ||
              key.startsWith(_cacheMetadataPrefix) ||
              key.startsWith('pos_') ||
              key.startsWith('medico_'))
          .toList();

      for (final key in cacheKeys) {
        await _prefs.remove(key);
      }

      debugPrint(
          '$_tag 🧹 Todo o cache foi limpo (${cacheKeys.length} chaves)');
    } catch (e) {
      debugPrint('$_tag ❌ Erro ao limpar todo o cache: $e');
    }
  }

  /// ✅ OBTER ESTATÍSTICAS DO CACHE
  Future<Map<String, dynamic>> getCacheStats() async {
    await _ensureInitialized();

    try {
      final keys = _prefs.getKeys();

      final filaCount = keys.where((k) => k.startsWith(_filaDataPrefix)).length;
      final messageCount =
          keys.where((k) => k.startsWith(_messagesPrefix)).length;
      final medicoCount = keys.where((k) => k.startsWith('medico_')).length;

      return {
        'totalKeys': keys.length,
        'filaDataCached': filaCount,
        'messagesCached': messageCount,
        'medicoCached': medicoCount,
        'lastCleanup': await _getLastCleanupTime(),
      };
    } catch (e) {
      debugPrint('$_tag ❌ Erro ao obter estatísticas: $e');
      return {};
    }
  }

  /// ✅ MÉTODOS PRIVADOS

  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  int _getExpiryForStrategy(String strategy) {
    switch (strategy) {
      case 'fast_refresh':
        return _fastRefreshExpiry;
      case 'medium_refresh':
        return _mediumRefreshExpiry;
      case 'slow_refresh':
        return _slowRefreshExpiry;
      default:
        return _defaultCacheExpiry;
    }
  }

  Future<void> _updateCacheHitCount(String metadataKey) async {
    try {
      final metadataString = _prefs.getString(metadataKey);
      if (metadataString != null) {
        final metadata = jsonDecode(metadataString) as Map<String, dynamic>;
        metadata['hitCount'] = (metadata['hitCount'] ?? 0) + 1;
        await _prefs.setString(metadataKey, jsonEncode(metadata));
      }
    } catch (e) {
      // Ignorar erros de estatísticas
    }
  }

  Future<void> _removeCache(String cacheKey, String metadataKey) async {
    try {
      await _prefs.remove(cacheKey);
      await _prefs.remove(metadataKey);
    } catch (e) {
      debugPrint('$_tag ❌ Erro ao remover cache: $e');
    }
  }

  /// ✅ LIMPEZA AUTOMÁTICA DE CACHE EXPIRADO
  Future<void> _cleanExpiredCache() async {
    try {
      final now = TimezoneUtils.getNowInDeviceTimezone().millisecondsSinceEpoch;
      final keys = _prefs.getKeys();
      int removedCount = 0;

      for (final key in keys) {
        if (key.startsWith(_filaDataPrefix) ||
            key.startsWith(_messagesPrefix) ||
            key.startsWith('medico_')) {
          final cachedString = _prefs.getString(key);
          if (cachedString != null) {
            try {
              final cachedData =
                  jsonDecode(cachedString) as Map<String, dynamic>;
              final timestamp = cachedData['timestamp'] as int?;

              if (timestamp != null) {
                final age = now - timestamp;

                // ✅ REMOVER CACHE MUITO ANTIGO (MAIS DE 2 HORAS)
                if (age > (2 * 3600 * 1000)) {
                  await _prefs.remove(key);
                  removedCount++;
                }
              }
            } catch (e) {
              // Se não conseguir fazer parse, remover a chave corrompida
              await _prefs.remove(key);
              removedCount++;
            }
          }
        }
      }

      if (removedCount > 0) {
        debugPrint(
            '$_tag 🧹 Limpeza automática: $removedCount chaves expiradas removidas');
      }

      // ✅ SALVAR TIMESTAMP DA ÚLTIMA LIMPEZA
      await _prefs.setInt('last_cache_cleanup', now);
    } catch (e) {
      debugPrint('$_tag ❌ Erro na limpeza automática: $e');
    }
  }

  Future<int?> _getLastCleanupTime() async {
    return _prefs.getInt('last_cache_cleanup');
  }

  /// ✅ CACHE PREDITIVO BASEADO EM PADRÕES
  Future<void> predictiveCache(String filaId, int currentPosition) async {
    try {
      // ✅ CACHE BASEADO NA POSIÇÃO ATUAL
      if (currentPosition <= 5) {
        // Se está próximo, usar cache rápido
        await _setCacheStrategy(filaId, 'fast_refresh');
      } else if (currentPosition <= 15) {
        // Posição média, cache médio
        await _setCacheStrategy(filaId, 'medium_refresh');
      } else {
        // Posição distante, cache lento
        await _setCacheStrategy(filaId, 'slow_refresh');
      }

      debugPrint(
          '$_tag 🔮 Cache preditivo configurado para posição $currentPosition');
    } catch (e) {
      debugPrint('$_tag ❌ Erro no cache preditivo: $e');
    }
  }

  Future<void> _setCacheStrategy(String filaId, String strategy) async {
    try {
      final metadataKey = '$_cacheMetadataPrefix$filaId';
      final metadataString = _prefs.getString(metadataKey);

      Map<String, dynamic> metadata;
      if (metadataString != null) {
        metadata = jsonDecode(metadataString) as Map<String, dynamic>;
      } else {
        metadata = {};
      }

      metadata['strategy'] = strategy;
      metadata['strategySetAt'] =
          TimezoneUtils.getNowInDeviceTimezone().millisecondsSinceEpoch;

      await _prefs.setString(metadataKey, jsonEncode(metadata));
    } catch (e) {
      debugPrint('$_tag ❌ Erro ao configurar estratégia: $e');
    }
  }

  /// ✅ CACHE DE CONFIGURAÇÕES TEMPORÁRIAS
  Future<void> setCacheConfig(String key, dynamic value) async {
    await _ensureInitialized();

    try {
      final configKey = 'config_$key';
      await _prefs.setString(
          configKey,
          jsonEncode({
            'value': value,
            'timestamp':
                TimezoneUtils.getNowInDeviceTimezone().millisecondsSinceEpoch,
          }));
    } catch (e) {
      debugPrint('$_tag ❌ Erro ao salvar configuração: $e');
    }
  }

  Future<T?> getCacheConfig<T>(String key) async {
    await _ensureInitialized();

    try {
      final configKey = 'config_$key';
      final configString = _prefs.getString(configKey);

      if (configString != null) {
        final config = jsonDecode(configString) as Map<String, dynamic>;
        return config['value'] as T?;
      }

      return null;
    } catch (e) {
      debugPrint('$_tag ❌ Erro ao obter configuração: $e');
      return null;
    }
  }
}
