import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:fila_app/modules/admin/controllers/admin_controller.dart';
import 'package:fila_app/controllers/login_controller.dart';
import 'package:fila_app/theme/theme.dart';
import 'package:fila_app/modules/admin/widgets/hospitals_list_view.dart';
import 'package:fila_app/modules/admin/widgets/hospital_form_view.dart';

class AdminScreen extends StatefulWidget {
  const AdminScreen({super.key});

  @override
  State<AdminScreen> createState() => _AdminScreenState();
}

class _AdminScreenState extends State<AdminScreen>
    with SingleTickerProviderStateMixin {
  final AdminController controller = Get.find<AdminController>();
  final LoginController loginController = Get.find<LoginController>();

  // View controller
  final RxInt _currentView = 0.obs;
  final RxBool _isLoading = false.obs;
  final RxBool _showMenu = false.obs;

  // Filtros e ordenação
  final RxString _filterStatus = 'Todos'.obs;
  final RxString _sortBy = 'Nome (A-Z)'.obs;
  final RxBool _showFilterOptions = false.obs;
  final RxBool _showSortOptions = false.obs;

  // Animation controller for transitions
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  // Metrics - usar as métricas estáticas do AdminController
  // final RxMap<String, dynamic> _dashboardMetrics = {
  //   'hospitais': 0,
  //   'ativos': 0,
  //   'inativos': 0,
  // }.obs;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _slideAnimation = Tween<double>(begin: 0.2, end: 0.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOutCubic,
      ),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    _animationController.forward();

    // ✅ NAVEGAÇÃO AUTOMÁTICA PARA EDIÇÃO: Escutar flag de navegação
    ever(controller.shouldNavigateToEditForm, (shouldNavigate) {
      if (shouldNavigate) {
        debugPrint('Flag de navegação ativada - mudando para tela de edição');
        _changeView(1); // Mudar para a tela de formulário
        controller.shouldNavigateToEditForm.value = false; // Reset da flag
      }
    });

    // ✅ OBSERVADOR PARA ATUALIZAR MÉTRICAS: Escutar mudanças no carregamento da lista
    ever(controller.isLoadingList, (isLoading) {
      if (!isLoading) {
        _updateDashboardMetrics();
      }
    });

    // Load data
    _loadData();
  }

  Future<void> _loadData() async {
    _isLoading.value = true;
    await controller.carregarHospitais();

    // Update dashboard metrics
    _updateDashboardMetrics();
    _isLoading.value = false;
  }

  void _updateDashboardMetrics() {
    int total = controller.hospitais.length;
    int ativos =
        controller.hospitais.where((h) => h.get<bool>('ativo') ?? false).length;

    AdminController.dashboardMetrics.update('hospitais', (_) => total);
    AdminController.dashboardMetrics.update('ativos', (_) => ativos);
    AdminController.dashboardMetrics.update('inativos', (_) => total - ativos);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _changeView(int index) {
    if (_currentView.value == index) return;

    if (index == 0 && _currentView.value == 1) {
      // Check for unsaved changes when going from form to dashboard
      if (controller.isEditMode.value && controller.formHasChanges()) {
        _showExitConfirmationDialog();
        return;
      }
    }

    _animationController.reverse().then((_) {
      _currentView.value = index;
      _animationController.forward();
    });

    _showMenu.value = false;
  }

  void _showExitConfirmationDialog() {
    showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        elevation: 5,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          'Alterações não salvas',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        content: const Text(
          'Você tem alterações não salvas. Deseja sair sem salvar?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(
              'Cancelar',
              style: TextStyle(color: AppTheme.accentColor),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop(true);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.redAccent,
              foregroundColor: Colors.white,
            ),
            child: const Text('Sair sem salvar'),
          ),
        ],
      ),
    ).then((value) {
      if (value == true) {
        // ✅ CORREÇÃO: Limpar o formulário antes de navegar
        controller.limparFormulario();
        _changeView(0);
      }
    });
  }

  Future<void> _handleLogout() async {
    final confirmed = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            backgroundColor: Colors.white,
            elevation: 5,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: const Text(
              'Confirmar Logout',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            content: const Text('Tem certeza que deseja sair?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text(
                  'Cancelar',
                  style: TextStyle(color: AppTheme.accentColor),
                ),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                ),
                child: const Text('Confirmar'),
              ),
            ],
          ),
        ) ??
        false;

    if (confirmed && mounted) {
      await loginController.logout();
      Get.offAllNamed('/login');
    }
  }

  void _createNewHospital() {
    showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        elevation: 5,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: const Text(
          'Novo Cadastro',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        content: const Text('Deseja iniciar um novo cadastro de consultório?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(
              'Cancelar',
              style: TextStyle(color: AppTheme.accentColor),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
            ),
            child: const Text('Confirmar'),
          ),
        ],
      ),
    ).then((confirmed) {
      if (confirmed == true) {
        controller.limparFormulario();
        _changeView(1);
      }
    });
  }

  // Métodos de filtro e ordenação
  void _applyFilter(String filter) {
    _filterStatus.value = filter;
    _showFilterOptions.value = false;
  }

  void _applySorting(String sortOption) {
    _sortBy.value = sortOption;
    _showSortOptions.value = false;
  }

  List<dynamic> _getFilteredAndSortedHospitals() {
    var filteredList = controller.hospitais.where((hospital) {
      if (_filterStatus.value == 'Todos') {
        return true;
      } else if (_filterStatus.value == 'Ativos') {
        return hospital.get<bool>('ativo') ?? false;
      } else if (_filterStatus.value == 'Inativos') {
        return !(hospital.get<bool>('ativo') ?? false);
      } else if (_filterStatus.value == 'Públicos') {
        return (hospital.get<String>('tipo') ?? '').toLowerCase() == 'público';
      } else if (_filterStatus.value == 'Particulares') {
        return (hospital.get<String>('tipo') ?? '').toLowerCase() ==
            'particular';
      }
      return true;
    }).toList();

    // Aplicar ordenação
    filteredList.sort((a, b) {
      switch (_sortBy.value) {
        case 'Nome (A-Z)':
          return (a.get<String>('nome') ?? '')
              .compareTo(b.get<String>('nome') ?? '');
        case 'Nome (Z-A)':
          return (b.get<String>('nome') ?? '')
              .compareTo(a.get<String>('nome') ?? '');
        case 'Mais recentes':
          final dateA = a.get<DateTime>('dataCadastro');
          final dateB = b.get<DateTime>('dataCadastro');
          if (dateA == null || dateB == null) return 0;
          return dateB.compareTo(dateA);
        case 'Mais antigos':
          final dateA = a.get<DateTime>('dataCadastro');
          final dateB = b.get<DateTime>('dataCadastro');
          if (dateA == null || dateB == null) return 0;
          return dateA.compareTo(dateB);
        default:
          return 0;
      }
    });

    return filteredList;
  }

  // Função para mostrar o popover de filtros
  Widget _buildFilterPopover() {
    return Positioned(
      top: 60,
      right: 80,
      child: AnimatedOpacity(
        opacity: _showFilterOptions.value ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 200),
        child: Visibility(
          visible: _showFilterOptions.value,
          child: Material(
            elevation: 8,
            borderRadius: BorderRadius.circular(8),
            child: Container(
              width: 180,
              padding: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildFilterOption('Todos'),
                  _buildFilterOption('Ativos'),
                  _buildFilterOption('Inativos'),
                  const Divider(height: 4),
                  _buildFilterOption('Públicos'),
                  _buildFilterOption('Particulares'),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Função para mostrar o popover de ordenação
  Widget _buildSortPopover() {
    return Positioned(
      top: 60,
      right: 12,
      child: AnimatedOpacity(
        opacity: _showSortOptions.value ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 200),
        child: Visibility(
          visible: _showSortOptions.value,
          child: Material(
            elevation: 8,
            borderRadius: BorderRadius.circular(8),
            child: Container(
              width: 180,
              padding: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildSortOption('Nome (A-Z)'),
                  _buildSortOption('Nome (Z-A)'),
                  const Divider(height: 4),
                  _buildSortOption('Mais recentes'),
                  _buildSortOption('Mais antigos'),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFilterOption(String option) {
    final isSelected = _filterStatus.value == option;

    return InkWell(
      onTap: () => _applyFilter(option),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        color: isSelected ? Colors.grey.shade100 : Colors.transparent,
        child: Row(
          children: [
            Text(
              option,
              style: TextStyle(
                color: isSelected ? AppTheme.primaryColor : Colors.black87,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
            const Spacer(),
            if (isSelected)
              Icon(
                Icons.check,
                size: 18,
                color: AppTheme.primaryColor,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSortOption(String option) {
    final isSelected = _sortBy.value == option;

    return InkWell(
      onTap: () => _applySorting(option),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        color: isSelected ? Colors.grey.shade100 : Colors.transparent,
        child: Row(
          children: [
            Text(
              option,
              style: TextStyle(
                color: isSelected ? AppTheme.primaryColor : Colors.black87,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
            const Spacer(),
            if (isSelected)
              Icon(
                Icons.check,
                size: 18,
                color: AppTheme.primaryColor,
              ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;

    // ✅ CORREÇÃO: Adicionar PopScope para controlar navegação do botão voltar
    return PopScope(
      canPop: false, // Sempre interceptar tentativas de navegação
      onPopInvokedWithResult: (bool didPop, dynamic result) async {
        if (didPop) return; // Se já saiu, não fazer nada

        // Verificar se está no formulário e tem mudanças não salvas
        if (_currentView.value == 1 &&
            controller.isEditMode.value &&
            controller.formHasChanges()) {
          // Mostrar dialog de confirmação
          _showExitConfirmationDialog();
          return;
        }

        // Se está no dashboard ou não tem mudanças, permitir navegação
        if (_currentView.value == 0) {
          // Está no dashboard, pode sair do app
          Navigator.of(context).pop();
        } else {
          // Está no formulário mas sem mudanças, voltar para dashboard
          _changeView(0);
        }
      },
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        body: GestureDetector(
          onTap: () {
            // Fechar os popovers quando tocar fora deles
            _showFilterOptions.value = false;
            _showSortOptions.value = false;
          },
          child: Stack(
            children: [
              // Background with gradient header
              Column(
                children: [
                  Container(
                    height: 180,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          AppTheme.primaryColor,
                          AppTheme.primaryDarkColor,
                        ],
                      ),
                      borderRadius: const BorderRadius.only(
                        bottomLeft: Radius.circular(30),
                        bottomRight: Radius.circular(30),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.3),
                          spreadRadius: 2,
                          blurRadius: 7,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                  ),
                  Expanded(child: Container()),
                ],
              ),

              // Main content
              SafeArea(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Custom App Bar
                    _buildAppBar(),

                    // Main content
                    Expanded(
                      child: Obx(() => FadeTransition(
                            opacity: _fadeAnimation,
                            child: SlideTransition(
                              position: Tween<Offset>(
                                      begin: Offset(_slideAnimation.value, 0),
                                      end: Offset.zero)
                                  .animate(_animationController),
                              child: _currentView.value == 0
                                  ? _buildDashboard(screenSize)
                                  : _buildFormView(),
                            ),
                          )),
                    ),
                  ],
                ),
              ),

              // Overlay menu (quando visível)
              Obx(() => _showMenu.value
                  ? _buildMenuOverlay()
                  : const SizedBox.shrink()),

              // Filtro e ordenação popovers
              Obx(() => _buildFilterPopover()),
              Obx(() => _buildSortPopover()),
            ],
          ),
        ),
        floatingActionButton: Obx(
          () => _currentView.value == 0
              ? FloatingActionButton.extended(
                  onPressed: _createNewHospital,
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  elevation: 4,
                  icon: const Icon(Icons.add),
                  label: const Text('Novo Consultório'),
                )
              : FloatingActionButton(
                  onPressed: () => _changeView(0),
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  elevation: 4,
                  child: const Icon(Icons.arrow_back),
                ),
        ),
      ),
    );
  }

  Widget _buildMenuOverlay() {
    return GestureDetector(
      onTap: () => _showMenu.value = false,
      child: Container(
        color: Colors.black54,
        width: double.infinity,
        height: double.infinity,
        child: Stack(
          children: [
            Positioned(
              top: 70,
              right: 20,
              child: Material(
                elevation: 8,
                borderRadius: BorderRadius.circular(12),
                color: Colors.white,
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildMenuOption(
                        icon: Icons.refresh,
                        label: 'Atualizar dados',
                        onTap: () {
                          _loadData();
                          _showMenu.value = false;
                        },
                      ),
                      const Divider(height: 2),
                      _buildMenuOption(
                        icon: Icons.logout,
                        label: 'Sair',
                        onTap: () {
                          _showMenu.value = false;
                          _handleLogout();
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 12.0),
        child: Row(
          children: [
            Icon(icon, color: AppTheme.accentColor, size: 20),
            const SizedBox(width: 12),
            Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Obx(() => Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _currentView.value == 0
                        ? 'Painel Administrativo'
                        : 'Consultório',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _currentView.value == 0
                        ? 'Gerencie seus consultórios'
                        : controller.isEditMode.value
                            ? 'Edição de dados'
                            : 'Novo cadastro',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.white,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              )),
          InkWell(
            onTap: () => _showMenu.value = true,
            borderRadius: BorderRadius.circular(50),
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.3),
                borderRadius: BorderRadius.circular(50),
              ),
              child: const Icon(
                Icons.more_vert,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboard(Size screenSize) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Stats cards - FIXOS NO TOPO
          SizedBox(
            width: double.infinity,
            height: 120,
            child: Obx(() => Row(
                  children: [
                    _buildStatCard(
                      icon: Icons.business,
                      iconColor: Colors.blue,
                      title: 'Total',
                      value: AdminController.dashboardMetrics['hospitais']
                          .toString(),
                      subtitle: 'Consultórios',
                    ),
                    const SizedBox(width: 16),
                    _buildStatCard(
                      icon: Icons.check_circle,
                      iconColor: Colors.green,
                      title: 'Ativos',
                      value:
                          AdminController.dashboardMetrics['ativos'].toString(),
                      subtitle: 'Consultórios',
                    ),
                    const SizedBox(width: 16),
                    _buildStatCard(
                      icon: Icons.cancel,
                      iconColor: Colors.redAccent,
                      title: 'Inativos',
                      value: AdminController.dashboardMetrics['inativos']
                          .toString(),
                      subtitle: 'Consultórios',
                    ),
                  ],
                )),
          ),

          const SizedBox(height: 24),

          // Cabeçalho da lista e filtros - FIXOS
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Consultórios',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Obx(() => Text(
                        'Filtro: ${_filterStatus.value} • Ordenação: ${_sortBy.value}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      )),
                ],
              ),
              Row(
                children: [
                  _buildFilterButton(
                    icon: Icons.filter_list,
                    tooltip: 'Filtrar',
                    onPressed: () {
                      _showSortOptions.value = false;
                      _showFilterOptions.toggle();
                    },
                    isActive: _filterStatus.value != 'Todos',
                  ),
                  const SizedBox(width: 8),
                  _buildFilterButton(
                    icon: Icons.sort,
                    tooltip: 'Ordenar',
                    onPressed: () {
                      _showFilterOptions.value = false;
                      _showSortOptions.toggle();
                    },
                    isActive: _sortBy.value != 'Nome (A-Z)',
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Lista de hospitais - ROLÁVEL
          Expanded(
            child: Obx(() {
              if (_isLoading.value) {
                return const Center(
                  child: Padding(
                    padding: EdgeInsets.all(32.0),
                    child: CircularProgressIndicator(),
                  ),
                );
              }

              final filteredHospitals = _getFilteredAndSortedHospitals();

              if (controller.hospitais.isEmpty) {
                return _buildEmptyState();
              }

              if (filteredHospitals.isEmpty) {
                return Center(
                  child: Padding(
                    padding: const EdgeInsets.all(32.0),
                    child: Column(
                      children: [
                        Icon(
                          Icons.filter_list_off,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Nenhum consultório encontrado com o filtro atual',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton.icon(
                          onPressed: () => _applyFilter('Todos'),
                          icon: const Icon(Icons.clear_all),
                          label: const Text('Limpar filtros'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryColor,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }

              // Lista com RefreshIndicator integrado
              return RefreshIndicator(
                onRefresh: _loadData,
                color: AppTheme.primaryColor,
                child: HospitalsListView(
                  hospitals: filteredHospitals,
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.business_outlined,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Nenhum consultório cadastrado',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Clique no botão "Novo Consultório" para começar',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _createNewHospital,
              icon: const Icon(Icons.add),
              label: const Text('Novo Consultório'),
              style: ElevatedButton.styleFrom(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String value,
    required String subtitle,
  }) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(icon, color: iconColor, size: 24),
            const Spacer(),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: iconColor,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterButton({
    required IconData icon,
    required String tooltip,
    required VoidCallback onPressed,
    bool isActive = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isActive ? AppTheme.primaryColor.withOpacity(0.1) : Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: IconButton(
        icon: Icon(
          icon,
          color: isActive ? AppTheme.primaryColor : AppTheme.accentColor,
        ),
        onPressed: onPressed,
        tooltip: tooltip,
        constraints: const BoxConstraints(
          minHeight: 40,
          minWidth: 40,
        ),
      ),
    );
  }

  Widget _buildFormView() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: HospitalFormView(
        onFormSubmitted: () => _changeView(0),
      ),
    );
  }
}

// Classe wrapper para manter compatibilidade com rotas existentes
class TelaAdminHospitais extends StatelessWidget {
  const TelaAdminHospitais({super.key});

  @override
  Widget build(BuildContext context) {
    return const AdminScreen();
  }
}
