import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import '../models/fila.dart';
import '../utils/date_utils.dart';

class ConfirmacaoSaidaController extends GetxController {
  final RxBool demoraNoAtendimento = false.obs;
  final RxBool imprevistos = false.obs;
  final RxBool atendimentoOutroLocal = false.obs;
  final RxBool filaGrande = false.obs;
  final RxBool semPrevisaoAtendimento = false.obs;
  final RxBool prefiroNaoResponder = false.obs;
  final RxString outroMotivo = ''.obs;
  final RxBool isSaving = false.obs;
  final RxBool isPesquisaAtiva = false.obs;
  final RxList<String> perguntasPersonalizadas = <String>[].obs;

  final String filaId;
  final String nomeMedico;
  final String especialidade;
  int? posicao;

  ConfirmacaoSaidaController({
    required this.filaId,
    required this.nomeMedico,
    required this.especialidade,
    this.posicao,
  });

  @override
  void onInit() {
    super.onInit();
    verificarPesquisaAtiva();
  }

  Future<void> verificarPesquisaAtiva() async {
    try {
      debugPrint(
          "[ConfirmacaoSaida] Verificando se a pesquisa está ativa para o médico");

      // Buscar o objeto Fila para obter o médico
      final fila = await ParseObject('Fila').getObject(filaId);

      if (fila.results == null || fila.results!.isEmpty) {
        debugPrint("[ConfirmacaoSaida] Fila não encontrada");
        return;
      }

      final filaObj = fila.results!.first as ParseObject;
      final medicoObj = filaObj.get<ParseObject>('medico');

      if (medicoObj == null) {
        debugPrint("[ConfirmacaoSaida] Médico não encontrado na fila");
        return;
      }

      // Carregar os dados completos do médico
      final medicoResponse =
          await ParseObject('Medico').getObject(medicoObj.objectId!);
      if (medicoResponse.results == null || medicoResponse.results!.isEmpty) {
        debugPrint("[ConfirmacaoSaida] Detalhes do médico não encontrados");
        return;
      }

      final medicoDetalhes = medicoResponse.results!.first as ParseObject;

      // Verificar se a pesquisa está ativa
      final pesquisaAtiva =
          medicoDetalhes.get<bool>('pesquisaSatisfacaoAtiva') ?? false;
      debugPrint(
          "[ConfirmacaoSaida] Pesquisa de satisfação ativa: $pesquisaAtiva");

      // Obter as perguntas personalizadas
      final perguntas =
          medicoDetalhes.get<List<dynamic>>('perguntasPersonalizadas');
      final listPerguntas = perguntas != null
          ? perguntas.map((p) => p.toString()).toList()
          : <String>[];

      debugPrint(
          "[ConfirmacaoSaida] Perguntas personalizadas: ${listPerguntas.length}");

      isPesquisaAtiva.value = pesquisaAtiva;
      perguntasPersonalizadas.value = listPerguntas;
    } catch (e) {
      debugPrint("[ConfirmacaoSaida] Erro ao verificar pesquisa: $e");
      // Se houver erro, assumimos que a pesquisa está ativa como padrão
      isPesquisaAtiva.value = true;
    }
  }

  bool get isAnyOptionSelected =>
      demoraNoAtendimento.value ||
      imprevistos.value ||
      atendimentoOutroLocal.value ||
      filaGrande.value ||
      semPrevisaoAtendimento.value ||
      prefiroNaoResponder.value ||
      outroMotivo.value.trim().isNotEmpty;

  void toggleOption(String option) {
    if (option != 'prefiroNaoResponder' && prefiroNaoResponder.value) {
      prefiroNaoResponder.value = false;
    }

    switch (option) {
      case 'demoraNoAtendimento':
        demoraNoAtendimento.value = !demoraNoAtendimento.value;
        break;
      case 'imprevistos':
        imprevistos.value = !imprevistos.value;
        break;
      case 'atendimentoOutroLocal':
        atendimentoOutroLocal.value = !atendimentoOutroLocal.value;
        break;
      case 'filaGrande':
        filaGrande.value = !filaGrande.value;
        break;
      case 'semPrevisaoAtendimento':
        semPrevisaoAtendimento.value = !semPrevisaoAtendimento.value;
        break;
      case 'prefiroNaoResponder':
        prefiroNaoResponder.value = !prefiroNaoResponder.value;
        if (prefiroNaoResponder.value) {
          _resetOtherOptions();
        }
        break;
    }
  }

  void _resetOtherOptions() {
    demoraNoAtendimento.value = false;
    imprevistos.value = false;
    atendimentoOutroLocal.value = false;
    filaGrande.value = false;
    semPrevisaoAtendimento.value = false;
    outroMotivo.value = '';
  }

  Future<void> salvarFeedbackESairDaFila() async {
    // Se a pesquisa não estiver ativa, podemos pular a validação
    if (isPesquisaAtiva.value && !isAnyOptionSelected) {
      Get.dialog(
        AlertDialog(
          backgroundColor: Colors.white,
          title: const Text(
            'Atenção',
            style: TextStyle(
              fontFamily: 'Georgia',
              fontWeight: FontWeight.bold,
            ),
          ),
          content: const Text(
            'Por favor, selecione pelo menos uma opção antes de confirmar.',
            style: TextStyle(
              fontFamily: 'Georgia',
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text(
                'OK',
                style: TextStyle(
                  color: Colors.black,
                  fontFamily: 'Georgia',
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      );
      return;
    }

    isSaving.value = true;

    try {
      debugPrint("[ConfirmacaoSaida] Iniciando processo de saída da fila");
      debugPrint("[ConfirmacaoSaida] ID da fila: $filaId");

      // Buscar o objeto Fila diretamente
      final fila = await ParseObject('Fila').getObject(filaId);

      if (fila.results == null || fila.results!.isEmpty) {
        throw Exception('Fila não encontrada');
      }

      final filaObj = fila.results!.first as ParseObject;
      final idPaciente = filaObj.get<String>('idPaciente') ?? '';

      if (idPaciente.isEmpty) {
        throw Exception('ID do paciente não encontrado na fila');
      }

      debugPrint("[ConfirmacaoSaida] ID Paciente: $idPaciente");

      // Primeiro, salvar o feedback se a pesquisa está ativa
      if (isPesquisaAtiva.value) {
        await _salvarFeedback(filaId);
      }

      // Segundo, sair da fila usando o modelo Fila corrigido
      final sucessoSaidaFila = await Fila.leaveFila(
        filaId: filaId,
        idPaciente: idPaciente,
      );

      if (!sucessoSaidaFila) {
        throw Exception('Falha ao sair da fila');
      }

      // Terceiro, criar notificação para o médico
      await _criarNotificacaoSaida(filaObj);

      _mostrarDialogoSucesso();
    } catch (e) {
      debugPrint("[ConfirmacaoSaida] ERRO CRÍTICO: $e");
      Get.snackbar(
        'Erro',
        'Erro ao processar saída: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 5),
      );
    } finally {
      isSaving.value = false;
    }
  }

  Future<void> _salvarFeedback(String filaId) async {
    try {
      debugPrint("[ConfirmacaoSaida] Salvando feedback");
      final feedback = ParseObject('FilaFeedback');

      // Criar relação com objeto Fila
      final filaPointer = ParseObject('Fila')..objectId = filaId;
      feedback.set('filaId', filaPointer);

      // Definir campos de feedback
      feedback.set('demoraAtendimento', demoraNoAtendimento.value);
      feedback.set('imprevistos', imprevistos.value);
      feedback.set('atendimentoOutroLocal', atendimentoOutroLocal.value);
      feedback.set('filaGrande', filaGrande.value);
      feedback.set('semPrevisaoAtendimento', semPrevisaoAtendimento.value);
      feedback.set('semResposta', prefiroNaoResponder.value);
      feedback.set('outroMotivo', outroMotivo.value.trim());
      feedback.set(
          'dataFeedback', BrazilTimeZone.createForParse(BrazilTimeZone.now()));

      // Adicionar perguntas personalizadas, se existirem
      if (perguntasPersonalizadas.isNotEmpty) {
        feedback.set('perguntasPersonalizadas', perguntasPersonalizadas);
      }

      // Configurar ACL
      final acl = ParseACL();
      acl.setPublicReadAccess(allowed: true);
      acl.setPublicWriteAccess(allowed: true);
      feedback.setACL(acl);

      final response = await feedback.save();

      if (!response.success) {
        throw Exception('Erro ao salvar feedback: ${response.error?.message}');
      }

      debugPrint(
          "[ConfirmacaoSaida] Feedback salvo com sucesso: ${response.results?.first.objectId}");
    } catch (e) {
      debugPrint("[ConfirmacaoSaida] Erro ao salvar feedback: $e");
      // Continuamos mesmo com erro para permitir a saída da fila
    }
  }

  Future<void> _criarNotificacaoSaida(ParseObject filaObj) async {
    try {
      debugPrint("[ConfirmacaoSaida] Criando notificação de saída");

      // Obter referências do médico e consultório
      final medicoObj = filaObj.get<ParseObject>('medico');
      final consultorioObj = filaObj.get<ParseObject>('consultorio');

      if (medicoObj == null || consultorioObj == null) {
        debugPrint(
            "[ConfirmacaoSaida] Médico ou consultório não encontrado na fila");
        return;
      }

      final notificacao = ParseObject('Notificacao')
        ..set('tipo', 'saida_fila')
        ..set('medico_id', medicoObj.objectId)
        ..set('consultorio_id', consultorioObj.objectId)
        ..set('fila_id', filaObj.objectId)
        ..set('lida', false)
        ..set(
            'created_at', BrazilTimeZone.createForParse(BrazilTimeZone.now()));

      // Configurar ACL
      final acl = ParseACL();
      acl.setPublicReadAccess(allowed: true);
      acl.setPublicWriteAccess(allowed: true);
      notificacao.setACL(acl);

      await notificacao.save();
      debugPrint("[ConfirmacaoSaida] Notificação criada com sucesso");
    } catch (e) {
      // Apenas log, não interrompe o fluxo
      debugPrint("[ConfirmacaoSaida] Erro ao criar notificação: $e");
    }
  }

  void _mostrarDialogoSucesso() {
    Get.dialog(
      AlertDialog(
        backgroundColor: Colors.white,
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              'assets/confirmar.png',
              width: 100,
              height: 100,
            ),
            const SizedBox(height: 16),
            const Text(
              'Você saiu da fila de espera com sucesso!',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontFamily: 'Georgia',
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF34ECCB),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              onPressed: () {
                // Usar Get.until em vez de Get.offAllNamed para preservar a sessão
                // Isso volta até a tela inicial preservando a autenticação
                Get.until((route) =>
                    route.settings.name == '/home' ||
                    route.settings.name == '/');
              },
              child: const Text(
                'OK',
                style: TextStyle(
                  color: Colors.black,
                  fontFamily: 'Georgia',
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
      barrierDismissible: false,
    );
  }
}
