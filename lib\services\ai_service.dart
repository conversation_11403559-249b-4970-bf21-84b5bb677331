import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;

/// 🤖 SERVIÇO DE INTELIGÊNCIA ARTIFICIAL
/// Integração com OpenAI ChatGPT para respostas inteligentes do chatbot
class AIService {
  static const String _tag = '[AI_SERVICE]';
  static const String _baseUrl = 'https://api.openai.com/v1/chat/completions';

  // Configurações da API
  static const String _model = 'gpt-3.5-turbo';
  static const int _maxTokens = 500;
  static const double _temperature = 0.7;

  // Timeout para requisições
  static const Duration _timeout = Duration(seconds: 30);

  /// Chave da API obtida do arquivo .env
  String? get _apiKey => dotenv.env['OPENAI_API_KEY'];

  /// Verificar se o serviço está configurado corretamente
  bool get isConfigured => _apiKey != null && _apiKey!.isNotEmpty;

  /// 💬 OBTER RESPOSTA DA IA
  ///
  /// [userMessage] - Mensagem do usuário
  /// [messageHistory] - Histórico de mensagens para contexto
  ///
  /// Retorna a resposta da IA ou uma resposta de fallback
  Future<String> getCompletion(
      String userMessage, List<Map<String, String>> messageHistory) async {
    if (!isConfigured) {
      debugPrint('$_tag ⚠️ Chave da API OpenAI não configurada');
      return _getFallbackResponse(userMessage);
    }

    try {
      debugPrint('$_tag 🚀 Enviando mensagem para OpenAI...');

      // Preparar o payload
      final payload = _buildPayload(userMessage, messageHistory);

      // Fazer a requisição
      final response = await http
          .post(
            Uri.parse(_baseUrl),
            headers: _buildHeaders(),
            body: jsonEncode(payload),
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        final aiResponse = _extractResponse(responseData);

        debugPrint('$_tag ✅ Resposta recebida da OpenAI');
        return aiResponse;
      } else {
        debugPrint('$_tag ❌ Erro na API OpenAI: ${response.statusCode}');
        debugPrint('$_tag Response: ${response.body}');
        return _getFallbackResponse(userMessage);
      }
    } on SocketException catch (e) {
      debugPrint('$_tag ❌ Erro de conexão: $e');
      return 'Parece que você está sem conexão com a internet. Verifique sua conexão e tente novamente.';
    } on HttpException catch (e) {
      debugPrint('$_tag ❌ Erro HTTP: $e');
      return _getFallbackResponse(userMessage);
    } catch (e) {
      debugPrint('$_tag ❌ Erro inesperado: $e');
      return _getFallbackResponse(userMessage);
    }
  }

  /// 📝 CONSTRUIR PAYLOAD PARA A API
  Map<String, dynamic> _buildPayload(
      String userMessage, List<Map<String, String>> messageHistory) {
    // Mensagem do sistema para definir o comportamento da IA
    final systemMessage = {"role": "system", "content": _getSystemPrompt()};

    // Preparar mensagens (sistema + histórico + nova mensagem)
    final messages = <Map<String, String>>[systemMessage];

    // Adicionar histórico limitado (últimas 10 mensagens para economizar tokens)
    final limitedHistory = messageHistory.length > 10
        ? messageHistory.skip(messageHistory.length - 10).toList()
        : messageHistory;

    messages.addAll(limitedHistory);

    // Adicionar nova mensagem do usuário
    messages.add({"role": "user", "content": userMessage});

    return {
      "model": _model,
      "messages": messages,
      "max_tokens": _maxTokens,
      "temperature": _temperature,
      "top_p": 1,
      "frequency_penalty": 0,
      "presence_penalty": 0,
    };
  }

  /// 🔑 CONSTRUIR HEADERS DA REQUISIÇÃO
  Map<String, String> _buildHeaders() {
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $_apiKey',
    };
  }

  /// 📤 EXTRAIR RESPOSTA DA IA
  String _extractResponse(Map<String, dynamic> responseData) {
    try {
      final choices = responseData['choices'] as List;
      if (choices.isNotEmpty) {
        final message = choices[0]['message'] as Map<String, dynamic>;
        final content = message['content'] as String;
        return content.trim();
      }

      return 'Desculpe, não consegui gerar uma resposta adequada.';
    } catch (e) {
      debugPrint('$_tag ❌ Erro ao extrair resposta: $e');
      return 'Erro ao processar a resposta da IA.';
    }
  }

  /// 🎯 PROMPT DO SISTEMA PARA DEFINIR COMPORTAMENTO
  String _getSystemPrompt() {
    return '''
Você é um assistente virtual especializado no aplicativo "Saúde Sem Espera", uma plataforma de gestão de filas médicas.

SUAS FUNÇÕES:
- Ajudar usuários com dúvidas sobre o aplicativo
- Explicar como usar as funcionalidades
- Auxiliar com problemas técnicos básicos
- Fornecer suporte geral sobre saúde digital

SOBRE O APLICATIVO:
- Permite entrar em filas médicas virtualmente
- Usuários escaneiam QR codes para entrar nas filas
- Acompanhamento em tempo real da posição na fila
- Notificações sobre andamento do atendimento
- Disponível para pacientes, médicos e secretárias

REGRAS IMPORTANTES:
1. NUNCA dê conselhos médicos específicos
2. Para problemas médicos, sempre recomende consultar um profissional
3. Para problemas técnicos complexos, use: "ENCAMINHAR_PARA_SUPORTE [motivo]"
4. Para dúvidas médicas, use: "ENCAMINHAR_PARA_SUPORTE Dúvida médica"
5. Seja sempre educado, claro e útil
6. Responda em português brasileiro
7. Mantenha respostas concisas (máximo 3 parágrafos)

Se não souber responder algo específico, seja honesto e ofereça alternativas de ajuda.
''';
  }

  /// 🔄 RESPOSTA DE FALLBACK
  String _getFallbackResponse(String userMessage) {
    final lowerMessage = userMessage.toLowerCase();

    // Verificar se é uma solicitação de suporte humano
    if (_containsAny(lowerMessage, [
      'falar com humano',
      'atendente',
      'pessoa real',
      'suporte humano',
      'agente',
      'chat humano'
    ])) {
      return "ENCAMINHAR_PARA_SUPORTE Solicitação direta de atendimento humano";
    }

    // Verificar se é uma questão médica
    if (_containsAny(lowerMessage, [
      'médico',
      'doença',
      'sintoma',
      'tratamento',
      'remédio',
      'medicamento',
      'dor',
      'febre',
      'tosse'
    ])) {
      return "ENCAMINHAR_PARA_SUPORTE Dúvida médica que requer atendimento especializado";
    }

    // Verificar se é um problema técnico
    if (_containsAny(lowerMessage,
        ['problema', 'não funciona', 'erro', 'bug', 'travando', 'falha'])) {
      return "ENCAMINHAR_PARA_SUPORTE Problema técnico reportado pelo usuário";
    }

    // Respostas básicas
    if (_containsAny(lowerMessage, ['olá', 'oi', 'bom dia', 'boa tarde'])) {
      return 'Olá! Sou o assistente virtual do Saúde Sem Espera. Como posso ajudar você hoje?';
    }

    if (_containsAny(lowerMessage, ['como', 'funciona', 'usar'])) {
      return 'O Saúde Sem Espera permite que você entre na fila médica virtualmente:\n\n1. Escaneie o QR Code do médico/consultório\n2. Aguarde a confirmação\n3. Acompanhe sua posição em tempo real\n4. Receba notificações sobre o andamento\n\nQuer que eu explique alguma funcionalidade específica?';
    }

    // Resposta padrão
    return 'Desculpe, estou com dificuldades para processar sua solicitação no momento. Posso tentar ajudar de outra forma ou você pode entrar em contato com nosso suporte humano.';
  }

  /// 🔍 VERIFICAR SE CONTÉM PALAVRAS-CHAVE
  bool _containsAny(String text, List<String> keywords) {
    return keywords.any((keyword) => text.contains(keyword));
  }
}
