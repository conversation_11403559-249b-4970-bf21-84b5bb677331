import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fila_app/views/gradient_background.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:flutter/services.dart';
import 'package:phone_form_field/phone_form_field.dart';
import '../controllers/paciente_controller.dart';
import 'package:fila_app/widgets/app_header.dart';

class InserirPacienteFila extends StatefulWidget {
  final String nomeMedico;
  final String especialidade;
  final String id;
  final String solicitacaoId;

  const InserirPacienteFila({
    super.key,
    required this.nomeMedico,
    required this.especialidade,
    required this.id,
    required this.solicitacaoId,
    required String idMedico,
  });

  @override
  State<InserirPacienteFila> createState() => _InserirPacienteFilaState();
}

class _InserirPacienteFilaState extends State<InserirPacienteFila> {
  final _nomeController = TextEditingController();
  final _telefoneController = TextEditingController();
  late PhoneController _phoneController;
  final PacienteController _pacienteController = Get.find<PacienteController>();

  bool _isLoading = false;
  String? _errorMessage;
  bool _isInitialized = false;
  bool _isDeviceValidated = false;
  String _deviceId = '';

  @override
  void initState() {
    super.initState();
    _phoneController = PhoneController(
      initialValue: PhoneNumber.parse('+55'),
    );
    _initializeDevice();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        _isInitialized = true;
      });
    });
  }

  /// Carrega dados existentes do usuário
  Future<void> _initializeDevice() async {
    try {
      setState(() => _isLoading = true);

      // Verificar dados do usuário através do controller
      await _pacienteController.userDataController.checkUserData();

      // Carregar dados locais
      await _loadLocalUserData();
      _isDeviceValidated = true;
    } catch (e) {
      debugPrint('Erro ao inicializar: $e');
      // Fallback para verificação local
      await _loadLocalUserData();
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// Carrega dados locais como fallback
  Future<void> _loadLocalUserData() async {
    try {
      final userData =
          await _pacienteController.userDataController.getUserData();
      if (userData != null) {
        setState(() {
          _nomeController.text = userData['nome'] ?? '';
          if (userData['telefone'] != null && userData['telefone'].isNotEmpty) {
            _telefoneController.text = userData['telefone'];
            try {
              _phoneController.value = PhoneNumber.parse(userData['telefone']);
            } catch (e) {
              debugPrint('Erro ao parsear telefone local: $e');
            }
          }
        });
      }
    } catch (e) {
      debugPrint('Erro ao carregar dados locais do usuário: $e');
    }
  }

  @override
  void dispose() {
    _nomeController.dispose();
    _telefoneController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  Future<void> _confirmarInsercao() async {
    if (_nomeController.text.isEmpty || _telefoneController.text.isEmpty) {
      setState(() {
        _errorMessage = 'Por favor, preencha todos os campos';
      });
      return;
    }

    setState(() => _isLoading = true);

    try {
      String userId;

      // Obter userId dos dados locais ou criar novo
      final userData =
          await _pacienteController.userDataController.getUserData();
      if (userData != null && userData.containsKey('userId')) {
        userId = userData['userId']!;
      } else {
        // Criar novo paciente - o saveUserData irá gerar um userId
        await _pacienteController.userDataController.saveUserData(
          _nomeController.text,
          _telefoneController.text,
        );
        final newUserData =
            await _pacienteController.userDataController.getUserData();
        if (newUserData != null && newUserData.containsKey('userId')) {
          userId = newUserData['userId']!;
        } else {
          throw Exception('Não foi possível obter o ID do usuário');
        }
      }

      // Atualizar dados no UserDataController
      await _pacienteController.userDataController.saveUserData(
        _nomeController.text,
        _telefoneController.text,
        isEditing: _isDeviceValidated,
      );

      // Adicionar o paciente à fila usando o userId
      await _adicionarPacienteNaFila(userId);
    } catch (e) {
      debugPrint('Erro ao confirmar inserção: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao adicionar à fila: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  // Adiciona o paciente à fila usando o Cloud Code
  Future<void> _adicionarPacienteNaFila(String userId) async {
    try {
      debugPrint('Adicionando paciente à fila com userId: $userId');

      // Usar a função Cloud Code para adicionar paciente na fila
      final response =
          await ParseCloudFunction('adicionarPacienteNaFila').execute(
        parameters: {
          'solicitacaoId': widget.solicitacaoId,
          'idPaciente': userId,
          'nome': _nomeController.text,
          'telefone': _telefoneController.text,
        },
      );

      if (response.success) {
        debugPrint('Paciente adicionado à fila com sucesso');

        if (mounted) {
          // Mostrar mensagem de sucesso
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Você foi adicionado à fila com sucesso!'),
              backgroundColor: Colors.green,
            ),
          );

          // Voltar para a tela anterior após um delay
          await Future.delayed(const Duration(seconds: 1));
          if (mounted) {
            Navigator.of(context).pop();
          }
        }
      } else {
        throw Exception(
            response.error?.message ?? 'Erro desconhecido ao adicionar à fila');
      }
    } catch (e) {
      debugPrint('Erro ao adicionar paciente na fila: $e');
      throw Exception('Erro ao adicionar paciente na fila: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppHeader(
        title: 'Adicionar à Fila',
        centerTitle: true,
        onBackPressed: () => Navigator.of(context).pop(),
      ),
      body: GradientBackground(
        child: SafeArea(
          bottom: false,
          child: SingleChildScrollView(
            physics: const ClampingScrollPhysics(),
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const SizedBox(height: 20),
                _buildMedicoCard(),
                const SizedBox(height: 20),
                _buildPacienteIdCard(),
                const SizedBox(height: 30),
                if (_errorMessage != null) _buildErrorMessage(),
                const SizedBox(height: 20),
                _buildTextField(
                  label: 'Nome do Paciente',
                  controller: _nomeController,
                  hint: 'Digite o nome completo',
                ),
                const SizedBox(height: 20),
                _buildPhoneField(),
                const SizedBox(height: 40),
                _buildButtons(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMedicoCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            'Dr. ${widget.nomeMedico}',
            style: const TextStyle(
              fontFamily: 'Georgia',
              fontSize: 22,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            widget.especialidade,
            style: const TextStyle(
              fontFamily: 'Georgia',
              fontSize: 18,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPacienteIdCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.person, color: Colors.teal),
          const SizedBox(width: 8),
          Text(
            'ID do Paciente: ${widget.id}',
            style: const TextStyle(
              fontFamily: 'Georgia',
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorMessage() {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: Colors.red),
      ),
      child: Text(
        _errorMessage!,
        style: const TextStyle(
          color: Colors.red,
          fontFamily: 'Georgia',
        ),
      ),
    );
  }

  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
    required String hint,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: TextField(
        controller: controller,
        keyboardType: keyboardType,
        inputFormatters: inputFormatters,
        style: const TextStyle(
          fontFamily: 'Georgia',
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        decoration: InputDecoration(
          labelText: label,
          labelStyle: const TextStyle(
            fontFamily: 'Georgia',
            fontSize: 15,
            color: Colors.teal,
            fontWeight: FontWeight.w500,
          ),
          hintText: hint,
          hintStyle: TextStyle(
            color: Colors.grey[400],
            fontFamily: 'Georgia',
            fontSize: 15,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.teal.shade200, width: 1),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.teal.shade200, width: 1),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Colors.teal, width: 2),
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.all(16),
          prefixIcon: label.contains('Nome')
              ? const Icon(Icons.person_outline, color: Colors.teal)
              : null,
        ),
      ),
    );
  }

  Widget _buildPhoneField() {
    if (!_isInitialized) {
      return Container(
        margin: const EdgeInsets.symmetric(vertical: 10),
        height: 58,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 6,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: const Center(
          child: SizedBox(
            width: 24,
            height: 24,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.teal),
            ),
          ),
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
        border: Border.all(color: Colors.teal.shade200),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: PhoneFormField(
          controller: _phoneController,
          decoration: InputDecoration(
            labelText: 'Telefone/WhatsApp',
            filled: true,
            fillColor: Colors.white,
            border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
            prefixIcon: const Icon(Icons.phone_outlined, color: Colors.teal),
          ),
          onChanged: (phoneNumber) {
            if (phoneNumber != null) {
              _telefoneController.text = phoneNumber.international;
            }
          },
          validator: (phoneNumber) {
            if (phoneNumber == null || !phoneNumber.isValid()) {
              return 'Por favor, digite um telefone válido';
            }
            return null;
          },
          countrySelectorNavigator: const CountrySelectorNavigator.dialog(),
          isCountryButtonPersistent: true,
        ),
      ),
    );
  }

  Widget _buildButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : () => Get.back(result: false),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            child: const Text(
              'Cancelar',
              style: TextStyle(
                fontFamily: 'Georgia',
                fontSize: 16,
                color: Colors.white,
              ),
            ),
          ),
        ),
        const SizedBox(width: 20),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _confirmarInsercao,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text(
                    'Confirmar',
                    style: TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 16,
                      color: Colors.white,
                    ),
                  ),
          ),
        ),
      ],
    );
  }
}
