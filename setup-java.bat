@echo off
echo 🚀 Configurando Java para Flutter/Android...
echo.

:: Verificar se está executando como administrador
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ Este script deve ser executado como Administrador!
    echo 💡 Clique com botão direito e selecione "Executar como administrador"
    pause
    exit /b 1
)

:: Possíveis caminhos do Java
set "JAVA_PATH_1=C:\Program Files\Java\jdk-24"
set "JAVA_PATH_2=C:\Program Files\Java\jdk-21"
set "JAVA_PATH_3=C:\Program Files\Eclipse Adoptium\jdk-21.0.5-hotspot"
set "JAVA_PATH_4=C:\Program Files\Eclipse Adoptium\jdk-17.0.12-hotspot"

set "JAVA_HOME_PATH="

:: Procurar Java instalado
if exist "%JAVA_PATH_1%" (
    set "JAVA_HOME_PATH=%JAVA_PATH_1%"
    echo ✅ Java 24 encontrado
) else if exist "%JAVA_PATH_2%" (
    set "JAVA_HOME_PATH=%JAVA_PATH_2%"
    echo ✅ Java 21 encontrado
) else if exist "%JAVA_PATH_3%" (
    set "JAVA_HOME_PATH=%JAVA_PATH_3%"
    echo ✅ OpenJDK 21 encontrado
) else if exist "%JAVA_PATH_4%" (
    set "JAVA_HOME_PATH=%JAVA_PATH_4%"
    echo ✅ OpenJDK 17 encontrado
) else (
    echo ❌ Java não encontrado nos caminhos padrão!
    echo 📥 Instale o Java primeiro:
    echo    • OpenJDK: https://adoptium.net/
    echo    • Oracle JDK: https://www.oracle.com/java/technologies/downloads/
    pause
    exit /b 1
)

echo 📍 Usando: %JAVA_HOME_PATH%
echo.

:: Configurar JAVA_HOME
echo 🔧 Configurando JAVA_HOME...
setx JAVA_HOME "%JAVA_HOME_PATH%" /M
if %errorLevel% equ 0 (
    echo ✅ JAVA_HOME configurado: %JAVA_HOME_PATH%
) else (
    echo ❌ Erro ao configurar JAVA_HOME
    pause
    exit /b 1
)

:: Configurar PATH
echo 🔧 Configurando PATH...
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set "CURRENT_PATH=%%b"

:: Remover entradas antigas do Java
set "CLEAN_PATH=%CURRENT_PATH%"
set "CLEAN_PATH=%CLEAN_PATH:;C:\Program Files\Java\jdk-21\bin=%"
set "CLEAN_PATH=%CLEAN_PATH:;C:\Program Files\Java\jdk-24\bin=%"
set "CLEAN_PATH=%CLEAN_PATH:;%JAVA_HOME%\bin=%"

:: Adicionar nova entrada
set "NEW_PATH=%%JAVA_HOME%%\bin;%CLEAN_PATH%"

:: Aplicar novo PATH
setx PATH "%NEW_PATH%" /M
if %errorLevel% equ 0 (
    echo ✅ PATH atualizado
) else (
    echo ❌ Erro ao atualizar PATH
    pause
    exit /b 1
)

:: Verificar configuração
echo.
echo 🧪 Verificando configuração...
echo.

:: Testar Java
"%JAVA_HOME_PATH%\bin\java.exe" -version
if %errorLevel% equ 0 (
    echo ✅ Java funcionando corretamente
) else (
    echo ❌ Erro ao executar Java
)

echo.
"%JAVA_HOME_PATH%\bin\javac.exe" -version
if %errorLevel% equ 0 (
    echo ✅ Javac funcionando corretamente
) else (
    echo ❌ Erro ao executar Javac
)

:: Criar configuração para Gradle
echo.
echo 📝 Criando configuração para Gradle...
set "GRADLE_CONFIG_FILE=gradle-java-config.txt"

echo # Configuração do Java para android/gradle.properties > "%GRADLE_CONFIG_FILE%"
echo # Copie estas linhas para o arquivo android/gradle.properties do seu projeto >> "%GRADLE_CONFIG_FILE%"
echo. >> "%GRADLE_CONFIG_FILE%"
echo # Caminho do Java (use barras duplas no Windows) >> "%GRADLE_CONFIG_FILE%"
echo org.gradle.java.home=%JAVA_HOME_PATH:\=\\% >> "%GRADLE_CONFIG_FILE%"
echo. >> "%GRADLE_CONFIG_FILE%"
echo # Configurações JVM otimizadas >> "%GRADLE_CONFIG_FILE%"
echo org.gradle.jvmargs=-Xmx4g -XX:MaxMetaspaceSize=1g -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -XX:+UseG1GC -Xlint:-options >> "%GRADLE_CONFIG_FILE%"
echo. >> "%GRADLE_CONFIG_FILE%"
echo # Configurações de performance >> "%GRADLE_CONFIG_FILE%"
echo org.gradle.daemon=true >> "%GRADLE_CONFIG_FILE%"
echo org.gradle.parallel=true >> "%GRADLE_CONFIG_FILE%"
echo org.gradle.caching=true >> "%GRADLE_CONFIG_FILE%"

echo ✅ Configuração salva em: %GRADLE_CONFIG_FILE%

echo.
echo 🎉 Configuração concluída com sucesso!
echo.
echo 📋 Próximos passos:
echo    1. Feche todos os terminais/IDEs abertos
echo    2. Abra um novo PowerShell/CMD
echo    3. Execute: java -version
echo    4. Execute: echo %%JAVA_HOME%%
echo    5. Copie o conteúdo de '%GRADLE_CONFIG_FILE%' para android/gradle.properties
echo.
echo 🔄 Reinicie o computador se houver problemas
echo.
pause
