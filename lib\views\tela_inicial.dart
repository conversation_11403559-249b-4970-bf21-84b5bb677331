import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fila_app/views/gradient_background.dart';

class TelaInicial extends StatefulWidget {
  const TelaInicial({super.key});

  @override
  State<TelaInicial> createState() => _TelaInicialState();
}

class _TelaInicialState extends State<TelaInicial>
    with TickerProviderStateMixin {
  late AnimationController _titleController;
  late AnimationController _adminController;
  late AnimationController _patientController;
  late AnimationController _helpController;
  late Animation<double> _titleFade;
  late Animation<Offset> _titleSlide;
  late Animation<double> _adminScale;
  late Animation<double> _adminFade;
  late Animation<double> _patientScale;
  late Animation<double> _patientFade;
  late Animation<double> _helpFade;
  late Animation<Offset> _helpSlide;

  @override
  void initState() {
    super.initState();

    // Controladores para animações sequenciais
    _titleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _adminController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _patientController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _helpController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    // Animações do título
    _titleFade = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _titleController, curve: Curves.easeIn),
    );

    _titleSlide = Tween<Offset>(
      begin: const Offset(0, -0.5),
      end: Offset.zero,
    ).animate(
        CurvedAnimation(parent: _titleController, curve: Curves.elasticOut));

    // Animações do botão admin
    _adminScale = Tween<double>(begin: 0.7, end: 1.0).animate(
      CurvedAnimation(parent: _adminController, curve: Curves.elasticOut),
    );

    _adminFade = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _adminController, curve: Curves.easeIn),
    );

    // Animações do botão paciente
    _patientScale = Tween<double>(begin: 0.7, end: 1.0).animate(
      CurvedAnimation(parent: _patientController, curve: Curves.elasticOut),
    );

    _patientFade = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _patientController, curve: Curves.easeIn),
    );

    // Animações do botão de ajuda
    _helpFade = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _helpController, curve: Curves.easeIn),
    );

    _helpSlide = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(
        CurvedAnimation(parent: _helpController, curve: Curves.elasticOut));

    // Executar animações em sequência
    _startSequentialAnimations();
  }

  void _startSequentialAnimations() {
    // 1. Título aparece primeiro
    _titleController.forward();

    // 2. Botão admin depois de 400ms
    Future.delayed(const Duration(milliseconds: 400), () {
      if (mounted) _adminController.forward();
    });

    // 3. Botão paciente depois de mais 300ms
    Future.delayed(const Duration(milliseconds: 700), () {
      if (mounted) _patientController.forward();
    });

    // 4. Botão ajuda depois de mais 300ms
    Future.delayed(const Duration(milliseconds: 1000), () {
      if (mounted) _helpController.forward();
    });
  }

  @override
  void dispose() {
    _titleController.dispose();
    _adminController.dispose();
    _patientController.dispose();
    _helpController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientBackground(
        child: SafeArea(
          child: LayoutBuilder(
            builder: (context, constraints) {
              return SingleChildScrollView(
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight: constraints.maxHeight,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Título de boas-vindas animado
                        SlideTransition(
                          position: _titleSlide,
                          child: FadeTransition(
                            opacity: _titleFade,
                            child: _buildWelcomeText(),
                          ),
                        ),

                        // Botões de acesso com animações sequenciais
                        Column(
                          children: [
                            // Botão Administrativo
                            AnimatedBuilder(
                              animation: _adminController,
                              builder: (context, child) {
                                return Transform.scale(
                                  scale: _adminScale.value,
                                  child: FadeTransition(
                                    opacity: _adminFade,
                                    child: _buildModernOption(
                                      'ACESSO ADMINISTRATIVO',
                                      'Médicos, secretárias e administradores',
                                      Icons.admin_panel_settings_rounded,
                                      Colors.teal,
                                      '/login',
                                    ),
                                  ),
                                );
                              },
                            ),

                            const SizedBox(height: 20),

                            // Botão Paciente
                            AnimatedBuilder(
                              animation: _patientController,
                              builder: (context, child) {
                                return Transform.scale(
                                  scale: _patientScale.value,
                                  child: FadeTransition(
                                    opacity: _patientFade,
                                    child: _buildModernOption(
                                      'ACESSO PACIENTE',
                                      'Entrar na fila através do QR Code',
                                      Icons.qr_code_scanner_rounded,
                                      Colors.blue,
                                      '/paciente',
                                    ),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),

                        // Botão de ajuda animado
                        SlideTransition(
                          position: _helpSlide,
                          child: FadeTransition(
                            opacity: _helpFade,
                            child: _buildHelpButton(),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeText() {
    return Column(
      children: [
        ShaderMask(
          shaderCallback: (bounds) => LinearGradient(
            colors: [Colors.teal.shade600, Colors.teal.shade800],
          ).createShader(bounds),
          child: const Text(
            'Bem-vindo',
            style: TextStyle(
              fontFamily: 'Georgia',
              fontSize: 42,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
        const SizedBox(height: 12),
        Text(
          'Escolha o tipo de acesso desejado',
          style: TextStyle(
            fontFamily: 'Georgia',
            fontSize: 18,
            color: Colors.grey.shade700,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildModernOption(
    String title,
    String subtitle,
    IconData icon,
    MaterialColor color,
    String route,
  ) {
    return GestureDetector(
      onTap: () => Get.toNamed(route),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(25),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.white,
              Colors.grey.shade50,
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 20,
              spreadRadius: 0,
              offset: const Offset(0, 10),
            ),
          ],
          border: Border.all(
            color: color.withOpacity(0.2),
            width: 2,
          ),
        ),
        child: Column(
          children: [
            // Ícone principal
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [color.shade400, color.shade600],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: color.withOpacity(0.3),
                    blurRadius: 15,
                    spreadRadius: 0,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Icon(
                icon,
                size: 40,
                color: Colors.white,
              ),
            ),

            const SizedBox(height: 20),

            // Título
            Text(
              title,
              style: TextStyle(
                fontFamily: 'Georgia',
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color.shade800,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 8),

            // Subtítulo
            Text(
              subtitle,
              style: TextStyle(
                fontFamily: 'Georgia',
                fontSize: 14,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 15),

            // Indicador de ação
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Toque para acessar',
                  style: TextStyle(
                    fontFamily: 'Georgia',
                    fontSize: 12,
                    color: color.shade600,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(width: 5),
                Icon(
                  Icons.arrow_forward_rounded,
                  size: 16,
                  color: color.shade600,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHelpButton() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(50),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 15,
            spreadRadius: 0,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => Get.toNamed('/ajuda'),
          borderRadius: BorderRadius.circular(50),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.orange.shade400, Colors.orange.shade600],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(50),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.help_outline_rounded,
                  color: Colors.white,
                  size: 24,
                ),
                const SizedBox(width: 10),
                const Text(
                  'Precisa de ajuda?',
                  style: TextStyle(
                    fontFamily: 'Georgia',
                    fontSize: 16,
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
