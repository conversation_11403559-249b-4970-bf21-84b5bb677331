// lib/views/tela_secretaria_filas.dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fila_app/controllers/secretaria_controller.dart';
import 'package:fila_app/views/gradient_background.dart';
import 'package:fila_app/widgets/app_header.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:fila_app/controllers/login_controller.dart';
import 'package:fila_app/widgets/floating_action_button_menu.dart';
import 'package:fila_app/models/fila.dart';
import 'dart:async';
import 'package:fila_app/controllers/emergencia_controller.dart';
import '../utils/date_utils.dart';

class TelaSecretariaFilas extends StatefulWidget {
  const TelaSecretariaFilas({super.key});

  @override
  State<TelaSecretariaFilas> createState() => _TelaSecretariaFilasState();
}

class _TelaSecretariaFilasState extends State<TelaSecretariaFilas>
    with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  final SecretariaController controller = Get.put(SecretariaController());
  final LoginController loginController = Get.find<LoginController>();

  // Estado local para a lista de filas e médico selecionado
  List<ParseObject> _filaAtual = [];
  String? _medicoSelecionadoId;
  String? _medicoSelecionadoNome;
  Map<String, dynamic> _metricas = {};
  bool _carregandoFila = false;
  bool _isLandscape = false;
  ParseObject? _hospitalAtual;

  late TabController _tabController;
  Timer? _timer;

  // Variável para armazenar a subscrição do LiveQuery para HospitalMedicoUpdate
  Subscription? _hospitalMedicoUpdateSubscription;
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _medicoSelecionadoId = null;
    _hospitalAtual = null;
    _filaAtual = [];
    _metricas = {
      'totalPacientes': 0,
      'pacientesAguardando': 0,
      'pacientesEmAtendimento': 0,
      'pacientesAtendidos': 0,
      'tempoMedioEspera': const Duration(minutes: 0),
      'tempoMedioAtendimento': const Duration(minutes: 0),
    };

    // Inicializar controlador de emergências
    _configurarListenerEmergencias();

    // Configurar listener para atualização de médicos
    _configurarListenerNovosMedicos();

    // Adicionar listener para mudanças de orientação
    WidgetsBinding.instance.addObserver(this);

    // Uso de addPostFrameCallback para garantir que a inicialização ocorra depois da construção da UI
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Inicializar dados apenas uma vez após a renderização inicial completa
      _inicializarDados();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _timer?.cancel();
    // Remover observer de orientação da tela
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    // Verificar orientação quando há mudanças nas métricas da tela
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _verificarOrientacao();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Verificar orientação inicial
    _verificarOrientacao();
  }

  Future<void> _inicializarDados() async {
    if (!mounted) return;
    setState(() => _carregandoFila = true);
    try {
      // Wait for controller to finish loading secretaria data and doctors
      if (controller.medicos.isEmpty) {
        await controller.carregarDadosSecretaria();
      }

      // If we have doctors but no selected doctor, select the first one
      if (_medicoSelecionadoId == null && controller.medicos.isNotEmpty) {
        final firstDoctor = controller.medicos.first;
        _medicoSelecionadoId = firstDoctor.objectId;
        _medicoSelecionadoNome = firstDoctor.get<String>('nome') ?? 'Médico';

        // Try to repair any queue issues before loading
        await controller.verificarERepararFilas(_medicoSelecionadoId!);

        // Then load the queue
        final fila = await controller.obterFilaPorMedico(_medicoSelecionadoId!);
        final metricas =
            await controller.obterMetricasMedico(_medicoSelecionadoId!);

        if (mounted) {
          setState(() {
            _filaAtual = fila;
            _metricas = metricas;
          });
        }
      }

      if (mounted) {
        setState(() => _carregandoFila = false);
      }
    } catch (e) {
      if (mounted) {
        _mostrarMensagem('Erro ao inicializar dados: $e', isError: true);
        setState(() => _carregandoFila = false);
      }
    }
  }

  Future<void> _selecionarMedico(ParseObject medico) async {
    if (!mounted) return;

    // Armazenar temporariamente os IDs para usar nas operações
    final medicoId = medico.objectId;
    final medicoNome = medico.get<String>('nome') ?? 'Médico';

    // Atualizar estado para mostrar carregamento
    setState(() {
      _carregandoFila = true;
    });

    try {
      // Primeiro verificar e reparar qualquer problema na fila
      await controller.verificarERepararFilas(medicoId!);

      // Depois corrigir inconsistências no status em_fila dos usuários
      await controller.corrigirStatusEmFilaUsuarios();

      // Atualizar as variáveis de estado somente depois que tudo estiver pronto
      if (mounted) {
        setState(() {
          _medicoSelecionadoId = medicoId;
          _medicoSelecionadoNome = medicoNome;
        });
      }

      // Obter fila e métricas do médico selecionado
      final fila = await controller.obterFilaPorMedico(_medicoSelecionadoId!);
      final metricas =
          await controller.obterMetricasMedico(_medicoSelecionadoId!);

      if (!mounted) return;

      setState(() {
        _filaAtual = fila;
        _metricas = metricas;
        _carregandoFila = false;
      });

      // Configurar o timer para atualizações automáticas quando um médico for selecionado
      _configurarTimerAtualizacao();

      // Log de informações para debug
      debugPrint(
          'Fila carregada para o médico $_medicoSelecionadoNome: ${_filaAtual.length} pacientes');
      for (var paciente in _filaAtual) {
        final nome = paciente.get<String>('nome') ?? 'Sem nome';
        final status = paciente.get<String>('status') ?? 'desconhecido';
        final posicao = paciente.get<int>('posicao') ?? 0;
        debugPrint(
            'Paciente: $nome, Status: $status, Posição: $posicao, ObjectId: ${paciente.objectId}');
      }
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _filaAtual = [];
        _metricas = {};
        _carregandoFila = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erro ao carregar fila: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _iniciarAtendimento(ParseObject paciente) async {
    if (!await _confirmarInicioAtendimento(paciente)) return;

    setState(() => _carregandoFila = true);

    try {
      // Iniciar o atendimento usando o controlador
      final success = await controller.iniciarAtendimento(paciente);

      if (mounted) {
        if (success) {
          _mostrarMensagem('Atendimento iniciado com sucesso');
          _verificarEAtualizarFila();
        } else {
          throw Exception('Não foi possível iniciar o atendimento');
        }
      }
    } catch (e) {
      if (mounted) {
        _mostrarMensagem('Erro ao iniciar atendimento: $e', isError: true);
      }
    } finally {
      if (mounted) {
        setState(() => _carregandoFila = false);
      }
    }
  }

  Future<void> _finalizarAtendimento(ParseObject paciente) async {
    if (!await _confirmarFinalizacaoAtendimento(paciente)) return;

    setState(() => _carregandoFila = true);

    try {
      // Guardar ID e dados do paciente antes de finalizar, para recuperação em caso de erro
      final String pacienteId = paciente.objectId!;
      final String pacienteNome = paciente.get<String>('nome') ?? 'Paciente';

      // Mudar status para atendido e salvar hora de fim
      final success = await controller.finalizarAtendimento(paciente);

      if (success) {
        // Verificar se o paciente ainda existe na fila após a finalização
        final QueryBuilder<ParseObject> query =
            QueryBuilder<ParseObject>(ParseObject('Fila'))
              ..whereEqualTo('objectId', pacienteId);

        final response = await query.query();

        // Se o paciente não existe mais, reportar erro e corrigir
        if (!response.success ||
            response.results == null ||
            response.results!.isEmpty) {
          debugPrint(
              "[ERRO] Paciente desapareceu após finalização: $pacienteNome (ID: $pacienteId)");
          throw Exception(
              'Paciente desapareceu após finalização. Recarregando fila...');
        }

        // Atualizar o objeto local para refletir a mudança
        paciente.set('status', 'atendido');
        paciente.set('data_fim_atendimento',
            BrazilTimeZone.createForParse(BrazilTimeZone.now()));

        // Recarregar a lista completa para garantir dados atualizados
        await _verificarEAtualizarFila();

        // Atualizar explicitamente as métricas para garantir que o card de atendidos seja atualizado
        await _atualizarContadorPacientesAtendidos();

        // Incrementar localmente o contador de atendidos para atualização imediata
        setState(() {
          _metricas['pacientesAtendidos'] =
              (_metricas['pacientesAtendidos'] ?? 0) + 1;
        });

        _mostrarMensagem('Atendimento finalizado com sucesso');
      } else {
        throw Exception('Não foi possível finalizar o atendimento');
      }
    } catch (e) {
      _mostrarMensagem('Erro ao finalizar atendimento: $e', isError: true);
      // Forçar atualização completa para corrigir qualquer inconsistência
      await _verificarEAtualizarFila();
    } finally {
      if (mounted) {
        setState(() => _carregandoFila = false);
      }
    }
  }

  Future<bool> _confirmarInicioAtendimento(ParseObject paciente) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'Iniciar Atendimento',
          style: TextStyle(
            fontFamily: 'Georgia',
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          'Iniciar atendimento para ${paciente.get<String>('nome')}?',
          style: const TextStyle(fontFamily: 'Georgia'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text(
              'Cancelar',
              style: TextStyle(color: Colors.grey, fontFamily: 'Georgia'),
            ),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
            ),
            onPressed: () => Navigator.pop(context, true),
            child: const Text(
              'Confirmar',
              style: TextStyle(fontFamily: 'Georgia'),
            ),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  Future<bool> _confirmarFinalizacaoAtendimento(ParseObject paciente) async {
    final nome = paciente.get<String>('nome') ?? 'paciente';

    if (!mounted) return false;

    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Finalizar Atendimento'),
            content: Text('Deseja finalizar o atendimento de $nome?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancelar'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Finalizar'),
              ),
            ],
          ),
        ) ??
        false;
  }

  Future<void> _removerPaciente(ParseObject paciente) async {
    final confirmou = await _confirmarRemocao(paciente);
    if (!confirmou || !mounted) return;

    setState(() => _carregandoFila = true);

    try {
      // Remover o paciente usando o controlador
      final success = await controller.removerPaciente(paciente);

      if (mounted) {
        if (success) {
          _mostrarMensagem('Paciente removido com sucesso');
          _verificarEAtualizarFila();
        } else {
          throw Exception('Não foi possível remover o paciente');
        }
      }
    } catch (e) {
      if (mounted) {
        _mostrarMensagem('Erro ao remover paciente: $e', isError: true);
      }
    } finally {
      if (mounted) {
        setState(() => _carregandoFila = false);
      }
    }
  }

  Future<bool> _confirmarRemocao(ParseObject paciente) async {
    if (!mounted) return false;

    final nome = paciente.get<String>('nome') ?? 'paciente';
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Remover Paciente'),
            content: Text('Deseja remover $nome da fila?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancelar'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Remover'),
              ),
            ],
          ),
        ) ??
        false;
  }

  void _mostrarMensagem(String mensagem, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          mensagem,
          style: const TextStyle(fontFamily: 'Georgia'),
        ),
        backgroundColor: isError ? Colors.red : Colors.teal,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  // Novo método para tratamento de navegação para trás
  void _handleBack() {
    // Cancel the timer to avoid callback after navigation
    _timer?.cancel();

    // Use um pequeno delay antes de navegar
    Future.delayed(Duration.zero, () {
      Get.offNamed('/home_secretaria');
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;

        final confirmarSaida = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Confirmar Saída'),
            content: const Text('Deseja realmente sair do aplicativo?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Não'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Sim'),
              ),
            ],
          ),
        );

        if (confirmarSaida == true) {
          // Sair do aplicativo
          Navigator.of(context).pop();
        }
      },
      child: Scaffold(
        body: GradientBackground(
          child: SafeArea(
            child: Column(
              children: [
                // Header com título e botão voltar com callback para retornar à tela inicial
                AppHeader(
                  title: 'Gerenciamento de Filas',
                  centerTitle: true,
                  onBackPressed: _handleBack,
                  actions: [
                    IconButton(
                      icon: const Icon(Icons.refresh,
                          color: Color(0xFF34ECCB), size: 22),
                      onPressed: () {
                        _verificarEAtualizarFila();
                        _mostrarMensagem('Atualizando dados...',
                            isError: false);
                      },
                      tooltip: 'Atualizar manualmente',
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),

                Expanded(
                  child: Obx(() {
                    // Mostra loader enquanto carrega os médicos
                    if (controller.isLoading.value) {
                      return const Center(child: CircularProgressIndicator());
                    } // Mostrar a lista de médicos e carrosséis, mesmo quando não houver médicos
                    return Column(
                      children: [
                        // Médicos disponíveis - removida a seta de atualização lateral
                        Row(
                          children: [
                            Expanded(
                              child: Container(
                                height: 110,
                                margin:
                                    const EdgeInsets.symmetric(vertical: 8.0),
                                child: _buildMedicosDisponiveis(),
                              ),
                            ),
                            // Seta de atualização removida
                          ],
                        ), // Conteúdo restante (fila do médico selecionado) dentro de um Expanded com SingleChildScrollView
                        Expanded(
                          child: Column(
                            children: [
                              // Sempre mostrar o carrossel de estatísticas, independentemente de ter um médico selecionado
                              _buildMedicoMetricas(),

                              // Área para os detalhes do médico e fila, apenas se houver um médico selecionado
                              Expanded(
                                child: _medicoSelecionadoId != null
                                    ? Column(
                                        children: [
                                          // Título da fila do médico
                                          Padding(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 16.0,
                                                vertical: 8.0),
                                            child: Center(
                                              child: Text(
                                                _medicoSelecionadoNome != null
                                                    ? 'Fila do(a) Dr(a). $_medicoSelecionadoNome'
                                                    : 'Fila do(a) Dr(a).',
                                                style: const TextStyle(
                                                  fontFamily: 'Georgia',
                                                  fontSize: 18,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                                textAlign: TextAlign.center,
                                              ),
                                            ),
                                          ),

                                          // TabBar para separar fila de espera e em atendimento
                                          Container(
                                            height: 48,
                                            margin: const EdgeInsets.symmetric(
                                                horizontal: 16, vertical: 8),
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              borderRadius:
                                                  BorderRadius.circular(25),
                                              boxShadow: [
                                                BoxShadow(
                                                  color: Colors.black
                                                      .withOpacity(0.1),
                                                  blurRadius: 4,
                                                  offset: const Offset(0, 2),
                                                ),
                                              ],
                                            ),
                                            child: TabBar(
                                              controller: _tabController,
                                              tabs: const [
                                                Tab(text: "Aguardando"),
                                                Tab(text: "Em Atendimento"),
                                              ],
                                              indicator: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(25),
                                                color: Colors.teal
                                                    .withOpacity(0.2),
                                              ),
                                              labelColor: Colors.teal,
                                              unselectedLabelColor: Colors.grey,
                                              dividerColor: Colors.transparent,
                                            ),
                                          ),

                                          // Área para TabBarView - usando Expanded para ocupar o espaço restante
                                          Expanded(
                                            child: _carregandoFila
                                                ? const Center(
                                                    child:
                                                        CircularProgressIndicator())
                                                : TabBarView(
                                                    controller: _tabController,
                                                    children: [
                                                      _listarPacientesAguardando(),
                                                      _listarPacientesEmAtendimento(),
                                                    ],
                                                  ),
                                          ),
                                        ],
                                      )
                                    : const Expanded(
                                        child: Center(
                                          child: Text(
                                            'Selecione um médico para visualizar a fila',
                                            style: TextStyle(
                                              fontFamily: 'Georgia',
                                              fontSize: 16,
                                            ),
                                          ),
                                        ),
                                      ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    );
                  }),
                ),
              ],
            ),
          ),
        ),
        floatingActionButton:
            _medicoSelecionadoId != null && controller.medicos.isNotEmpty
                ? FloatingActionButtonMenu(
                    onAlertPressed: _enviarAlertaFila,
                    onMessagePressed: _enviarMensagemPersonalizadaCompleta,
                    onEmergencyPressed: _ativarEmergenciaSecretaria,
                    onMedicoEmergencyPressed: _ativarEmergenciaMedico,
                    onRepairPressed: _verificarEReparFila,
                    onFinalizarEmergenciaPressed: _finalizarEmergencia,
                  )
                : null,
      ),
    );
  }

  Widget _buildMedicoCard(ParseObject medico, bool isSelected) {
    // Acessando os campos corretamente do objeto ParseObject
    final String nome = medico.get<String>('nome') ?? 'Médico';
    final String especialidade =
        medico.get<String>('especialidade') ?? 'Especialidade';

    return GestureDetector(
      onTap: () => _selecionarMedico(medico),
      child: Container(
        width: 145, // Valor médio entre 130-160
        constraints: const BoxConstraints(
          maxWidth: 160,
          minWidth: 130,
          maxHeight: 150, // Aumentado para 150px (era 130px)
          minHeight: 140, // Aumentado para 140px (era 120px)
        ),
        margin: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          color: isSelected ? Colors.teal.withOpacity(0.2) : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? Colors.teal : Colors.grey.withOpacity(0.3),
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        // Usando Stack para posicionar o indicador de seleção sem afetar o layout principal
        child: Stack(
          children: [
            // Conteúdo principal do card com Padding para dar espaço suficiente
            Padding(
              padding: const EdgeInsets.all(12),
              // Usando SingleChildScrollView para evitar overflow caso o conteúdo seja maior
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Ícone de perfil com raio reduzido
                    CircleAvatar(
                      radius: 18, // Reduzido de 24 para 18
                      backgroundColor: Colors.teal.withOpacity(0.1),
                      child: Text(
                        nome.isNotEmpty ? nome[0].toUpperCase() : 'M',
                        style: const TextStyle(
                          fontSize: 16, // Reduzido de 20 para 16
                          color: Colors.teal,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(
                        height:
                            6), // Aumentado de 4 para 6 para dar mais espaço
                    // Nome com texto ajustado para caber
                    Text(
                      nome,
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontFamily: 'Georgia',
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 6), // Aumentado de 4 para 6
                    // Especialidade em um container com altura fixa
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 5,
                          vertical: 4), // Aumentado padding vertical
                      constraints: const BoxConstraints(
                          minHeight: 40), // Aumentado de 28 para 40
                      decoration: BoxDecoration(
                        color: Colors.teal.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.teal.withOpacity(0.3)),
                      ),
                      child: Text(
                        especialidade,
                        style: TextStyle(
                          fontFamily: 'Georgia',
                          fontSize: 12,
                          color: Colors.teal.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Indicador de médico selecionado
            if (isSelected)
              Positioned(
                top: 5,
                right: 5,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: const BoxDecoration(
                    color: Colors.teal,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 14,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildMedicoMetricas() {
    // Tamanho dos cards calculado para dispositivo
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;
    // Ajustar o tamanho do card para ocupar aproximadamente 40% da largura da tela
    final cardWidth = screenWidth * 0.40;

    // Verificar se há médico selecionado para evitar requisições desnecessárias
    final bool medicoSelecionado =
        _medicoSelecionadoId != null && _medicoSelecionadoId!.isNotEmpty;
    // Verificar se existem médicos vinculados ao hospital
    final bool existemMedicosVinculados = controller.medicos.isNotEmpty;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Título da seção de métricas
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Text(
              medicoSelecionado
                  ? 'Estatísticas de Atendimento'
                  : 'Estatísticas',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                fontFamily: 'Georgia',
                color: medicoSelecionado ? Colors.teal : Colors.grey,
              ),
            ),
          ),

          // Se não há médico selecionado, mostrar mensagem informativa
          if (!medicoSelecionado)
            Container(
              height: 120,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.grey.withOpacity(0.7),
                      size: 32,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      existemMedicosVinculados
                          ? "Selecione um médico para ver estatísticas"
                          : "Nenhum médico vinculado ao hospital",
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 14,
                        fontFamily: 'Georgia',
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            // Carrossel horizontal de cards com indicador de rolagem
            Container(
              height: 120, // Altura fixa para o carrossel
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    SizedBox(
                      width: cardWidth,
                      child: _buildEstatisticaCard(
                        'Total',
                        _metricas['totalPacientes']?.toString() ?? '0',
                        Icons.people,
                        Colors.teal,
                      ),
                    ),
                    SizedBox(
                      width: cardWidth,
                      child: _buildEstatisticaCard(
                        'Atendidos',
                        _metricas['pacientesAtendidos']?.toString() ?? '0',
                        Icons.check_circle,
                        Colors.green,
                      ),
                    ),
                    SizedBox(
                      width: cardWidth,
                      child: _buildEstatisticaCard(
                        'Espera média',
                        _formatarTempo(_metricas['tempoMedioEspera']),
                        Icons.timer,
                        Colors.orange,
                      ),
                    ),
                    SizedBox(
                      width: cardWidth,
                      child: _buildEstatisticaCard(
                        'Atendimento',
                        _formatarTempo(_metricas['tempoMedioAtendimento']),
                        Icons.medical_services,
                        Colors.deepOrange,
                      ),
                    ),
                  ],
                ),
              ),
            ),

          // Indicador de rolagem horizontal e status de atualização
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (medicoSelecionado) ...[
                const Icon(
                  Icons.swipe_left,
                  size: 14,
                  color: Colors.grey,
                ),
                const SizedBox(width: 4),
                Text(
                  "Deslize para mais estatísticas",
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                    fontStyle: FontStyle.italic,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  "Atualização:",
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade700,
                  ),
                ),
                const SizedBox(width: 4),
                // Indicador de atualização em tempo real
                Container(
                  height: 8,
                  width: 8,
                  decoration: BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.green.withOpacity(0.5),
                        blurRadius: 4,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEstatisticaCard(
      String titulo, String valor, IconData icone, Color cor) {
    // Ajustar o tamanho baseado na orientação
    final double iconSize = _isLandscape ? 24 : 20;
    final double titleFontSize = _isLandscape ? 14 : 13;
    final double valueFontSize = _isLandscape ? 22 : 18;
    final double elevation = _isLandscape ? 4 : 3;
    final double padding = _isLandscape ? 12 : 10;

    return Card(
      elevation: elevation,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      shadowColor: Colors.black.withAlpha(51), // 0.2 opacity = 51/255
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
            color: cor.withAlpha(51), width: 1.0), // 0.2 opacity = 51/255
      ),
      child: Padding(
        padding: EdgeInsets.all(padding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircleAvatar(
              radius: iconSize,
              backgroundColor: cor.withAlpha(38), // 0.15 opacity = 38/255
              child: Icon(icone, color: cor, size: iconSize),
            ),
            const SizedBox(height: 8),
            Text(
              titulo,
              style: TextStyle(
                fontSize: titleFontSize,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 2),
            Text(
              valor,
              style: TextStyle(
                fontSize: valueFontSize,
                fontWeight: FontWeight.bold,
                color: cor,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _listarPacientesAguardando() {
    // Filtragem eficiente e explícita para pacientes com status 'aguardando'
    final pacientesAguardando = _filaAtual.where((p) {
      final status = p.get<String>('status') ?? '';
      return status == 'aguardando';
    }).toList();

    // Ordenar por posição
    pacientesAguardando.sort((a, b) {
      final posicaoA = a.get<int>('posicao') ?? 999;
      final posicaoB = b.get<int>('posicao') ?? 999;
      return posicaoA.compareTo(posicaoB);
    });

    if (pacientesAguardando.isEmpty) {
      return _buildFilaVazia();
    }

    // Informações de debug
    debugPrint(
        'Renderizando pacientes aguardando: ${pacientesAguardando.length}');
    for (var p in pacientesAguardando) {
      final nome = p.get<String>('nome') ?? 'Sem nome';
      final posicao = p.get<int>('posicao') ?? 0;
      debugPrint('Paciente aguardando: $nome, Posição: $posicao');
    }

    return RefreshIndicator(
      onRefresh: _verificarEAtualizarFila,
      child: ReorderableListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: pacientesAguardando.length,
        onReorder: _reordenarPacientes,
        itemBuilder: (context, index) {
          final paciente = pacientesAguardando[index];
          final posicao = paciente.get<int>('posicao') ?? (index + 1);
          return _buildCardPacienteFila(
            paciente,
            posicao: posicao,
            key: Key(paciente.objectId!),
          );
        },
      ),
    );
  }

  Widget _listarPacientesEmAtendimento() {
    // Filtragem eficiente e explícita para pacientes com status 'em_atendimento'
    final pacientesEmAtendimento = _filaAtual.where((p) {
      final status = p.get<String>('status') ?? '';
      return status == 'em_atendimento';
    }).toList();

    // Ordenar por tempo de início do atendimento (mais recente primeiro)
    pacientesEmAtendimento.sort((a, b) {
      final tempoA =
          a.get<DateTime>('data_inicio_atendimento')?.millisecondsSinceEpoch ??
              0;
      final tempoB =
          b.get<DateTime>('data_inicio_atendimento')?.millisecondsSinceEpoch ??
              0;
      return tempoB.compareTo(tempoA); // Ordem decrescente
    });

    if (pacientesEmAtendimento.isEmpty) {
      return _buildFilaVazia();
    }

    // Informações de debug
    debugPrint(
        'Renderizando pacientes em atendimento: ${pacientesEmAtendimento.length}');
    for (var p in pacientesEmAtendimento) {
      final nome = p.get<String>('nome') ?? 'Sem nome';
      debugPrint('Paciente em atendimento: $nome');
    }

    return RefreshIndicator(
      onRefresh: _verificarEAtualizarFila,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: pacientesEmAtendimento.length,
        itemBuilder: (context, index) {
          final paciente = pacientesEmAtendimento[index];
          return _buildCardPacienteFila(
            paciente,
            key: Key(paciente.objectId!),
          );
        },
      ),
    );
  }

  Widget _buildFilaVazia() {
    return RefreshIndicator(
      onRefresh: _verificarEAtualizarFila,
      child: ListView(
        physics: const AlwaysScrollableScrollPhysics(),
        children: [
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.4,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.people_outline,
                    size: 72,
                    color: Colors.grey.withOpacity(0.7),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Sem pacientes na fila',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.withOpacity(0.8),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Nenhum paciente aguardando atendimento',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey.withOpacity(0.6),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    "Atualizações em tempo real ativas",
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.teal.withAlpha(204), // 0.8 * 255 = 204
                      fontStyle: FontStyle.italic,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        "Verificando novos pacientes",
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Static green dot instead of animation
                      Container(
                        height: 10,
                        width: 10,
                        decoration: BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.green.withOpacity(0.5),
                              blurRadius: 4,
                              spreadRadius: 1,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCardPacienteFila(
    ParseObject paciente, {
    int? posicao,
    Key? key,
  }) {
    final status = paciente.get<String>('status') ?? 'aguardando';
    final pacienteNome = paciente.get<String>('nome') ??
        paciente.get<String>('nome_paciente') ??
        'Paciente';
    final pacienteTelefone = paciente.get<String>('telefone') ??
        paciente.get<String>('telefone_paciente') ??
        '';
    final tempoEspera = _calcularTempoEspera(paciente);
    final isEmAtendimento = status == 'em_atendimento';

    // Para pacientes em atendimento, calcular o tempo de atendimento
    final tempoAtendimento =
        isEmAtendimento ? _calcularTempoAtendimento(paciente) : 0;

    // Layout expandido para orientação paisagem (landscape)
    if (_isLandscape) {
      return Card(
        key: key,
        margin: const EdgeInsets.only(bottom: 12),
        elevation: 3,
        shadowColor: Colors.black.withAlpha(51), // 0.2 opacity = 51/255
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
          side: BorderSide(
            color: isEmAtendimento ? Colors.teal : Colors.grey.shade300,
            width: isEmAtendimento ? 1.0 : 0.5,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Indicador de posição/status
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: isEmAtendimento
                      ? Colors.teal.withAlpha(51) // 0.2 opacity = 51/255
                      : Colors.teal.withAlpha(25), // 0.1 opacity = 25/255
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Center(
                  child: isEmAtendimento
                      ? const Icon(Icons.medical_services,
                          color: Colors.teal, size: 26)
                      : Text(
                          '${posicao ?? ""}',
                          style: const TextStyle(
                            fontFamily: 'Georgia',
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            color: Colors.teal,
                          ),
                        ),
                ),
              ),
              const SizedBox(width: 16),

              // Informações do paciente
              Expanded(
                flex: 3,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      pacienteNome,
                      style: const TextStyle(
                        fontFamily: 'Georgia',
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Icon(Icons.phone, size: 18, color: Colors.grey),
                        const SizedBox(width: 8),
                        Text(
                          pacienteTelefone.isEmpty
                              ? 'Sem telefone'
                              : pacienteTelefone,
                          style: TextStyle(
                            fontFamily: 'Georgia',
                            fontSize: 18,
                            color: Colors.grey.shade700,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Informações de tempo
              Expanded(
                flex: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (isEmAtendimento)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 14, vertical: 8),
                        decoration: BoxDecoration(
                          color:
                              Colors.teal.withAlpha(25), // 0.1 opacity = 25/255
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(Icons.access_time,
                                size: 18, color: Colors.teal),
                            const SizedBox(width: 8),
                            Text(
                              '${_formatarTempo(tempoAtendimento)} em atendimento',
                              style: const TextStyle(
                                fontFamily: 'Georgia',
                                fontSize: 16,
                                color: Colors.teal,
                              ),
                            ),
                          ],
                        ),
                      )
                    else if (tempoEspera > 0)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 14, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.orange
                              .withAlpha(25), // 0.1 opacity = 25/255
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(Icons.timer,
                                size: 18, color: Colors.orangeAccent),
                            const SizedBox(width: 8),
                            Text(
                              '${_formatarTempo(tempoEspera)} aguardando',
                              style: const TextStyle(
                                fontFamily: 'Georgia',
                                fontSize: 16,
                                color: Colors.orangeAccent,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),

              // Botões de ação
              Row(
                children: [
                  // Botão de atendimento/finalizar
                  IconButton(
                    icon: Icon(
                      isEmAtendimento
                          ? Icons.check_circle_outline
                          : Icons.play_circle_outline,
                      color: isEmAtendimento ? Colors.green : Colors.teal,
                      size: 32,
                    ),
                    onPressed: () {
                      if (isEmAtendimento) {
                        _finalizarAtendimento(paciente);
                      } else {
                        _iniciarAtendimento(paciente);
                      }
                    },
                    tooltip: isEmAtendimento
                        ? 'Finalizar atendimento'
                        : 'Iniciar atendimento',
                  ),
                  const SizedBox(width: 10),
                  // Botão de remoção
                  IconButton(
                    icon: const Icon(
                      Icons.cancel_outlined,
                      color: Colors.red,
                      size: 32,
                    ),
                    onPressed: () => _removerPaciente(paciente),
                    tooltip: 'Remover da fila',
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    }

    // Layout original para orientação retrato (portrait)
    return Card(
      key: key,
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 3,
      shadowColor: Colors.black.withAlpha(51), // 0.2 opacity = 51/255
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: BorderSide(
          color: isEmAtendimento ? Colors.teal : Colors.grey.shade300,
          width: isEmAtendimento ? 1.0 : 0.5,
        ),
      ),
      child: IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Indicador de posição/status
            Container(
              width: 50,
              decoration: BoxDecoration(
                color: isEmAtendimento
                    ? Colors.teal.withAlpha(51) // 0.2 opacity = 51/255
                    : Colors.teal.withAlpha(25), // 0.1 opacity = 25/255
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(10),
                  bottomLeft: Radius.circular(10),
                ),
              ),
              child: Center(
                child: isEmAtendimento
                    ? const Icon(Icons.medical_services, color: Colors.teal)
                    : Text(
                        '${posicao ?? ""}',
                        style: const TextStyle(
                          fontFamily: 'Georgia',
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.teal,
                        ),
                      ),
              ),
            ),

            // Conteúdo principal
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Nome com ícone de status
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            pacienteNome,
                            style: const TextStyle(
                              fontFamily: 'Georgia',
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),

                    // Informações secundárias
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: Row(
                            children: [
                              const Icon(
                                Icons.phone,
                                size: 14,
                                color: Colors.grey,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                pacienteTelefone.isEmpty
                                    ? 'Sem telefone'
                                    : pacienteTelefone,
                                style: TextStyle(
                                  fontFamily: 'Georgia',
                                  fontSize: 14,
                                  color: Colors.grey
                                      .withAlpha(179), // 0.7 opacity = 179/255
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    // Tempo em atendimento ou aguardando (agora abaixo do telefone)
                    const SizedBox(height: 4),
                    if (isEmAtendimento)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color:
                              Colors.teal.withAlpha(25), // 0.1 opacity = 25/255
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.access_time,
                              size: 14,
                              color: Colors.teal,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${_formatarTempo(tempoAtendimento)} em atendimento',
                              style: const TextStyle(
                                fontFamily: 'Georgia',
                                fontSize: 12,
                                color: Colors.teal,
                              ),
                            ),
                          ],
                        ),
                      )
                    else if (!isEmAtendimento && tempoEspera > 0)
                      Row(
                        children: [
                          const Icon(
                            Icons.timer,
                            size: 14,
                            color: Colors.orangeAccent,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${_formatarTempo(tempoEspera)} aguardando',
                            style: const TextStyle(
                              fontFamily: 'Georgia',
                              fontSize: 14,
                              color: Colors.orangeAccent,
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
            ),

            // Botões de ação
            Container(
              width: 50,
              decoration: BoxDecoration(
                color: Colors.grey.withAlpha(13), // 0.05 opacity = 13/255
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(10),
                  bottomRight: Radius.circular(10),
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // Botão de atendimento/finalizar
                  IconButton(
                    icon: Icon(
                      isEmAtendimento
                          ? Icons.check_circle_outline
                          : Icons.play_circle_outline,
                      color: isEmAtendimento ? Colors.green : Colors.teal,
                    ),
                    onPressed: () {
                      if (isEmAtendimento) {
                        _finalizarAtendimento(paciente);
                      } else {
                        _iniciarAtendimento(paciente);
                      }
                    },
                    tooltip: isEmAtendimento
                        ? 'Finalizar atendimento'
                        : 'Iniciar atendimento',
                  ),
                  // Botão de remoção
                  IconButton(
                    icon: const Icon(
                      Icons.cancel_outlined,
                      color: Colors.red,
                    ),
                    onPressed: () => _removerPaciente(paciente),
                    tooltip: 'Remover da fila',
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Corrigir o botão de enviar alertas
  void _enviarMensagemFila() async {
    if (_medicoSelecionadoId == null || _medicoSelecionadoNome == null) {
      _mostrarMensagem('Selecione um médico primeiro', isError: true);
      return;
    }

    // Contagem de pacientes
    final pacientesNaFila = _filaAtual.where((p) {
      final status = p.get<String>('status') ?? '';
      return status == 'aguardando' || status == 'em_atendimento';
    }).length;

    if (pacientesNaFila == 0) {
      _mostrarMensagem('Não há pacientes na fila para receber mensagens',
          isError: true);
      return;
    }

    // Mostrar loading enquanto carrega as mensagens
    setState(() => _carregandoFila = true);

    try {
      // Garantir que as mensagens estejam carregadas
      await controller.carregarMensagensPredefinidas();
    } catch (e) {
      debugPrint('[ERRO] Falha ao carregar mensagens: $e');
      if (mounted) {
        _mostrarMensagem('Erro ao carregar mensagens predefinidas: $e',
            isError: true);
      }
    } finally {
      if (mounted) setState(() => _carregandoFila = false);
    }

    // Verificar montagem antes de prosseguir
    if (!mounted) return;

    await showDialog<Map<String, dynamic>>(
      context: context,
      barrierDismissible: true, // Permitir fechar clicando fora
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.notifications_active, color: Colors.orange),
            const SizedBox(width: 10),
            const Expanded(
              child: Text(
                'Enviar Alerta aos Pacientes',
                style: TextStyle(fontSize: 18),
              ),
            ),
          ],
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Fila do Dr(a) $_medicoSelecionadoNome',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.teal,
                ),
              ),
              Text(
                'Total de $pacientesNaFila paciente(s) na fila',
                style: const TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 15),
              const Text('Selecione uma mensagem:'),
              const SizedBox(height: 10),
              Flexible(
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: controller.mensagensPredefinidas.length + 1,
                  itemBuilder: (context, index) {
                    // Última opção: mensagem personalizada
                    if (index == controller.mensagensPredefinidas.length) {
                      return ListTile(
                        leading: const CircleAvatar(
                          backgroundColor: Colors.blue,
                          child: Icon(Icons.edit, color: Colors.white),
                        ),
                        title: const Text('Mensagem Personalizada'),
                        subtitle: const Text('Criar sua própria mensagem'),
                        onTap: () async {
                          Navigator.pop(context);
                          // Aguardar um breve momento para fechar o diálogo atual
                          await Future.delayed(
                              const Duration(milliseconds: 300));
                          if (mounted) {
                            _mostrarDialogoMensagemPersonalizada();
                          }
                        },
                      );
                    }

                    // Mensagens predefinidas
                    final mensagem = controller.mensagensPredefinidas[index];
                    final titulo = mensagem.get<String>('titulo') ?? '';
                    final texto = mensagem.get<String>('texto') ?? '';

                    return ListTile(
                      leading: const CircleAvatar(
                        backgroundColor: Colors.orange,
                        child: Icon(Icons.message, color: Colors.white),
                      ),
                      title: Text(titulo),
                      subtitle: Text(texto,
                          maxLines: 2, overflow: TextOverflow.ellipsis),
                      onTap: () async {
                        Navigator.pop(context);

                        if (!mounted) return;

                        // Mostrar diálogo de carregamento
                        showDialog(
                          context: context,
                          barrierDismissible: false,
                          builder: (context) => const Center(
                            child: CircularProgressIndicator(),
                          ),
                        );

                        try {
                          // Enviar mensagem para a fila
                          final sucesso =
                              await controller.enviarMensagemParaFila(
                            _medicoSelecionadoId!,
                            titulo,
                            texto,
                            prioridade:
                                mensagem.get<String>('prioridade') ?? 'media',
                            icone: mensagem.get<String>('icone') ?? 'info',
                          );

                          if (mounted) {
                            // Fechar o diálogo de carregamento
                            Navigator.of(context).pop();

                            if (sucesso) {
                              _mostrarMensagem('Alerta enviado com sucesso!');
                            } else {
                              _mostrarMensagem(
                                'Erro ao enviar alerta: ${controller.error.value}',
                                isError: true,
                              );
                            }
                          }
                        } catch (e) {
                          if (mounted) {
                            // Fechar o diálogo de carregamento
                            Navigator.of(context).pop();

                            _mostrarMensagem(
                              'Erro ao enviar alerta: $e',
                              isError: true,
                            );
                          }
                        }
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancelar'),
          ),
        ],
      ),
    );
  }

  // Método melhorado para mensagens personalizadas
  void _mostrarDialogoMensagemPersonalizada() async {
    if (_medicoSelecionadoId == null || _medicoSelecionadoNome == null) {
      _mostrarMensagem('Selecione um médico primeiro', isError: true);
      return;
    }

    final pacientesNaFila = _filaAtual.where((p) {
      final status = p.get<String>('status') ?? '';
      return status == 'aguardando' || status == 'em_atendimento';
    }).length;

    if (pacientesNaFila == 0) {
      _mostrarMensagem('Não há pacientes na fila para receber mensagens',
          isError: true);
      return;
    }

    final tituloController = TextEditingController();
    final mensagemController = TextEditingController();
    String prioridade = 'media';

    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Row(
            children: [
              const Icon(Icons.edit_notifications, color: Colors.blue),
              const SizedBox(width: 10),
              const Expanded(
                child: Text(
                  'Mensagem Personalizada',
                  style: TextStyle(fontSize: 18),
                ),
              ),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Fila do Dr. $_medicoSelecionadoNome',
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, color: Colors.teal)),
                Text('$pacientesNaFila paciente(s) receberão esta mensagem',
                    style: const TextStyle(color: Colors.teal, fontSize: 14)),
                const SizedBox(height: 15),
                TextField(
                  controller: tituloController,
                  decoration: const InputDecoration(
                    labelText: 'Título',
                    border: OutlineInputBorder(),
                    hintText: 'Ex: Atendimento atrasado',
                  ),
                  maxLength: 50,
                ),
                const SizedBox(height: 10),
                TextField(
                  controller: mensagemController,
                  decoration: const InputDecoration(
                    labelText: 'Mensagem',
                    border: OutlineInputBorder(),
                    hintText: 'Detalhes da mensagem...',
                  ),
                  maxLines: 3,
                  maxLength: 200,
                ),
                const SizedBox(height: 15),
                const Text('Prioridade:'),
                RadioListTile<String>(
                  title: const Text('Baixa', style: TextStyle(fontSize: 15)),
                  value: 'baixa',
                  groupValue: prioridade,
                  onChanged: (value) => setState(() => prioridade = value!),
                  dense: true,
                ),
                RadioListTile<String>(
                  title: const Text('Média', style: TextStyle(fontSize: 15)),
                  value: 'media',
                  groupValue: prioridade,
                  onChanged: (value) => setState(() => prioridade = value!),
                  dense: true,
                ),
                RadioListTile<String>(
                  title: const Text('Alta', style: TextStyle(fontSize: 15)),
                  value: 'alta',
                  groupValue: prioridade,
                  onChanged: (value) => setState(() => prioridade = value!),
                  dense: true,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancelar'),
            ),
            ElevatedButton(
              onPressed: () {
                if (tituloController.text.trim().isEmpty ||
                    mensagemController.text.trim().isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                        content: Text('Preencha todos os campos'),
                        backgroundColor: Colors.red),
                  );
                  return;
                }

                Navigator.pop(context, {
                  'titulo': tituloController.text.trim(),
                  'texto': mensagemController.text.trim(),
                  'prioridade': prioridade,
                });
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.teal),
              child: const Text('Enviar'),
            ),
          ],
        ),
      ),
    );

    // Processar o resultado
    if (!mounted || result == null) return;

    // Mostrar diálogo de carregamento
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    try {
      final sucesso = await controller.enviarMensagemParaFila(
        _medicoSelecionadoId!,
        result['titulo'],
        result['texto'],
        prioridade: result['prioridade'],
        icone: 'custom',
      );

      if (mounted) {
        // Fechar o diálogo de carregamento
        Navigator.of(context).pop();

        if (sucesso) {
          _mostrarMensagem('Mensagem personalizada enviada com sucesso!');
        } else {
          _mostrarMensagem('Erro ao enviar mensagem: ${controller.error.value}',
              isError: true);
        }
      }
    } catch (e) {
      if (mounted) {
        // Fechar o diálogo de carregamento
        Navigator.of(context).pop();

        _mostrarMensagem('Erro ao enviar mensagem: $e', isError: true);
      }
    }
  }

  double _calcularTempoEspera(ParseObject paciente) {
    final dataEntrada = paciente.get<DateTime>('data_entrada');
    final status = paciente.get<String>('status') ?? '';

    if (dataEntrada != null) {
      final dataEntradaBrasil =
          BrazilTimeZone.parseObjectDateToBrazil(dataEntrada);
      if (dataEntradaBrasil == null) return 0.0;

      final agora = BrazilTimeZone.now();

      // Se o paciente está em atendimento, calcular o tempo até o início do atendimento
      if (status == 'em_atendimento') {
        final dataInicio = paciente.get<DateTime>('data_inicio_atendimento');
        if (dataInicio != null) {
          final dataInicioBrasil =
              BrazilTimeZone.parseObjectDateToBrazil(dataInicio);
          if (dataInicioBrasil != null) {
            final tempoEspera =
                dataInicioBrasil.difference(dataEntradaBrasil).inMinutes;
            return tempoEspera > 0 ? tempoEspera.toDouble() : 0.0;
          }
        }
      }

      // Se ainda está aguardando, calcular o tempo até agora
      final tempoEspera = agora.difference(dataEntradaBrasil).inMinutes;
      return tempoEspera > 0 ? tempoEspera.toDouble() : 0.0;
    }
    return 0.0;
  }

  double _calcularTempoAtendimento(ParseObject paciente) {
    final dataInicio = paciente.get<DateTime>('data_inicio_atendimento');

    if (dataInicio != null) {
      final dataInicioBrasil =
          BrazilTimeZone.parseObjectDateToBrazil(dataInicio);
      if (dataInicioBrasil == null) return 0.0;

      final agora = BrazilTimeZone.now();
      final tempoAtendimento = agora.difference(dataInicioBrasil).inMinutes;
      return tempoAtendimento > 0 ? tempoAtendimento.toDouble() : 0.0;
    }
    return 0.0;
  }

  Future<void> _showAddPacienteDialog() async {
    final nomeController = TextEditingController();
    final telefoneController = TextEditingController();

    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Adicionar Paciente'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nomeController,
              decoration: const InputDecoration(labelText: 'Nome'),
            ),
            TextField(
              controller: telefoneController,
              decoration: const InputDecoration(labelText: 'Telefone'),
              keyboardType: TextInputType.phone,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(null),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (nomeController.text.trim().isEmpty ||
                  telefoneController.text.trim().isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Preencha todos os campos'),
                    backgroundColor: Colors.red,
                  ),
                );
                return;
              }

              final nome = nomeController.text.trim();
              final telefone = telefoneController.text.trim();

              // Calcular próxima posição
              int proximaPosicao = 1;
              if (_filaAtual.isNotEmpty) {
                for (var paciente in _filaAtual) {
                  final posicao = paciente.get<int>('posicao') ?? 0;
                  if (posicao >= proximaPosicao) {
                    proximaPosicao = posicao + 1;
                  }
                }
              }

              // Return result to be handled outside dialog
              Navigator.of(context).pop({
                'success': true,
                'nome': nome,
                'telefone': telefone,
                'posicao': proximaPosicao,
              });
            },
            child: const Text('Adicionar'),
          ),
        ],
      ),
    );

    if (result != null && mounted) {
      try {
        setState(() => _carregandoFila = true);

        // Adicionar paciente usando o controlador
        final resultado = await controller.adicionarPacienteAFila(
          nome: result['nome'],
          telefone: result['telefone'],
          posicao: result['posicao'],
          medicoId: _medicoSelecionadoId!,
        );

        if (mounted) {
          if (resultado['success']) {
            _mostrarMensagem(
                'Paciente ${result['nome']} adicionado à fila com sucesso');
            _verificarEAtualizarFila();
          } else {
            _mostrarMensagem(
                'Erro ao adicionar paciente: ${resultado['error']}',
                isError: true);
          }
        }
      } catch (e) {
        if (mounted) {
          _mostrarMensagem('Erro ao adicionar paciente: $e', isError: true);
        }
      } finally {
        if (mounted) {
          setState(() => _carregandoFila = false);
        }
      }
    }
  }

  // Verifica e carrega as filas, mostrando indicadores de carregamento
  Future<void> _verificarEAtualizarFila() async {
    // Verificar explicitamente se há um médico selecionado e se o ID não está vazio
    if (_medicoSelecionadoId == null || _medicoSelecionadoId!.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Selecione um médico para visualizar a fila'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    debugPrint(
        '🔄 [ATUALIZAÇÃO MANUAL] Iniciando atualização da fila: ${BrazilTimeZone.now().toString()}');
    debugPrint(
        '📋 [ATUALIZAÇÃO MANUAL] Médico: $_medicoSelecionadoNome (ID: $_medicoSelecionadoId)');

    setState(() {
      _carregandoFila = true;
    });

    try {
      // Verificar e corrigir status dos usuários na fila
      debugPrint(
          '⏳ [ATUALIZAÇÃO MANUAL] Corrigindo status dos usuários na fila...');
      await controller.corrigirStatusEmFilaUsuarios();
      debugPrint(
          '✅ [ATUALIZAÇÃO MANUAL] Status dos usuários verificado e corrigido');

      // Verificar e reparar filas para o médico selecionado
      debugPrint(
          '⏳ [ATUALIZAÇÃO MANUAL] Verificando e reparando filas do médico...');
      await controller.verificarERepararFilas(_medicoSelecionadoId!);
      debugPrint('✅ [ATUALIZAÇÃO MANUAL] Filas verificadas e reparadas');

      // Recarregar a fila com dados atualizados
      debugPrint(
          '⏳ [ATUALIZAÇÃO MANUAL] Buscando dados atualizados da fila...');
      final filaAtualizada =
          await controller.obterFilaPorMedico(_medicoSelecionadoId!);
      final metricasAtualizadas =
          await controller.obterMetricasMedico(_medicoSelecionadoId!);

      // Consulta específica para pacientes atendidos para atualizar métricas
      final QueryBuilder<ParseObject> queryAtendidos =
          QueryBuilder<ParseObject>(ParseObject('Fila'))
            ..whereEqualTo('medico',
                ParseObject('Medico')..objectId = _medicoSelecionadoId)
            ..whereEqualTo('status', 'atendido')
            ..whereEqualTo('consultorio', controller.currentHospital);

      final atendidosResponse = await queryAtendidos.query();
      final int pacientesAtendidosContados =
          atendidosResponse.success && atendidosResponse.results != null
              ? atendidosResponse.results!.length
              : 0;

      debugPrint(
          '✅ [ATUALIZAÇÃO MANUAL] Dados atualizados obtidos com sucesso');
      debugPrint(
          '📊 [ATUALIZAÇÃO MANUAL] Total de pacientes na fila: ${filaAtualizada.length}');
      debugPrint(
          '📊 [ATUALIZAÇÃO MANUAL] Pacientes atendidos: $pacientesAtendidosContados');

      if (mounted) {
        setState(() {
          _filaAtual = filaAtualizada;
          _metricas = metricasAtualizadas;

          // Garantir que a contagem de atendidos esteja correta
          _metricas['pacientesAtendidos'] = pacientesAtendidosContados;

          // Log detalhado de cada paciente
          for (var p in _filaAtual) {
            final nome = p.get<String>('nome') ?? 'Sem nome';
            final status = p.get<String>('status') ?? 'desconhecido';
            final posicao = p.get<int>('posicao') ?? 0;
            debugPrint(
                "👤 [ATUALIZAÇÃO MANUAL] Paciente: $nome, Status: $status, Posição: $posicao, ID: ${p.objectId}");
          }
          _carregandoFila = false;
        });
      }

      debugPrint(
          '✅ [ATUALIZAÇÃO MANUAL] Atualização completa com sucesso: ${BrazilTimeZone.now().toString()}');
    } catch (e) {
      debugPrint("❌ [ATUALIZAÇÃO MANUAL] Erro ao verificar/atualizar fila: $e");
      if (mounted) {
        setState(() {
          _carregandoFila = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao atualizar fila: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Método para buscar e calcular corretamente o número de pacientes atendidos
  Future<void> _atualizarContadorPacientesAtendidos() async {
    if (!mounted || _medicoSelecionadoId == null) return;

    try {
      // Consulta específica para buscar pacientes atendidos
      final QueryBuilder<ParseObject> query =
          QueryBuilder<ParseObject>(ParseObject('Fila'))
            ..whereEqualTo('medico',
                ParseObject('Medico')..objectId = _medicoSelecionadoId)
            ..whereEqualTo('status', 'atendido')
            ..whereEqualTo('consultorio', controller.currentHospital);

      final response = await query.query();

      if (response.success && response.results != null) {
        setState(() {
          // Atualizar diretamente o contador de atendidos nas métricas
          _metricas['pacientesAtendidos'] = response.results!.length;
        });

        debugPrint(
            "[DEBUG] Pacientes atendidos atualizados: ${response.results!.length}");
      }
    } catch (e) {
      debugPrint("[DEBUG] Erro ao buscar pacientes atendidos: $e");
    }
  }

  // Reordenar pacientes na fila (arrastar e soltar)
  Future<void> _reordenarPacientes(int oldIndex, int newIndex) async {
    setState(() => _carregandoFila = true);

    try {
      // Filtrar apenas pacientes em espera
      final pacientesAguardando = _filaAtual
          .where((p) => p.get<String>('status') == 'aguardando')
          .toList();

      if (oldIndex >= pacientesAguardando.length ||
          newIndex >= pacientesAguardando.length) {
        throw Exception('Índice inválido ao reordenar');
      }

      // Reordenar localmente
      final paciente = pacientesAguardando.removeAt(oldIndex);
      pacientesAguardando.insert(newIndex, paciente);

      // Atualizar posições
      for (int i = 0; i < pacientesAguardando.length; i++) {
        pacientesAguardando[i].set('posicao', i + 1);
      }

      // Salvar no back4app
      final success = await controller.reordenarFila(
          _medicoSelecionadoId!, pacientesAguardando);

      if (success) {
        // Recarregar a fila para confirmar mudanças
        await _verificarEAtualizarFila();
        _mostrarMensagem('Fila reordenada com sucesso');
      } else {
        throw Exception('Erro ao salvar a nova ordem');
      }
    } catch (e) {
      _mostrarMensagem('Erro ao reordenar fila: $e', isError: true);
    } finally {
      if (mounted) {
        setState(() => _carregandoFila = false);
      }
    }
  }

  // Verifica se a orientação mudou e atualiza a UI conforme necessário
  void _verificarOrientacao() {
    if (!mounted) return;
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;
    if (_isLandscape != isLandscape) {
      setState(() {
        _isLandscape = isLandscape;
      });
    }
  }

  // Método para configurar listener de emergências
  void _configurarListenerEmergencias() async {
    try {
      final emergenciasSubscription = await LiveQuery().client.subscribe(
            QueryBuilder<ParseObject>(ParseObject('Emergencia'))
              ..whereEqualTo('status', 'pendente')
              ..includeObject(['fila'])
              ..orderByDescending('createdAt'),
          );

      emergenciasSubscription.on(LiveQueryEvent.create,
          (ParseObject emergencia) {
        _mostrarNotificacaoEmergencia(emergencia);
      });
    } catch (e) {
      debugPrint('Erro ao configurar listener de emergências: $e');
    }
  }

  // Método para mostrar notificação de emergência
  void _mostrarNotificacaoEmergencia(ParseObject emergencia) {
    try {
      final filaObj = emergencia.get<ParseObject>('fila');
      final tipo = emergencia.get<String>('tipo') ?? 'desconhecido';

      if (filaObj == null) return;

      // Verificar se a fila pertence ao hospital atual
      final consultorio = filaObj.get<ParseObject>('consultorio');
      if (consultorio == null ||
          consultorio.objectId != _hospitalAtual?.objectId) {
        return; // Ignorar emergências de outros hospitais
      }

      // Obter informações do paciente
      final nomePaciente = filaObj.get<String>('nome') ?? 'Paciente';

      // Definir título e cor com base no tipo
      String titulo;
      Color cor;

      switch (tipo) {
        case 'sos':
          titulo = 'EMERGÊNCIA SOS';
          cor = Colors.red;
          break;
        case 'denuncia':
          titulo = 'DENÚNCIA';
          cor = Colors.orange;
          break;
        case 'ajuda':
          titulo = 'SOLICITAÇÃO DE AJUDA';
          cor = Colors.blue;
          break;
        default:
          titulo = 'ALERTA';
          cor = Colors.yellow;
      }

      if (mounted) {
        // Mostrar alerta na interface
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            title: Row(
              children: [
                Icon(Icons.warning_amber_rounded, color: cor),
                const SizedBox(width: 10),
                Expanded(
                  child: Text(
                    titulo,
                    style: TextStyle(color: cor, fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Paciente: $nomePaciente'),
                const SizedBox(height: 8),
                Text(
                  tipo == 'sos'
                      ? 'O paciente acionou o botão de emergência. Atenção imediata é necessária!'
                      : tipo == 'denuncia'
                          ? 'O paciente registrou uma denúncia durante o atendimento.'
                          : 'O paciente está solicitando assistência durante seu atendimento.',
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: const Text('OK'),
              ),
              ElevatedButton(
                onPressed: () async {
                  Navigator.pop(context);

                  // Marcar emergência como atendida
                  emergencia.set('status', 'atendida');
                  await emergencia.save();

                  // Contatar o paciente por WhatsApp se disponível
                  final telefone = filaObj.get<String>('telefone');
                  if (telefone != null && telefone.isNotEmpty) {
                    try {
                      await _abrirWhatsApp(telefone);
                    } catch (e) {
                      debugPrint('Erro ao abrir WhatsApp: $e');
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Não foi possível abrir o WhatsApp'),
                        ),
                      );
                    }
                  }
                },
                style: ElevatedButton.styleFrom(backgroundColor: cor),
                child: const Text('Atender',
                    style: TextStyle(color: Colors.white)),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      debugPrint('Erro ao mostrar notificação de emergência: $e');
    }
  }

  // Método para abrir WhatsApp
  Future<void> _abrirWhatsApp(String telefone) async {
    try {
      await controller.abrirWhatsAppSecretaria(telefone);
    } catch (e) {
      debugPrint('Erro ao abrir WhatsApp: $e');
      throw Exception('Não foi possível abrir o WhatsApp: $e');
    }
  }

  // Método para construir a lista de médicos disponíveis como um carrossel
  Widget _buildMedicosDisponiveis() {
    // Usar um Map para garantir que não haja médicos duplicados, usando o ID como chave
    final Map<String, ParseObject> medicosMap = {};

    // Primeiro passo: coletar todos os médicos em um mapa
    for (var medico in controller.medicos) {
      final id = medico.objectId!;

      if (medicosMap.containsKey(id)) {
        // Médico já existe no mapa, então precisamos decidir qual versão manter
        final medicoExistente = medicosMap[id]!;
        final isExistenteAtivo = medicoExistente.get<bool>('ativo') ?? false;
        final isNovoAtivo = medico.get<bool>('ativo') ?? false;

        // Preferir médico ativo sobre inativo
        if (isNovoAtivo && !isExistenteAtivo) {
          medicosMap[id] =
              medico; // O novo está ativo e o existente não, então usar o novo
        } else if (isExistenteAtivo == isNovoAtivo) {
          // Ambos com mesmo status de ativação, então usar o mais recente
          final dataExistente =
              medicoExistente.get<DateTime>('updatedAt') ?? DateTime(1970);
          final dataNovo = medico.get<DateTime>('updatedAt') ?? DateTime(1970);

          if (dataNovo.isAfter(dataExistente)) {
            medicosMap[id] = medico;
          }
        }
        // Se o existente está ativo e o novo inativo, mantemos o existente (ativo)
      } else {
        // Se não existe no mapa ainda, adiciona
        medicosMap[id] = medico;
      }
    }

    // Segundo passo: filtrar apenas médicos ativos E vinculados para exibição
    final List<ParseObject> medicosUnicos = medicosMap.values.where((m) {
      // Verificação explícita do status 'ativo'
      final bool isAtivo = m.get<bool>('ativo') ?? false;

      // Verificação adicional para garantir que médicos vinculados ao hospital atual estejam incluídos
      final bool isVinculado =
          controller.medicoEstaVinculadoAoHospitalAtual(m.objectId!);

      return isAtivo && isVinculado;
    }).toList();

    // Log para debug da filtragem de médicos
    debugPrint(
        '[MÉDICOS] Total de médicos únicos (sem duplicação): ${medicosMap.length}');
    debugPrint(
        '[MÉDICOS] Total de médicos ativos para exibição: ${medicosUnicos.length}');

    for (var medico in medicosUnicos) {
      final nome = medico.get<String>('nome') ?? 'Sem nome';
      final ativo = medico.get<bool>('ativo') ?? false;
      debugPrint('[MÉDICOS] - $nome (ID: ${medico.objectId}) - Ativo: $ativo');
    } // Se não há médicos para exibir, mostrar mensagem
    if (medicosUnicos.isEmpty) {
      return Container(
        height: 120,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.medical_services_outlined,
                size: 40,
                color: Colors.grey.withOpacity(0.7),
              ),
              const SizedBox(height: 8),
              Text(
                "Nenhum médico ativo disponível",
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 16,
                  fontFamily: 'Georgia',
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    // Continuar com o código normal quando há médicos disponíveis
    return Column(
      children: [
        // Carrossel de cards
        Expanded(
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            physics: const BouncingScrollPhysics(),
            itemCount: medicosUnicos.length,
            itemBuilder: (context, index) {
              final medico = medicosUnicos[index];
              final isSelected = medico.objectId == _medicoSelecionadoId;
              return _buildMedicoCard(medico, isSelected);
            },
          ),
        ),

        // Indicador de deslize (sempre visível, mesmo com poucos médicos)
        const SizedBox(height: 5),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(medicosUnicos.isEmpty ? Icons.info_outline : Icons.swipe,
                size: 14, color: Colors.grey),
            const SizedBox(width: 4),
            Text(
              medicosUnicos.isEmpty
                  ? "Nenhum médico vinculado ao hospital"
                  : (medicosUnicos.length > 1
                      ? "Deslize para navegar"
                      : "Médico disponível"),
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Função para formatar o tempo com horas e minutos quando necessário
  String _formatarTempo(dynamic minutos) {
    if (minutos == null) return '0 min';

    // Converter para int para garantir
    int mins = 0;
    if (minutos is int) {
      mins = minutos;
    } else if (minutos is double) {
      mins = minutos.round(); // Arredonda para o inteiro mais próximo
    } else if (minutos is Duration) {
      mins = minutos.inMinutes;
    } else {
      try {
        // Tenta converter para double e depois arredondar para int
        double valor = double.parse(minutos.toString());
        mins = valor.round();
      } catch (e) {
        return '0 min';
      }
    }

    // Se for menos de 60 minutos, retorna apenas os minutos
    if (mins < 60) {
      return '$mins min';
    }

    // Caso seja 60 minutos ou mais, formata em horas
    final horas = mins ~/ 60;
    final minutosRestantes = mins % 60;

    // Se não tiver minutos restantes, retorna apenas as horas
    if (minutosRestantes == 0) {
      return '${horas}h';
    }

    // Formato compacto para caber nos cards
    return '${horas}h ${minutosRestantes}min';
  }

  // Método para configurar observação de vínculos de médicos
  void _configurarTimerAtualizacao() {
    // Cancelar timer existente se houver
    _timer?.cancel();

    // Não configura mais um timer periódico
    // Agora será atualizado apenas via notificações de vínculos
    debugPrint(
        'Configurado para receber atualizações apenas por notificação de vínculo de médicos');

    // A atualização agora será controlada por:
    // 1. Notificações de novos vínculos de médicos (via LiveQuery)
    // 2. Botão de atualização manual
  }

  // Método para configurar listener de novos médicos vinculados
  void _configurarListenerNovosMedicos() async {
    try {
      // Cancelar assinatura anterior, se houver
      if (_hospitalMedicoUpdateSubscription != null) {
        try {
          // Corrigindo: unSubscribe retorna void, então não podemos usar await
          LiveQuery().client.unSubscribe(_hospitalMedicoUpdateSubscription!);
          debugPrint(
              'Inscrição anterior de HospitalMedicoUpdate cancelada com sucesso');
        } catch (e) {
          debugPrint('Erro ao cancelar inscrição anterior: $e');
        }
        _hospitalMedicoUpdateSubscription = null;
      }

      // Inscrever em atualizações de vínculo médico-hospital
      final QueryBuilder<ParseObject> query =
          QueryBuilder<ParseObject>(ParseObject('HospitalMedicoUpdate'))
            ..orderByDescending('createdAt');

      // Registrar a assinatura
      _hospitalMedicoUpdateSubscription =
          await LiveQuery().client.subscribe(query);

      // Configurar o handler para eventos de criação
      _hospitalMedicoUpdateSubscription!.on(LiveQueryEvent.create,
          (ParseObject update) {
        _handleHospitalMedicoUpdate(update);
      });

      debugPrint('LiveQuery para HospitalMedicoUpdate configurado com sucesso');
    } catch (e) {
      debugPrint('Erro ao configurar LiveQuery para HospitalMedicoUpdate: $e');
    }
  }

  // Método para processar atualizações de vínculos médico-hospital
  void _handleHospitalMedicoUpdate(ParseObject update) {
    if (!mounted) {
      debugPrint(
          '[SECRETARIA_UPDATE] ⚠️ Widget não está montado, ignorando notificação');
      return;
    }

    final hospitalId = update.get<String>('hospitalId');
    final medicoId = update.get<String>('medicoId');
    final medicoNome = update.get<String>('medicoNome') ?? 'Médico';
    final action = update.get<String>('action');
    final reason = update.get<String>('reason') ?? 'sem motivo';
    final isActive = update.get<bool>('isActive');
    final timestamp = update.get<DateTime>('timestamp') ?? BrazilTimeZone.now();

    // Logging detalhado da notificação recebida
    debugPrint('\n================================================');
    debugPrint(
        '[SECRETARIA_UPDATE] 📣 NOTIFICAÇÃO RECEBIDA (${BrazilTimeZone.now()})');
    debugPrint('[SECRETARIA_UPDATE] 🔍 ID da Notificação: ${update.objectId}');
    debugPrint('[SECRETARIA_UPDATE] 📅 Timestamp da notificação: $timestamp');
    debugPrint('[SECRETARIA_UPDATE] 🏥 Hospital: $hospitalId');
    debugPrint('[SECRETARIA_UPDATE] 👨‍⚕️ Médico: $medicoNome (ID: $medicoId)');
    debugPrint('[SECRETARIA_UPDATE] 🔄 Ação: $action');
    debugPrint('[SECRETARIA_UPDATE] 📝 Motivo: $reason');
    debugPrint(
        '[SECRETARIA_UPDATE] 🔘 Status ativo: ${isActive != null ? (isActive ? 'Sim' : 'Não') : 'Não informado'}');

    // Verificar se a atualização é relevante para o hospital atual
    final hospitalAtual = controller.currentHospital?.objectId;
    debugPrint('[SECRETARIA_UPDATE] 🏥 Hospital atual: $hospitalAtual');

    if (hospitalAtual == null) {
      debugPrint(
          '[SECRETARIA_UPDATE] ⚠️ Hospital atual não identificado, ignorando notificação');
      debugPrint('================================================\n');
      return;
    }

    if (hospitalId != hospitalAtual) {
      debugPrint(
          '[SECRETARIA_UPDATE] ℹ️ Notificação ignorada - hospital diferente');
      debugPrint('================================================\n');
      return;
    }

    // Nova verificação: se a notificação é sobre alteração de status de ativação
    if (action == 'status_update' && isActive != null) {
      debugPrint(
          '[SECRETARIA_UPDATE] 🔄 Atualização de status para médico: $medicoNome - Ativo: $isActive');

      // Verificar se o médico alterado é o que está selecionado atualmente
      if (_medicoSelecionadoId == medicoId && !isActive) {
        debugPrint(
            '[SECRETARIA_UPDATE] ⚠️ O médico desativado é o médico selecionado atualmente!');

        if (mounted) {
          // Se o médico foi desativado e estava selecionado, limpar a seleção
          setState(() {
            _medicoSelecionadoId = null;
            _medicoSelecionadoNome = null;
            _filaAtual = [];
            _metricas = {
              'totalPacientes': 0,
              'pacientesAguardando': 0,
              'pacientesEmAtendimento': 0,
              'pacientesAtendidos': 0,
              'tempoMedioEspera': const Duration(minutes: 0),
              'tempoMedioAtendimento': const Duration(minutes: 0),
            };
          });

          // Notificar usuário
          _mostrarMensagem(
              'O(A) Dr(a). $medicoNome não está mais disponível para atendimento.',
              isError: true);
        }
      }

      // De qualquer forma, atualizar a lista de médicos imediatamente na tela
      if (mounted) {
        setState(() => _carregandoFila = true);
      }

      controller.carregarMedicos().then((_) {
        debugPrint(
            '[SECRETARIA_UPDATE] ✅ Lista de médicos atualizada após mudança de status');

        if (mounted) {
          // Forçar uma reconstrução completa do widget
          setState(() {
            _carregandoFila = false;
          });

          // Se o médico foi ativado, mostrar mensagem positiva
          if (isActive) {
            _mostrarMensagem(
                'Dr(a). $medicoNome está disponível para atendimentos.',
                isError: false);
          }

          // Se não havia médicos selecionados e agora há médicos ativos disponíveis, selecionar o primeiro
          if (_medicoSelecionadoId == null) {
            // Filtrar apenas médicos ativos
            final medicosAtivos = controller.medicos
                .where((m) => m.get<bool>('ativo') ?? true)
                .toList();

            if (medicosAtivos.isNotEmpty) {
              final primeiroMedico = medicosAtivos.first;
              debugPrint(
                  '[SECRETARIA_UPDATE] 🔄 Selecionando automaticamente o primeiro médico ativo disponível');
              _selecionarMedico(primeiroMedico);
            }
          }
        }

        debugPrint(
            '[SECRETARIA_UPDATE] ✅ Processo de atualização de status concluído');
        debugPrint('================================================\n');
      }).catchError((error) {
        debugPrint('[SECRETARIA_UPDATE] ❌ Erro ao recarregar médicos: $error');

        if (mounted) {
          setState(() => _carregandoFila = false);
        }

        debugPrint('================================================\n');
      });

      return;
    }

    // Ação de vincular (adicionar) médico ao hospital
    if (action == 'vincular') {
      debugPrint(
          '[SECRETARIA_UPDATE] ✅ Médico vinculado ao hospital atual: $medicoNome');
      // Verificar se o médico está ativo antes de notificar
      final bool medicoAtivo = isActive ?? true;

      if (!medicoAtivo) {
        debugPrint(
            '[SECRETARIA_UPDATE] ⚠️ Médico vinculado está inativo, não será mostrado no carrossel');
      }

      debugPrint(
          '[SECRETARIA_UPDATE] ⏳ Iniciando atualização da lista de médicos...');

      // Exibir indicador visual de carregamento se necessário
      if (mounted) {
        setState(() => _carregandoFila = true);
      }

      // Recarregar a lista de médicos
      controller.carregarMedicos().then((_) {
        debugPrint(
            '[SECRETARIA_UPDATE] ✅ Lista de médicos atualizada com sucesso');
        debugPrint(
            '[SECRETARIA_UPDATE] 📊 Total de médicos após atualização: ${controller.medicos.length}');

        // Se o widget ainda estiver montado, atualizar a interface
        if (mounted) {
          // Forçar uma reconstrução completa do widget
          setState(() {
            _carregandoFila = false;
          });

          // Notificar usuário sobre atualização apenas se o médico estiver ativo
          if (medicoAtivo) {
            _mostrarMensagem(
                'Dr(a). $medicoNome foi adicionado(a) à lista de médicos deste hospital.',
                isError: false);
          }

          // Debug: listar médicos disponíveis após atualização
          debugPrint(
              '[SECRETARIA_UPDATE] 📋 Médicos disponíveis após atualização:');
          final medicosAtivos = controller.medicos
              .where((m) => m.get<bool>('ativo') ?? true)
              .toList();
          debugPrint(
              '[SECRETARIA_UPDATE] 📊 Total de médicos ativos: ${medicosAtivos.length}');

          for (var medico in medicosAtivos) {
            final nome = medico.get<String>('nome') ?? 'Sem nome';
            debugPrint('[SECRETARIA_UPDATE] - $nome (${medico.objectId})');
          }
        } else {
          debugPrint(
              '[SECRETARIA_UPDATE] ⚠️ Widget não está mais montado após carregar médicos');
        }

        debugPrint('[SECRETARIA_UPDATE] ✅ Processo de atualização concluído');
        debugPrint('================================================\n');
      }).catchError((error) {
        debugPrint('[SECRETARIA_UPDATE] ❌ Erro ao recarregar médicos: $error');

        if (mounted) {
          setState(() => _carregandoFila = false);
          _mostrarMensagem('Erro ao atualizar lista de médicos: $error',
              isError: true);
        }

        debugPrint('================================================\n');
      });
    }
    // Ação de desvincular (remover) médico do hospital
    else if (action == 'desvincular') {
      debugPrint(
          '[SECRETARIA_UPDATE] ❌ Médico desvinculado do hospital atual: $medicoNome');

      // Verificar se o médico desvinculado é o que está selecionado atualmente
      if (_medicoSelecionadoId == medicoId) {
        debugPrint(
            '[SECRETARIA_UPDATE] ⚠️ O médico desvinculado é o médico selecionado atualmente!');

        if (mounted) {
          // Limpar a seleção do médico e dados relacionados
          setState(() {
            debugPrint(
                '[SECRETARIA_UPDATE] 🧹 Limpando dados do médico selecionado e sua fila');
            _medicoSelecionadoId = null;
            _medicoSelecionadoNome = null;
            _filaAtual = [];
            _metricas = {
              'totalPacientes': 0,
              'pacientesAguardando': 0,
              'pacientesEmAtendimento': 0,
              'pacientesAtendidos': 0,
              'tempoMedioEspera': const Duration(minutes: 0),
              'tempoMedioAtendimento': const Duration(minutes: 0),
            };
          });

          // Notificar usuário
          _mostrarMensagem(
              'O(A) Dr(a). $medicoNome não está mais disponível neste hospital.',
              isError: true);
        } else {
          debugPrint(
              '[SECRETARIA_UPDATE] ⚠️ Widget não está mais montado, não é possível limpar seleção');
        }
      } else {
        debugPrint(
            '[SECRETARIA_UPDATE] ℹ️ O médico desvinculado não é o selecionado atualmente');
      }

      // De qualquer forma, atualizar a lista de médicos
      if (mounted) {
        setState(() => _carregandoFila = true);
      }

      controller.carregarMedicos().then((_) {
        debugPrint(
            '[SECRETARIA_UPDATE] ✅ Lista de médicos atualizada após desvinculação');
        debugPrint(
            '[SECRETARIA_UPDATE] 📊 Total de médicos após atualização: ${controller.medicos.length}');

        if (mounted) {
          // Forçar uma reconstrução completa do widget
          setState(() {
            _carregandoFila = false;
          });

          // Debug: listar médicos disponíveis após atualização
          debugPrint(
              '[SECRETARIA_UPDATE] 📋 Médicos disponíveis após desvinculação:');
          final medicosAtivos = controller.medicos
              .where((m) => m.get<bool>('ativo') ?? true)
              .toList();
          debugPrint(
              '[SECRETARIA_UPDATE] 📊 Total de médicos ativos: ${medicosAtivos.length}');

          for (var medico in medicosAtivos) {
            final nome = medico.get<String>('nome') ?? 'Sem nome';
            debugPrint('[SECRETARIA_UPDATE] - $nome (${medico.objectId})');
          }

          // Se não havia médicos selecionados e agora há médicos ATIVOS disponíveis, selecionar o primeiro
          if (_medicoSelecionadoId == null) {
            final medicosAtivos = controller.medicos
                .where((m) => m.get<bool>('ativo') ?? true)
                .toList();

            if (medicosAtivos.isNotEmpty) {
              final primeiroMedico = medicosAtivos.first;
              debugPrint(
                  '[SECRETARIA_UPDATE] 🔄 Selecionando automaticamente o primeiro médico ativo disponível');
              _selecionarMedico(primeiroMedico);
            }
          }
        } else {
          debugPrint(
              '[SECRETARIA_UPDATE] ⚠️ Widget não está mais montado após atualizar médicos');
        }

        debugPrint('[SECRETARIA_UPDATE] ✅ Processo de desvinculação concluído');
        debugPrint('================================================\n');
      }).catchError((error) {
        debugPrint('[SECRETARIA_UPDATE] ❌ Erro ao recarregar médicos: $error');

        if (mounted) {
          setState(() => _carregandoFila = false);
        }

        debugPrint('================================================\n');
      });
    } else {
      // Ação desconhecida
      debugPrint(
          '[SECRETARIA_UPDATE] ⚠️ Ação desconhecida: $action - ignorando');
      debugPrint('================================================\n');
    }
  }

  // ========== 🚨 SISTEMA DE EMERGÊNCIAS ==========

  /// 🚨 EMERGÊNCIA DA SECRETÁRIA: Pausar toda a fila
  Future<void> _ativarEmergenciaSecretaria() async {
    if (_medicoSelecionadoId == null || controller.currentHospital == null) {
      _mostrarMensagem('Selecione um médico primeiro', isError: true);
      return;
    }

    // Importar o controller de emergência
    final emergenciaController = Get.put(EmergenciaController());

    // Diálogo para selecionar motivo da emergência
    final result = await showDialog<Map<String, String>>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.emergency, color: Colors.red.shade700, size: 28),
            const SizedBox(width: 8),
            const Text('🚨 EMERGÊNCIA',
                style: TextStyle(fontWeight: FontWeight.bold)),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Selecione o motivo da emergência:',
                  style: TextStyle(fontWeight: FontWeight.w600)),
              const SizedBox(height: 16),
              ...[
                'Emergência médica geral',
                'Problema técnico',
                'Falta de energia',
                'Evacuação',
                'Outros'
              ]
                  .map(
                    (motivo) => ListTile(
                      title: Text(motivo),
                      leading: Radio<String>(
                        value: motivo,
                        groupValue: null,
                        onChanged: (value) =>
                            Navigator.pop(context, {'motivo': value!}),
                      ),
                    ),
                  )
                  .toList(),
              const SizedBox(height: 16),
              TextField(
                decoration: const InputDecoration(
                  labelText: 'Mensagem personalizada (opcional)',
                  border: OutlineInputBorder(),
                ),
                onSubmitted: (value) => Navigator.pop(
                    context, {'motivo': 'Outros', 'mensagem': value}),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancelar'),
          ),
        ],
      ),
    );

    if (result != null) {
      final sucesso = await emergenciaController.ativarEmergenciaSecretaria(
        medicoId: _medicoSelecionadoId!,
        hospitalId: controller.currentHospital!.objectId!,
        motivo: result['motivo']!,
        mensagemPersonalizada: result['mensagem'],
      );

      if (sucesso) {
        _mostrarMensagem('🚨 EMERGÊNCIA ATIVADA - Fila pausada',
            isError: false);
        _verificarEAtualizarFila(); // Atualizar interface
      } else {
        _mostrarMensagem('Erro ao ativar emergência', isError: true);
      }
    }
  }

  /// 🏥 EMERGÊNCIA DO MÉDICO: Pausar para cirurgia/emergência médica
  Future<void> _ativarEmergenciaMedico() async {
    if (_medicoSelecionadoId == null || controller.currentHospital == null) {
      _mostrarMensagem('Selecione um médico primeiro', isError: true);
      return;
    }

    final emergenciaController = Get.put(EmergenciaController());

    // Diálogo específico para emergência médica
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.medical_services, color: Colors.red.shade700, size: 28),
            const SizedBox(width: 8),
            const Text('🏥 Emergência Médica',
                style: TextStyle(fontWeight: FontWeight.bold)),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Tipo de emergência médica:',
                  style: TextStyle(fontWeight: FontWeight.w600)),
              const SizedBox(height: 12),

              // Tipo de emergência
              DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: 'Tipo de emergência',
                  border: OutlineInputBorder(),
                ),
                items: {
                  'cirurgia': '🔪 Cirurgia de emergência',
                  'emergencia_medica': '🚑 Emergência médica',
                  'outros': '⚠️ Outros'
                }
                    .entries
                    .map((entry) => DropdownMenuItem(
                          value: entry.key,
                          child: Text(entry.value),
                        ))
                    .toList(),
                onChanged: (value) {
                  // Store selection
                },
              ),

              const SizedBox(height: 16),

              // Tempo estimado
              TextField(
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'Tempo estimado (minutos)',
                  border: OutlineInputBorder(),
                  suffixText: 'min',
                ),
              ),

              const SizedBox(height: 16),

              // Motivo detalhado
              TextField(
                decoration: const InputDecoration(
                  labelText: 'Motivo detalhado',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () {
              // Collect data and return
              Navigator.pop(context, {
                'tipo': 'cirurgia', // Default
                'tempo': 60, // Default
                'motivo': 'Emergência médica'
              });
            },
            style:
                ElevatedButton.styleFrom(backgroundColor: Colors.red.shade700),
            child: const Text('Ativar Emergência',
                style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (result != null) {
      final sucesso = await emergenciaController.ativarEmergenciaMedico(
        medicoId: _medicoSelecionadoId!,
        hospitalId: controller.currentHospital!.objectId!,
        tipoEmergencia: result['tipo'],
        motivo: result['motivo'],
        tempoEstimadoMinutos: result['tempo'],
      );

      if (sucesso) {
        _mostrarMensagem('🏥 Emergência médica ativada - Pacientes notificados',
            isError: false);
        _verificarEAtualizarFila();
      } else {
        _mostrarMensagem('Erro ao ativar emergência médica', isError: true);
      }
    }
  }

  /// 📢 MENSAGEM PERSONALIZADA PARA TODOS OS PACIENTES
  Future<void> _enviarMensagemPersonalizadaCompleta() async {
    if (_medicoSelecionadoId == null) {
      _mostrarMensagem('Selecione um médico primeiro', isError: true);
      return;
    }

    final emergenciaController = Get.put(EmergenciaController());
    final tituloController = TextEditingController();
    final mensagemController = TextEditingController();
    bool pausarFila = false;

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Row(
            children: [
              Icon(Icons.edit_notifications,
                  color: Colors.blue.shade700, size: 28),
              const SizedBox(width: 8),
              const Text('📢 Mensagem Personalizada'),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: tituloController,
                  decoration: const InputDecoration(
                    labelText: 'Título da mensagem',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: mensagemController,
                  decoration: const InputDecoration(
                    labelText: 'Mensagem',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 4,
                ),
                const SizedBox(height: 16),
                CheckboxListTile(
                  title: const Text('Pausar fila junto com mensagem'),
                  subtitle: const Text('Os atendimentos serão pausados'),
                  value: pausarFila,
                  onChanged: (value) => setState(() => pausarFila = value!),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Cancelar'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (tituloController.text.isNotEmpty &&
                    mensagemController.text.isNotEmpty) {
                  final sucesso =
                      await emergenciaController.enviarMensagemPersonalizada(
                    medicoId: _medicoSelecionadoId!,
                    titulo: tituloController.text,
                    mensagem: mensagemController.text,
                    pausarFila: pausarFila,
                  );
                  Navigator.pop(context, sucesso);
                }
              },
              child: const Text('Enviar'),
            ),
          ],
        ),
      ),
    );

    if (result == true) {
      _mostrarMensagem('📢 Mensagem enviada para todos os pacientes',
          isError: false);
      if (pausarFila) {
        _verificarEAtualizarFila();
      }
    }
  }

  /// 🔔 ENVIAR ALERTA RÁPIDO PARA TODOS OS PACIENTES
  Future<void> _enviarAlertaFila() async {
    if (_medicoSelecionadoId == null) {
      _mostrarMensagem('Selecione um médico primeiro', isError: true);
      return;
    }

    final alertas = [
      'Atraso de 15 minutos',
      'Atraso de 30 minutos',
      'Atendimentos suspensos temporariamente',
      'Retomada dos atendimentos',
      'Alerta personalizado'
    ];

    final alerta = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.notifications_active,
                color: Colors.orange.shade700, size: 28),
            const SizedBox(width: 8),
            const Text('🔔 Enviar Alerta'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: alertas
              .map((alerta) => ListTile(
                    title: Text(alerta),
                    onTap: () => Navigator.pop(context, alerta),
                  ))
              .toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancelar'),
          ),
        ],
      ),
    );

    if (alerta != null) {
      final emergenciaController = Get.put(EmergenciaController());

      String mensagem = alerta;
      if (alerta == 'Alerta personalizado') {
        final controller = TextEditingController();
        final customMessage = await showDialog<String>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Alerta Personalizado'),
            content: TextField(
              controller: controller,
              decoration: const InputDecoration(
                labelText: 'Digite sua mensagem',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancelar'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context, controller.text),
                child: const Text('Enviar'),
              ),
            ],
          ),
        );
        if (customMessage?.isNotEmpty == true) {
          mensagem = customMessage!;
        } else {
          return;
        }
      }

      final sucesso = await emergenciaController.enviarMensagemPersonalizada(
        medicoId: _medicoSelecionadoId!,
        titulo: 'Alerta',
        mensagem: mensagem,
        pausarFila: false,
      );

      if (sucesso) {
        _mostrarMensagem('🔔 Alerta enviado para todos os pacientes',
            isError: false);
      } else {
        _mostrarMensagem('Erro ao enviar alerta', isError: true);
      }
    }
  }

  /// 🔧 VERIFICAR E REPARAR FILA
  Future<void> _verificarEReparFila() async {
    if (_medicoSelecionadoId == null) {
      _mostrarMensagem('Selecione um médico primeiro', isError: true);
      return;
    }

    // Mostrar diálogo de progresso
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 20),
            Text('Verificando e reparando fila...'),
          ],
        ),
      ),
    );

    try {
      // Verificar e reparar
      await controller.verificarERepararFilas(_medicoSelecionadoId!);
      await controller.corrigirStatusEmFilaUsuarios();

      // Atualizar dados
      await _verificarEAtualizarFila();

      Navigator.pop(context); // Fechar diálogo de progresso
      _mostrarMensagem('🔧 Fila verificada e reparada com sucesso',
          isError: false);
    } catch (e) {
      Navigator.pop(context); // Fechar diálogo de progresso
      _mostrarMensagem('Erro ao verificar fila: $e', isError: true);
    }
  }

  /// 🟢 FINALIZAR EMERGÊNCIA ATIVA
  Future<void> _finalizarEmergencia() async {
    if (_medicoSelecionadoId == null) {
      _mostrarMensagem('Selecione um médico primeiro', isError: true);
      return;
    }

    final emergenciaController = Get.put(EmergenciaController());

    // Primeiro verificar se existe emergência ativa
    await emergenciaController.verificarEstadoEmergencia(_medicoSelecionadoId!);

    if (!emergenciaController.filaEmEmergencia.value) {
      _mostrarMensagem('Não há emergência ativa para finalizar', isError: true);
      return;
    }

    // Diálogo de confirmação
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green.shade700, size: 28),
            const SizedBox(width: 8),
            const Text('🟢 Finalizar Emergência',
                style: TextStyle(fontWeight: FontWeight.bold)),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Você está prestes a finalizar a emergência ativa:',
                style: TextStyle(fontWeight: FontWeight.w600)),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.shade300),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Tipo: ${emergenciaController.tipoEmergencia.value}',
                      style: const TextStyle(fontWeight: FontWeight.w500)),
                  const SizedBox(height: 4),
                  Text(
                      'Motivo: ${emergenciaController.motivoEmergencia.value}'),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Row(
                children: [
                  Icon(Icons.info, color: Colors.green, size: 16),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Ao finalizar, todas as filas pausadas serão reativadas e os atendimentos retomados.',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green.shade700),
            child: const Text('Finalizar Emergência',
                style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (result == true) {
      final sucesso =
          await emergenciaController.finalizarEmergencia(_medicoSelecionadoId!);

      if (sucesso) {
        _mostrarMensagem(
            '🟢 Emergência finalizada - Filas reativadas com sucesso',
            isError: false);
        _verificarEAtualizarFila(); // Atualizar interface
      } else {
        _mostrarMensagem('Erro ao finalizar emergência', isError: true);
      }
    }
  }
}
