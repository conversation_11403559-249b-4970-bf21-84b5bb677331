import 'dart:io';
import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class DeviceManagerService {
  static DeviceManagerService? _instance;
  static DeviceManagerService get instance {
    return _instance ??= DeviceManagerService._internal();
  }

  DeviceManagerService._internal();

  static const String _keychainKey = 'device_unique_fingerprint';
  static const String _fallbackKey = 'device_fallback_id';
  static const String _secureStorageKey = 'secure_device_id';

  // Configuração para flutter_secure_storage
  static const _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
      sharedPreferencesName: 'saude_sem_espera_secure',
      preferencesKeyPrefix: 'device_',
    ),
    iOptions: IOSOptions(
      groupId: 'group.br.com.saudesemespera',
      accountName: 'SaudeSemEspera',
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  /// Obtém fingerprint único do dispositivo usando múltiplas estratégias
  Future<String> getDeviceFingerprint() async {
    try {
      debugPrint('🔍 Verificando dados do usuário...');

      // Estratégia 1: Tentar obter do Secure Storage (mais robusto)
      final secureId = await _getFromSecureStorage();
      if (secureId != null && secureId.isNotEmpty) {
        debugPrint('🔐 Device ID recuperado do Secure Storage: $secureId');
        await _saveToSharedPreferences(secureId); // Backup
        return secureId;
      }

      // Estratégia 2: Tentar obter do Keychain iOS (nativo)
      if (Platform.isIOS) {
        final keychainId = await _getFromKeychain();
        if (keychainId != null && keychainId.isNotEmpty) {
          debugPrint('🔑 Device ID recuperado do Keychain: $keychainId');
          await _saveToSecureStorage(
              keychainId); // Salvar também no secure storage
          await _saveToSharedPreferences(keychainId); // Backup
          return keychainId;
        }
      }

      // Estratégia 3: Tentar obter do SharedPreferences (último recurso)
      final prefs = await SharedPreferences.getInstance();
      final savedId = prefs.getString(_fallbackKey);
      if (savedId != null &&
          savedId.isNotEmpty &&
          !savedId.startsWith('DEV-ERROR')) {
        debugPrint('💾 Device ID recuperado do SharedPreferences: $savedId');
        await _saveToSecureStorage(savedId); // Migrar para secure storage
        return savedId;
      }

      // Estratégia 4: Tentar gerar baseado em identifierForVendor (iOS)
      if (Platform.isIOS) {
        final vendorId = await _getIOSVendorIdentifier();
        if (vendorId != null && vendorId.isNotEmpty) {
          final persistentId =
              'DEV-IOS-${vendorId.replaceAll('-', '').substring(0, 16).toUpperCase()}';
          debugPrint('📱 Device ID baseado em Vendor ID: $persistentId');
          await _saveAllLocations(persistentId);
          return persistentId;
        }
      }

      // Estratégia 5: Gerar novo fingerprint baseado em hardware
      final newFingerprint = await _generateHardwareFingerprint();
      await _saveAllLocations(newFingerprint);

      debugPrint('🆕 Novo Device ID gerado: $newFingerprint');
      return newFingerprint;
    } catch (e) {
      debugPrint('❌ Erro ao obter device fingerprint: $e');
      // Fallback final - ID baseado em timestamp
      final fallbackId = 'DEV-ERROR-${DateTime.now().millisecondsSinceEpoch}';
      await _saveToSharedPreferences(fallbackId);
      return fallbackId;
    }
  }

  /// Obtém identifier for vendor no iOS
  Future<String?> _getIOSVendorIdentifier() async {
    if (!Platform.isIOS) return null;

    try {
      final deviceInfo = DeviceInfoPlugin();
      final iosInfo = await deviceInfo.iosInfo;
      return iosInfo.identifierForVendor;
    } catch (e) {
      debugPrint('❌ Erro ao obter Vendor Identifier: $e');
      return null;
    }
  }

  /// Salva em todas as localizações para máxima persistência
  Future<void> _saveAllLocations(String deviceId) async {
    await Future.wait([
      _saveToSecureStorage(deviceId),
      _saveToKeychain(deviceId),
      _saveToSharedPreferences(deviceId),
    ]);
  }

  /// Salva no Flutter Secure Storage
  Future<void> _saveToSecureStorage(String deviceId) async {
    try {
      await _secureStorage.write(key: _secureStorageKey, value: deviceId);
      debugPrint('🔐 Device ID salvo no Secure Storage');
    } catch (e) {
      debugPrint('⚠️ Erro ao salvar no Secure Storage: $e');
    }
  }

  /// Recupera do Flutter Secure Storage
  Future<String?> _getFromSecureStorage() async {
    try {
      final result = await _secureStorage.read(key: _secureStorageKey);
      return result;
    } catch (e) {
      debugPrint('⚠️ Erro ao recuperar do Secure Storage: $e');
      return null;
    }
  }

  /// Salva no SharedPreferences (backup)
  Future<void> _saveToSharedPreferences(String deviceId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_fallbackKey, deviceId);
      debugPrint('💾 Device ID salvo no SharedPreferences');
    } catch (e) {
      debugPrint('⚠️ Erro ao salvar no SharedPreferences: $e');
    }
  }

  /// Obtém informações detalhadas do dispositivo
  Future<Map<String, dynamic>> getDeviceInfo() async {
    final deviceInfo = DeviceInfoPlugin();

    try {
      if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        return {
          'platform': 'iOS',
          'model': iosInfo.model,
          'systemVersion': iosInfo.systemVersion,
          'name': iosInfo.name,
          'identifierForVendor': iosInfo.identifierForVendor,
          'isPhysicalDevice': iosInfo.isPhysicalDevice,
        };
      } else if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        return {
          'platform': 'Android',
          'model': androidInfo.model,
          'systemVersion': androidInfo.version.release,
          'manufacturer': androidInfo.manufacturer,
          'brand': androidInfo.brand,
          'isPhysicalDevice': androidInfo.isPhysicalDevice,
        };
      }
    } catch (e) {
      debugPrint('❌ Erro ao obter informações do dispositivo: $e');
    }

    return {
      'platform': Platform.operatingSystem,
      'model': 'Desconhecido',
      'systemVersion': 'Desconhecido',
    };
  }

  /// Gera fingerprint baseado em características de hardware
  Future<String> _generateHardwareFingerprint() async {
    try {
      final deviceInfo = await getDeviceInfo();

      // Combinar informações do dispositivo
      final components = [
        deviceInfo['platform'] ?? 'unknown',
        deviceInfo['model'] ?? 'unknown',
        deviceInfo['manufacturer'] ?? 'unknown',
        deviceInfo['brand'] ?? 'unknown',
      ];

      // Adicionar timestamp para garantir unicidade
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final randomSuffix = (timestamp % 999999).toString().padLeft(6, '0');

      final combined = '${components.join('-')}-$randomSuffix';

      // Gerar hash para criar ID mais limpo
      final bytes = utf8.encode(combined);
      final digest = sha256.convert(bytes);
      final hash = digest.toString().substring(0, 16).toUpperCase();

      return 'DEV-$hash-${randomSuffix.substring(0, 8).toUpperCase()}';
    } catch (e) {
      debugPrint('❌ Erro ao gerar fingerprint de hardware: $e');
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      return 'DEV-ERROR-$timestamp';
    }
  }

  /// Salva ID no Keychain iOS (mais persistente)
  Future<void> _saveToKeychain(String deviceId) async {
    if (!Platform.isIOS) return;

    try {
      const channel = MethodChannel('br.com.saudesemespera.keychain');
      await channel.invokeMethod('setKeychainIdentifier', deviceId);
      debugPrint('🔑 Device ID salvo no Keychain');
    } catch (e) {
      debugPrint('⚠️ Erro ao salvar no Keychain: $e');
      // Não é crítico, fallback para SharedPreferences
    }
  }

  /// Recupera ID do Keychain iOS
  Future<String?> _getFromKeychain() async {
    if (!Platform.isIOS) return null;

    try {
      const channel = MethodChannel('br.com.saudesemespera.keychain');
      final result = await channel.invokeMethod('getKeychainIdentifier');
      return result as String?;
    } catch (e) {
      debugPrint('⚠️ Erro ao recuperar do Keychain: $e');
      return null;
    }
  }

  /// Limpa todos os IDs salvos (útil para testes)
  Future<void> clearDeviceId() async {
    try {
      // Limpar Secure Storage
      await _secureStorage.delete(key: _secureStorageKey);

      // Limpar SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_fallbackKey);

      // Limpar Keychain (iOS)
      if (Platform.isIOS) {
        try {
          const channel = MethodChannel('br.com.saudesemespera.keychain');
          await channel.invokeMethod('setKeychainIdentifier', '');
        } catch (e) {
          debugPrint('⚠️ Erro ao limpar Keychain: $e');
        }
      }

      debugPrint('🧹 Device IDs limpos de todas as localizações');
    } catch (e) {
      debugPrint('❌ Erro ao limpar device IDs: $e');
    }
  }

  /// Obtém timestamp da última validação do dispositivo
  Future<DateTime?> getLastValidation() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getInt('last_device_validation');
      return timestamp != null
          ? DateTime.fromMillisecondsSinceEpoch(timestamp)
          : null;
    } catch (e) {
      debugPrint('❌ Erro ao obter última validação: $e');
      return null;
    }
  }

  /// Atualiza timestamp da última validação
  Future<void> updateLastValidation() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(
          'last_device_validation', DateTime.now().millisecondsSinceEpoch);
      debugPrint('✅ Última validação atualizada');
    } catch (e) {
      debugPrint('❌ Erro ao atualizar última validação: $e');
    }
  }

  /// Limpa cache do dispositivo (método para compatibilidade com device_info_widget)
  Future<void> clearDeviceCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      // Remove validação e dados em cache
      await prefs.remove('last_device_validation');
      await prefs.remove('cached_device_info');
      debugPrint('🧹 Cache do dispositivo limpo');
    } catch (e) {
      debugPrint('❌ Erro ao limpar cache do dispositivo: $e');
    }
  }
}
