import 'package:flutter/material.dart';
import 'package:fila_app/controllers/notification_controller.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:fila_app/views/gradient_background.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:intl/intl.dart';
import 'dart:async';
import 'dart:convert';
import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/pdf.dart';
import 'package:printing/printing.dart';
import 'package:get/get.dart';
import '../utils/date_utils.dart';

class GerenciarFilaHospital extends StatefulWidget {
  const GerenciarFilaHospital({super.key});

  @override
  State<GerenciarFilaHospital> createState() => _GerenciarFilaHospitalState();
}

class _GerenciarFilaHospitalState extends State<GerenciarFilaHospital>
    with SingleTickerProviderStateMixin {
  late AnimationController _notificationController;
  final List<Map<String, dynamic>> _medicos = [];
  bool _isLoading = true;
  ParseObject? _currentHospital;
  Timer? _notificationTimer;
  final notificationController = Get.put(NotificationController());

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _carregarMedicos();
    _iniciarVerificacaoNotificacoes();
  }

  void _setupAnimations() {
    _notificationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _notificationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _notificationController.reverse();
      }
    });
  }

  void _iniciarVerificacaoNotificacoes() {
    _atualizarNotificacoes();

    _notificationTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      _atualizarNotificacoes();
    });
  }

  void _atualizarNotificacoes() {
    if (_currentHospital == null) return;

    for (var medico in _medicos) {
      notificationController.verificarNotificacoes(
        medico['id'],
        _currentHospital!.objectId!,
      );
    }
  }

  @override
  void dispose() {
    _notificationController.dispose();
    _notificationTimer?.cancel();
    super.dispose();
  }

  Future<void> _carregarMedicos() async {
    if (!mounted) return;
    setState(() => _isLoading = true);
    _medicos.clear();

    try {
      final currentUser = await ParseUser.currentUser() as ParseUser?;
      if (currentUser == null) {
        throw Exception('Usuário não encontrado');
      }

      var queryConsultorio =
          QueryBuilder<ParseObject>(ParseObject('consultorio'))
            ..whereEqualTo('user_consultorio', currentUser.toPointer());

      var consultorioResponse = await queryConsultorio.query();

      if (consultorioResponse.results == null ||
          consultorioResponse.results!.isEmpty) {
        throw Exception('Hospital não encontrado');
      }

      _currentHospital = consultorioResponse.results!.first;

      var queryMedicoConsultorio =
          QueryBuilder<ParseObject>(ParseObject('MedicoConsultorio'))
            ..whereEqualTo(
                'medicoConsultorio_consultorio',
                ParseObject('consultorio')
                  ..objectId = _currentHospital!.objectId);

      var medicoConsultorioResponse = await queryMedicoConsultorio.query();

      if (!medicoConsultorioResponse.success ||
          medicoConsultorioResponse.results == null) {
        debugPrint('Nenhum vínculo médico-consultório encontrado');
        return;
      }

      for (var vinculo in medicoConsultorioResponse.results!) {
        try {
          final medicoRelation = vinculo.get('medicoConsultorio_medico')
              as ParseRelation<ParseObject>;
          final medicoQuery = medicoRelation.getQuery()
            ..whereEqualTo('ativo', true);

          final medicoResponse = await medicoQuery.find();

          for (var medico in medicoResponse) {
            if (mounted) {
              setState(() {
                _medicos.add({
                  'id': medico.objectId!,
                  'nome': medico.get<String>('nome') ?? 'Nome não encontrado',
                  'especialidade':
                      medico.get<String>('especialidade') ?? 'Não informada',
                  'crm': medico.get<String>('crm') ?? '',
                  'vinculoId': vinculo.objectId!,
                });
              });
            }
          }
        } catch (e) {
          debugPrint('Erro ao processar vínculo ${vinculo.objectId}: $e');
          continue;
        }
      }
    } catch (e) {
      debugPrint('Erro ao carregar médicos: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao carregar médicos: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Widget _buildMedicoCard(Map<String, dynamic> medico) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        title: Text(
          medico['nome'],
          style: const TextStyle(
            fontFamily: 'Georgia',
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              medico['especialidade'],
              style: const TextStyle(
                fontFamily: 'Georgia',
                fontSize: 14,
              ),
            ),
            if (medico['crm']?.isNotEmpty ?? false)
              Text(
                'CRM: ${medico['crm']}',
                style: const TextStyle(
                  fontFamily: 'Georgia',
                  fontSize: 12,
                ),
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            NotificationIcon(
              medicoId: medico['id'],
              onTap: () => _abrirSolicitacoes(medico),
            ),
            IconButton(
              icon: const Icon(Icons.qr_code_2),
              onPressed: () => _mostrarQRCode(medico),
            ),
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: () => _abrirFila(medico),
            ),
          ],
        ),
      ),
    );
  }

  void _abrirSolicitacoes(Map<String, dynamic> medico) async {
    try {
      notificationController.marcarTodasComoLidas(medico['id']);

      await Get.toNamed(
        '/solicitacoes',
        arguments: medico,
      );

      if (mounted) {
        notificationController.verificarNotificacoes(
          medico['id'],
          _currentHospital?.objectId ?? '',
        );
      }
    } catch (e) {
      debugPrint('Erro ao abrir solicitações: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao abrir solicitações: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _mostrarQRCode(Map<String, dynamic> medico) async {
    if (_currentHospital == null) return;

    setState(() => _isLoading = true);

    try {
      final queryQRExistente =
          QueryBuilder<ParseObject>(ParseObject('QRCodeGerado'))
            ..whereEqualTo(
                'medico_id', ParseObject('Medico')..objectId = medico['id'])
            ..whereEqualTo(
                'consultorio_id',
                ParseObject('consultorio')
                  ..objectId = _currentHospital!.objectId)
            ..whereEqualTo('valido', true);

      final responseQRExistente = await queryQRExistente.query();

      if (!responseQRExistente.success ||
          responseQRExistente.results == null ||
          responseQRExistente.results!.isEmpty) {
        await _gerarNovoQRCode(medico);
      } else {
        setState(() => _isLoading = false);
        _exibirQRCode(responseQRExistente.results!.first, medico);
      }
    } catch (e) {
      debugPrint('Erro ao buscar QR Code: $e');
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erro ao buscar QR Code: $e')),
        );
      }
    }
  }

  Future<void> _gerarNovoQRCode(Map<String, dynamic> medico) async {
    try {
      setState(() => _isLoading = true);

      final queryQRExistente =
          QueryBuilder<ParseObject>(ParseObject('QRCodeGerado'))
            ..whereEqualTo(
                'medico_id', ParseObject('Medico')..objectId = medico['id'])
            ..whereEqualTo(
                'consultorio_id',
                ParseObject('consultorio')
                  ..objectId = _currentHospital!.objectId)
            ..whereEqualTo('valido', true);

      final responseQRExistente = await queryQRExistente.query();

      if (responseQRExistente.success &&
          responseQRExistente.results != null &&
          responseQRExistente.results!.isNotEmpty) {
        for (var qrCode in responseQRExistente.results!) {
          qrCode.set('valido', false);
          await qrCode.save();
        }
      }

      final sequencial = await _gerarSequencialUnico();
      final qrId = await _gerarQrIdUnico();

      final qrCodeGerado = ParseObject('QRCodeGerado')
        ..set('qr_id', qrId)
        ..set('medico_id', ParseObject('Medico')..objectId = medico['id'])
        ..set('consultorio_id',
            ParseObject('consultorio')..objectId = _currentHospital!.objectId)
        ..set('sequencial', sequencial)
        ..set('valido', true)
        ..set('impresso', false);

      if (!_validarCamposObrigatorios(qrCodeGerado)) {
        throw Exception('Campos obrigatórios não preenchidos');
      }

      final result = await qrCodeGerado.save();

      if (!mounted) return;
      setState(() => _isLoading = false);

      if (result.success) {
        _exibirQRCode(result.results!.first, medico);
      } else {
        throw Exception('Erro ao salvar QR Code');
      }
    } catch (e) {
      debugPrint('Erro ao gerar QR Code: $e');
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erro ao gerar QR Code: $e')),
        );
      }
    }
  }

  bool _validarCamposObrigatorios(ParseObject qrCode) {
    return qrCode.get<String>('qr_id') != null &&
        qrCode.get<ParseObject>('medico_id') != null &&
        qrCode.get<ParseObject>('consultorio_id') != null &&
        qrCode.get<String>('sequencial') != null;
  }

  Future<String> _gerarQrIdUnico() async {
    final agora = DateTime.now();
    final base =
        '${agora.year}${agora.month.toString().padLeft(2, '0')}${agora.day.toString().padLeft(2, '0')}_'
        '${agora.hour.toString().padLeft(2, '0')}${agora.minute.toString().padLeft(2, '0')}';

    var tentativa = 1;
    while (true) {
      final qrId = '${base}_$tentativa';

      final queryExistente =
          QueryBuilder<ParseObject>(ParseObject('QRCodeGerado'))
            ..whereEqualTo('qr_id', qrId);

      final response = await queryExistente.query();

      if (!response.success ||
          response.results == null ||
          response.results!.isEmpty) {
        return qrId;
      }

      tentativa++;
    }
  }

  Future<String> _gerarSequencialUnico() async {
    try {
      final hoje = BrazilTimeZone.now();
      final inicioDoDia = DateTime(hoje.year, hoje.month, hoje.day);

      final querySequencial =
          QueryBuilder<ParseObject>(ParseObject('QRCodeGerado'))
            ..whereGreaterThanOrEqualsTo('createdAt', inicioDoDia)
            ..orderByDescending('sequencial')
            ..setLimit(1);

      final response = await querySequencial.query();

      if (!response.success ||
          response.results == null ||
          response.results!.isEmpty) {
        return '001';
      }

      final ultimoQRCode = response.results!.first;
      final ultimoSequencial = ultimoQRCode.get<String>('sequencial') ?? '000';

      return (int.parse(ultimoSequencial) + 1).toString().padLeft(3, '0');
    } catch (e) {
      debugPrint('Erro ao gerar sequencial: $e');
      return '001';
    }
  }

  Future<void> _atualizarStatusImpressao(ParseObject qrCode) async {
    if (!(qrCode.get<bool>('impresso') ?? false)) {
      qrCode.set('impresso', true);

      try {
        await qrCode.save();
      } catch (e) {
        debugPrint('Erro ao atualizar status de impressão: $e');
        throw Exception('Erro ao atualizar status de impressão');
      }
    }
  }

  void _exibirQRCode(ParseObject qrCode, Map<String, dynamic> medico) {
    if (!mounted) return;

    final qrData = {
      'qrId': qrCode.get<String>('qr_id'),
      'medicoId': medico['id'],
      'medicoNome': medico['nome'],
      'especialidade': medico['especialidade'],
      'hospitalId': _currentHospital!.objectId,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          constraints: const BoxConstraints(maxWidth: 400),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color(0xFFE0EAFC),
                Color(0xFFCFDEF3),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
          ),
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Column(
                          children: [
                            const Text(
                              'QR Code de Atendimento',
                              style: TextStyle(
                                fontFamily: 'Georgia',
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Dr. ${medico['nome']}',
                              style: const TextStyle(
                                fontFamily: 'Georgia',
                                fontSize: 18,
                                fontWeight: FontWeight.normal,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            Text(
                              medico['especialidade'],
                              style: TextStyle(
                                fontFamily: 'Georgia',
                                fontSize: 16,
                                color: Colors.grey[600],
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  Container(
                    width: 280,
                    height: 280,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.black, width: 2),
                      borderRadius: BorderRadius.circular(12),
                      color: Colors.white,
                    ),
                    child: QrImageView(
                      data: jsonEncode(qrData),
                      version: QrVersions.auto,
                      size: 250,
                      backgroundColor: Colors.white,
                      errorCorrectionLevel: QrErrorCorrectLevel.H,
                    ),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'QR Code atualizado em:',
                    style: TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  Text(
                    BrazilDateFormat.formatDateTime(BrazilTimeZone.now()),
                    style: const TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'Sequencial: ${qrCode.get<String>('sequencial')}',
                    style: const TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 24),
                  Wrap(
                    spacing: 16,
                    runSpacing: 16,
                    alignment: WrapAlignment.center,
                    children: [
                      ElevatedButton.icon(
                        onPressed: () => _imprimirQRCode(qrCode, medico),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFFE0EAFC),
                          foregroundColor: Colors.black,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 12,
                          ),
                          elevation: 2,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        icon: const Icon(Icons.print),
                        label: const Text(
                          'Imprimir',
                          style: TextStyle(
                            fontFamily: 'Georgia',
                          ),
                        ),
                      ),
                      ElevatedButton.icon(
                        onPressed: () => _regenerarQRCode(medico),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFFE0EAFC),
                          foregroundColor: Colors.black,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 12,
                          ),
                          elevation: 2,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        icon: const Icon(Icons.refresh),
                        label: const Text(
                          'Atualizar',
                          style: TextStyle(
                            fontFamily: 'Georgia',
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Este QR Code pode ser impresso e fixado na recepção',
                    style: TextStyle(
                      fontFamily: 'Georgia',
                      fontSize: 14,
                      fontStyle: FontStyle.italic,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _imprimirQRCode(
      ParseObject qrCode, Map<String, dynamic> medico) async {
    try {
      final jaImpresso = qrCode.get<bool>('impresso') ?? false;
      if (jaImpresso) {
        if (!mounted) return;

        final reimprimirConfirmacao = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('QR Code já impresso'),
            content: const Text(
                'Este QR Code já foi impresso anteriormente. Deseja imprimir novamente?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('Não'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: const Text('Sim'),
              ),
            ],
          ),
        );

        if (reimprimirConfirmacao != true) return;
      }

      final pdf = pw.Document();

      final qrData = {
        'qrId': qrCode.get<String>('qr_id'),
        'medicoId': medico['id'],
        'medicoNome': medico['nome'],
        'especialidade': medico['especialidade'],
        'hospitalId': _currentHospital!.objectId,
        'timestamp': BrazilTimeZone.now().millisecondsSinceEpoch,
      };

      final qrPainter = QrPainter(
        data: jsonEncode(qrData),
        version: QrVersions.auto,
        errorCorrectionLevel: QrErrorCorrectLevel.H,
        eyeStyle: const QrEyeStyle(
          eyeShape: QrEyeShape.square,
          color: Color(0xFF000000),
        ),
        dataModuleStyle: const QrDataModuleStyle(
          dataModuleShape: QrDataModuleShape.square,
          color: Color(0xFF000000),
        ),
      );

      final qrImageData = await qrPainter.toImageData(200);

      if (qrImageData == null) {
        throw Exception('Falha ao gerar imagem do QR Code');
      }

      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Center(
              child: pw.Column(
                mainAxisAlignment: pw.MainAxisAlignment.center,
                children: [
                  pw.Text(
                    'QR Code de Atendimento',
                    style: pw.TextStyle(
                      fontSize: 24,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(height: 10),
                  pw.Text(
                    'Dr. ${medico['nome']}',
                    style: const pw.TextStyle(fontSize: 18),
                  ),
                  pw.Text(
                    medico['especialidade'],
                    style: const pw.TextStyle(fontSize: 16),
                  ),
                  pw.SizedBox(height: 20),
                  pw.Container(
                    width: 200,
                    height: 200,
                    child: pw.Image(
                      pw.MemoryImage(qrImageData.buffer.asUint8List()),
                    ),
                  ),
                  pw.SizedBox(height: 20),
                  pw.Text(
                    'Data de geração: ${BrazilDateFormat.formatDateTime(BrazilTimeZone.now())}',
                    style: const pw.TextStyle(fontSize: 14),
                  ),
                ],
              ),
            );
          },
        ),
      );

      final result = await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdf.save(),
        name: 'QR Code - Dr. ${medico['nome']}',
      );

      if (result) {
        await _atualizarStatusImpressao(qrCode);

        final logImpressao = ParseObject('QRCodeImpressaoLog')
          ..set('qr_code', qrCode)
          ..set('data_impressao', DateTime.now())
          ..set('medico_id', medico['id']);
        await logImpressao.save();

        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('QR Code impresso com sucesso!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Erro ao imprimir QR Code: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao imprimir QR Code: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _regenerarQRCode(Map<String, dynamic> medico) async {
    Navigator.pop(context);
    await _gerarNovoQRCode(medico);
  }

  void _abrirFila(Map<String, dynamic> medico) {
    Get.toNamed(
      '/fila_atendimento',
      arguments: {
        'id': medico['id'],
        'nome': medico['nome'],
        'especialidade': medico['especialidade'],
        'crm': medico['crm'],
        'vinculoId': medico['vinculoId'],
      },
    )?.then((_) => _carregarMedicos());
  }

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final availableHeight = mediaQuery.size.height - mediaQuery.padding.top;

    return Scaffold(
      body: GradientBackground(
        child: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              SizedBox(height: availableHeight * 0.02),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.arrow_back, color: Colors.black),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                    const Expanded(
                      child: Text(
                        'Filas ativas',
                        style: TextStyle(
                          fontFamily: 'Georgia',
                          fontSize: 32,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(width: 48),
                  ],
                ),
              ),
              SizedBox(height: availableHeight * 0.03),
              Expanded(
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _medicos.isEmpty
                        ? const Center(
                            child: Text(
                              'Nenhum médico vinculado encontrado',
                              style: TextStyle(
                                fontFamily: 'Georgia',
                                fontSize: 18,
                              ),
                            ),
                          )
                        : ListView.builder(
                            padding: const EdgeInsets.symmetric(horizontal: 20),
                            itemCount: _medicos.length,
                            itemBuilder: (context, index) {
                              final medico = _medicos[index];
                              return _buildMedicoCard(medico);
                            },
                          ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class NotificationIcon extends StatelessWidget {
  final String medicoId;
  final VoidCallback onTap;

  const NotificationIcon({
    super.key,
    required this.medicoId,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final notificationController = Get.find<NotificationController>();

    return Obx(() {
      final count = notificationController.notificacoes[medicoId] ?? 0;
      final isAnimating = notificationController.isAnimating.value;

      return Stack(
        clipBehavior: Clip.none,
        children: [
          TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 300),
            tween: Tween(begin: 1.0, end: isAnimating ? 1.2 : 1.0),
            builder: (context, value, child) {
              return Transform.scale(
                scale: value,
                child: Transform.rotate(
                  angle: isAnimating ? 0.1 : 0.0,
                  child: IconButton(
                    icon: const Icon(
                      Icons.notifications_outlined,
                      size: 28,
                    ),
                    onPressed: onTap,
                  ),
                ),
              );
            },
          ),
          if (count > 0)
            Positioned(
              right: 0,
              top: 0,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                constraints: const BoxConstraints(
                  minWidth: 16,
                  minHeight: 16,
                ),
                child: Text(
                  count > 99 ? '99+' : count.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
        ],
      );
    });
  }
}
