// lib/controllers/user_data_controller.dart
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';
import 'package:uuid/uuid.dart';
import 'package:device_info_plus/device_info_plus.dart';
import '../services/device_manager_service.dart';
import '../utils/date_utils.dart';

class UserDataController extends GetxController {
  final nomeController = TextEditingController();
  final telefoneController = TextEditingController();
  final RxBool isLoading = false.obs;
  final RxBool userDataExists = false.obs;
  final RxString formattedUserId = ''.obs;

  static const String _userDataKey = 'userData';
  static const String _userSessionKey = 'userSession';
  static const String _deviceIdKey = 'device_unique_id';

  final RxString deviceId = ''.obs;

  @override
  void onInit() {
    super.onInit();
    checkUserData();
  }

  @override
  void onClose() {
    nomeController.dispose();
    telefoneController.dispose();
    super.onClose();
  }

  Future<void> checkUserData() async {
    try {
      isLoading.value = true;
      debugPrint(
          '🔍 === VERIFICAÇÃO INTELIGENTE DE USUÁRIO (PÓS-REINSTALAÇÃO) ===');

      // ═══════════════════════════════════════════════════════════════════
      // ETAPA 1: GERAR DEVICE ID (Secure Storage + Keychain + Hardware)
      // ═══════════════════════════════════════════════════════════════════

      debugPrint('🔍 Etapa 1: Gerando Device ID persistente...');
      final deviceManager = DeviceManagerService.instance;
      final currentDeviceId = await deviceManager.getDeviceFingerprint();

      debugPrint('🔑 Device ID obtido: $currentDeviceId');

      // ═══════════════════════════════════════════════════════════════════
      // ETAPA 2: BUSCAR NO SERVIDOR POR DEVICE ID EXISTENTE
      // ═══════════════════════════════════════════════════════════════════

      debugPrint(
          '🔍 Etapa 2: Verificando se dispositivo já está registrado...');
      final existingByDevice = await _findPatientByDeviceId(currentDeviceId);

      if (existingByDevice != null) {
        debugPrint(
            '✅ Paciente encontrado por Device ID: ${existingByDevice.objectId}');
        await _updateLocalDataFromServer(existingByDevice);
        deviceId.value = currentDeviceId;
        isLoading.value = false;
        return;
      }

      // ═══════════════════════════════════════════════════════════════════
      // ETAPA 3: BUSCAR POR DADOS LOCAIS PERSISTENTES
      // ═══════════════════════════════════════════════════════════════════

      debugPrint('🔍 Etapa 3: Buscando por dados locais persistentes...');

      // Verificar se há dados locais (podem sobreviver à reinstalação em alguns casos)
      final localUserData = await getUserData();
      String? knownPhone = localUserData?['telefone'];
      String? knownUserId = localUserData?['userId'];

      // ═══════════════════════════════════════════════════════════════════
      // ETAPA 4: BUSCAR NO SERVIDOR POR DADOS CONHECIDOS
      // ═══════════════════════════════════════════════════════════════════

      ParseObject? existingPatient;

      // 4.1 Buscar por telefone se temos um local
      if (knownPhone != null && knownPhone.isNotEmpty) {
        debugPrint('🔍 Buscando por telefone conhecido: $knownPhone');
        existingPatient = await _findPatientByPhone(knownPhone);
      }

      // 4.2 Buscar por userId se não encontrou por telefone
      if (existingPatient == null &&
          knownUserId != null &&
          knownUserId.isNotEmpty) {
        debugPrint('🔍 Buscando por userId conhecido: $knownUserId');
        existingPatient = await _findPatientByUserId(knownUserId);
      }

      // 4.3 Buscar pacientes órfãos com dados similares (estratégia de recuperação)
      if (existingPatient == null) {
        debugPrint('🔍 Buscando pacientes órfãos para recuperação...');
        existingPatient = await _findOrphanedPatients(currentDeviceId);
      }

      // ═══════════════════════════════════════════════════════════════════
      // ETAPA 5: SE ENCONTROU USUÁRIO, ATUALIZAR SEU DEVICE ID
      // ═══════════════════════════════════════════════════════════════════

      if (existingPatient != null) {
        debugPrint(
            '✅ Usuário existente encontrado: ${existingPatient.objectId}');
        debugPrint('🔄 Vinculando este dispositivo ao usuário existente...');

        // Atualizar device ID do usuário existente
        existingPatient.set('deviceId', currentDeviceId);
        existingPatient.set('ultimoAcesso', TimezoneUtils.getNowBrasil());

        // Atualizar informações do dispositivo
        final deviceInfo = await deviceManager.getDeviceInfo();
        existingPatient.set('deviceInfo', {
          'platform': Platform.isIOS ? 'iOS' : 'Android',
          'model': deviceInfo['model'] ?? 'Desconhecido',
          'version': deviceInfo['systemVersion'] ?? 'Desconhecido',
          'deviceId': currentDeviceId,
          'lastRecovery': TimezoneUtils.getNowBrasil(),
        });

        await existingPatient.save();
        debugPrint('✅ Dispositivo vinculado ao usuário existente');

        // Atualizar dados locais
        await _updateLocalDataFromServer(existingPatient);
        deviceId.value = currentDeviceId;

        isLoading.value = false;
        return;
      }

      // ═══════════════════════════════════════════════════════════════════
      // ETAPA 6: NOVO USUÁRIO - Configurar Device ID para cadastro futuro
      // ═══════════════════════════════════════════════════════════════════

      debugPrint('🆕 Nenhum usuário encontrado - novo dispositivo');
      deviceId.value = currentDeviceId;
        userDataExists.value = false;

      nomeController.text = '';
      telefoneController.text = '';
      formattedUserId.value = '';

      debugPrint('✅ Device ID configurado para novo usuário: $currentDeviceId');
    } catch (e) {
      debugPrint('❌ Erro em checkUserData: $e');

      // Fallback para novo usuário em caso de erro
      deviceId.value = 'DEV-ERROR-${DateTime.now().millisecondsSinceEpoch}';
      userDataExists.value = false;
    } finally {
      isLoading.value = false;
    }
  }

  /// Busca pacientes órfãos que podem pertencer a este dispositivo
  Future<ParseObject?> _findOrphanedPatients(String currentDeviceId) async {
    try {
      // Buscar pacientes com deviceId vazio ou similar ao atual
      final query = QueryBuilder<ParseObject>(ParseObject('Paciente'))
        ..whereLessThan(
            'ultimoAcesso',
            DateTime.now().subtract(
                const Duration(days: 30))) // Último acesso há mais de 30 dias
        ..orderByDescending('ultimoAcesso')
        ..setLimit(5);

      final response = await query.query();

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        // Analizar se algum paciente pode ser o mesmo usuário
        for (final patient in response.results!) {
          final deviceInfo = patient.get<Map<String, dynamic>>('deviceInfo');
          if (deviceInfo != null) {
            final platform = deviceInfo['platform'];
            final currentPlatform = Platform.isIOS ? 'iOS' : 'Android';

            // Se for a mesma plataforma, pode ser o mesmo usuário
            if (platform == currentPlatform) {
              debugPrint('🔍 Paciente órfão encontrado: ${patient.objectId}');
              return patient;
            }
          }
        }
      }

      return null;
    } catch (e) {
      debugPrint('❌ Erro ao buscar pacientes órfãos: $e');
      return null;
    }
  }

  /// Busca paciente por Device ID
  Future<ParseObject?> _findPatientByDeviceId(String deviceId) async {
    try {
      final query = QueryBuilder<ParseObject>(ParseObject('Paciente'))
        ..whereEqualTo('deviceId', deviceId)
        ..orderByDescending('ultimoAcesso')
        ..setLimit(1);

      final response = await query.query();

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        return response.results!.first;
      }
      return null;
    } catch (e) {
      debugPrint('❌ Erro ao buscar paciente por Device ID: $e');
      return null;
    }
  }

  /// Valida dados locais com o servidor
  Future<void> _validateLocalDataWithServer(
      Map<String, dynamic> localData) async {
    try {
      final currentDeviceId = localData['deviceId'];
      if (currentDeviceId == null) return;

      // Verificar se ainda existe no servidor
      final query = QueryBuilder<ParseObject>(ParseObject('Paciente'))
        ..whereEqualTo('deviceId', currentDeviceId)
        ..setLimit(1);

      final response = await query.query();

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        // Atualizar último acesso
        _updateLastAccess();
        debugPrint('✅ Dados locais validados com o servidor');
      } else {
        debugPrint(
            '⚠️ Device ID local não encontrado no servidor, pode ter sido removido');
        // Os dados locais ainda são válidos, apenas não estão sincronizados
      }
    } catch (e) {
      debugPrint('⚠️ Erro ao validar dados locais: $e');
      // Não é crítico, continua com dados locais
    }
  }

  /// Procura paciente por fingerprint de hardware
  Future<ParseObject?> _findPatientByHardwareFingerprint(
      String fingerprint) async {
    try {
      final query = QueryBuilder<ParseObject>(ParseObject('Paciente'))
        ..whereEqualTo('deviceId', fingerprint)
        ..orderByDescending('ultimoAcesso')
        ..setLimit(1);

      final response = await query.query();

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        return response.results!.first;
      }

      return null;
    } catch (e) {
      debugPrint('❌ Erro ao buscar por fingerprint: $e');
      return null;
    }
  }

  /// Procura paciente por telefone
  Future<ParseObject?> _findPatientByPhone(String telefone) async {
    try {
      final cleanPhone = telefone.replaceAll(RegExp(r'[^0-9]'), '');

      final query = QueryBuilder<ParseObject>(ParseObject('Paciente'))
        ..whereEqualTo('telefone', cleanPhone)
        ..orderByDescending('ultimoAcesso')
        ..setLimit(1);

      final response = await query.query();

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        return response.results!.first;
      }

      return null;
    } catch (e) {
      debugPrint('❌ Erro ao buscar por telefone: $e');
      return null;
    }
  }

  /// Procura paciente por userId
  Future<ParseObject?> _findPatientByUserId(String userId) async {
    try {
      final query = QueryBuilder<ParseObject>(ParseObject('Paciente'))
        ..whereEqualTo('userId', userId);

      final response = await query.query();

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        return response.results!.first;
      }
      return null;
    } catch (e) {
      debugPrint('❌ Erro ao buscar paciente por userId: $e');
      return null;
    }
  }

  /// Atualiza o device ID de um paciente existente
  Future<void> _updatePatientDeviceId(
      ParseObject patient, String newDeviceId) async {
    try {
      patient.set('deviceId', newDeviceId);
      patient.set('ultimoAcesso', DateTime.now());

      await patient.save();
      debugPrint('✅ Device ID atualizado no servidor');
    } catch (e) {
      debugPrint('❌ Erro ao atualizar device ID: $e');
    }
  }

  // Método para atualizar o horário do último acesso
  Future<void> _updateLastAccess() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userData = prefs.getString(_userDataKey);

      if (userData != null) {
        final Map<String, dynamic> userDataMap = json.decode(userData);
        // Atualizar apenas o último acesso
        userDataMap['ultimoAcesso'] = TimezoneUtils.nowToIso8601StringBrasil();

        // Salvar novamente
        await prefs.setString(_userDataKey, json.encode(userDataMap));

        // Atualizar no servidor também
        await _updateLastAccessInCloud(userDataMap);
      }
    } catch (e) {
      debugPrint('Erro ao atualizar último acesso: $e');
    }
  }

  // Método para atualizar o último acesso no servidor
  Future<void> _updateLastAccessInCloud(Map<String, dynamic> userData) async {
    try {
      final userId = userData['userId'];
      if (userId == null) return;

      // Verificar se existe um paciente com esse ID
      final queryPaciente = QueryBuilder<ParseObject>(ParseObject('Paciente'))
        ..whereEqualTo('userId', userId);

      final pacienteResponse = await queryPaciente.query();

      if (pacienteResponse.success &&
          pacienteResponse.results != null &&
          pacienteResponse.results!.isNotEmpty) {
        // Atualizar paciente existente
        final paciente = pacienteResponse.results!.first;
        paciente.set('ultimoAcesso',
            TimezoneUtils.parseIso8601Brasil(userData['ultimoAcesso']));
        await paciente.save();
      }
    } catch (e) {
      // Apenas registrar o erro, mas não falhar
      debugPrint('Erro ao atualizar último acesso no servidor: $e');
    }
  }

  Future<bool> saveUserData(String nome, String telefone,
      {bool isEditing = false}) async {
    try {
      isLoading.value = true;

      if (nome.isEmpty || telefone.isEmpty) {
        return false;
      }

      final prefs = await SharedPreferences.getInstance();

      String? userId;

      // Se estiver editando ou se for a primeira vez, verificar se já existe um ID
      final existingData = await getUserData();
      if (existingData != null && existingData.containsKey('userId')) {
        userId = existingData['userId'];
      } else {
        // Gerar um novo userId
        userId = await _generateUserId();
      }

      // Verificar se já existe um usuário com esse telefone
      if (!isEditing) {
        final queryTelefone = QueryBuilder<ParseObject>(ParseObject('Paciente'))
          ..whereEqualTo(
              'telefone', telefone.replaceAll(RegExp(r'[^0-9]'), ''));

        final telefoneResponse = await queryTelefone.query();

        if (telefoneResponse.success &&
            telefoneResponse.results != null &&
            telefoneResponse.results!.isNotEmpty) {
          // Se encontrar, usar o userId existente
          final pacienteExistente = telefoneResponse.results!.first;
          userId = pacienteExistente.get<String>('userId') ?? userId;
        }
      }

      // Dados completos do usuário
      final userData = {
        'nome': nome,
        'telefone': telefone,
        'userId': userId,
        'deviceId': deviceId.value,
        'dataCadastro': isEditing
            ? (existingData != null && existingData.containsKey('dataCadastro')
                ? existingData['dataCadastro']
                : TimezoneUtils.nowToIso8601StringBrasil())
            : TimezoneUtils.nowToIso8601StringBrasil(),
        'ultimoAcesso': TimezoneUtils.nowToIso8601StringBrasil(),
      };

      // Salvar no dispositivo
      await prefs.setString(_userDataKey, json.encode(userData));

      // Salvar também um flag de sessão ativa para maior persistência
      await prefs.setString(_userSessionKey, "active");

      // Atualizar na nuvem também (se já existir um userId)
      await _syncUserDataToCloud(userData, isEditing);

      // Atualizar estado local
      nomeController.text = nome;
      telefoneController.text = telefone;
      formattedUserId.value = userId ?? '';
      userDataExists.value = true;

      return true;
    } catch (e) {
      debugPrint('Erro ao salvar dados: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Sincronizar dados do usuário com o Parse Server
  Future<void> _syncUserDataToCloud(
      Map<String, dynamic> userData, bool isEditing) async {
    try {
      final userId = userData['userId'];

      // Usar apenas a classe Paciente (migração completa)
      final queryPaciente = QueryBuilder<ParseObject>(ParseObject('Paciente'))
        ..whereEqualTo('userId', userId);

      final pacienteResponse = await queryPaciente.query();

      late ParseObject paciente;

      final bool hasExistingPatient = pacienteResponse.success &&
          pacienteResponse.results != null &&
          pacienteResponse.results!.isNotEmpty;

      if (hasExistingPatient) {
        // Atualizar paciente existente
        paciente = pacienteResponse.results!.first;
        debugPrint('Paciente existente encontrado: ${paciente.objectId}');
      } else {
        // Criar novo paciente
        paciente = ParseObject('Paciente');
        paciente.set('userId', userId);
        paciente.set('dataCadastro', DateTime.parse(userData['dataCadastro']));
        debugPrint('Criando novo paciente com userId: $userId');
      }

      // Atualizar dados do paciente
      paciente.set('nome', userData['nome']);
      paciente.set(
          'telefone', userData['telefone'].replaceAll(RegExp(r'[^0-9]'), ''));
      paciente.set('ultimoAcesso', DateTime.parse(userData['ultimoAcesso']));
      paciente.set('deviceId', userData['deviceId']);

      // Definir ACL pública para facilitar acesso
      final acl = ParseACL();
      acl.setPublicReadAccess(allowed: true);
      acl.setPublicWriteAccess(allowed: true);
      paciente.setACL(acl);

      // Obter informações robustas do dispositivo
      try {
        final deviceInfo = await getDeviceInfo();
        if (deviceInfo.isNotEmpty) {
          // Criar um resumo das informações do dispositivo para salvar
          final deviceSummary = {
            'platform': Platform.isIOS ? 'iOS' : 'Android',
            'model': deviceInfo['model'] ?? 'Desconhecido',
            'version': deviceInfo['systemName'] != null &&
                    deviceInfo['systemVersion'] != null
                ? '${deviceInfo['systemName']} ${deviceInfo['systemVersion']}'
                : 'Desconhecido',
            'deviceId': deviceId.value,
          };

        // Armazenar informações do dispositivo no campo deviceInfo
          paciente.set('deviceInfo', deviceSummary);
          debugPrint('Informações robustas do dispositivo salvas');
        }
      } catch (e) {
        debugPrint('Erro ao obter informações do dispositivo: $e');
        // Continuar mesmo se falhar
      }

      // Salvar paciente
      final saveResponse = await paciente.save();
      if (saveResponse.success) {
        debugPrint('Paciente salvo com sucesso: ${paciente.objectId}');
      } else {
        debugPrint('Erro ao salvar paciente: ${saveResponse.error?.message}');
        throw Exception('Erro ao salvar dados na nuvem');
      }
    } catch (e) {
      debugPrint('Erro ao sincronizar dados com a nuvem: $e');
      // Não interromper o fluxo se falhar a sincronização
    }
  }

  Future<String> _generateUserId() async {
    // Gerando um ID universal único para o usuário
    const uuid = Uuid();
    final prefix = _generatePrefix();
    final uniqueId = uuid.v4().substring(0, 8);

    return 'P-$prefix-$uniqueId';
  }

  String _generatePrefix() {
    final random = Random();
    final letters = [
      'A',
      'B',
      'C',
      'D',
      'E',
      'F',
      'G',
      'H',
      'J',
      'K',
      'L',
      'M',
      'N',
      'P',
      'Q',
      'R',
      'S',
      'T',
      'U',
      'V',
      'W',
      'X',
      'Y',
      'Z'
    ];

    // Pegando 2 letras aleatórias, evitando confusão com números
    final letter1 = letters[random.nextInt(letters.length)];
    final letter2 = letters[random.nextInt(letters.length)];

    // Gerando 3 números aleatórios
    final numbers = random.nextInt(900) + 100; // Ensures 3 digits (100-999)

    return '$letter1$letter2$numbers';
  }

  Future<Map<String, dynamic>?> getUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userData = prefs.getString(_userDataKey);

      // Verificar também o flag de sessão
      final sessionActive = prefs.getString(_userSessionKey);

      if (userData != null && sessionActive != null) {
        return json.decode(userData);
      } else if (userData != null && sessionActive == null) {
        // Restaurar a sessão se os dados existem mas o flag não
        await prefs.setString(_userSessionKey, "active");
        return json.decode(userData);
      }
      return null;
    } catch (e) {
      debugPrint('Erro ao obter dados do usuário: $e');
      return null;
    }
  }

  // Limpar os dados do usuário ao fazer logout
  Future<void> clearUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userSessionKey);
      // Não removemos o _userDataKey para manter o cache, apenas desativamos a sessão

      nomeController.text = '';
      telefoneController.text = '';
      formattedUserId.value = '';
      userDataExists.value = false;

      debugPrint('Dados da sessão do usuário limpos com sucesso');
    } catch (e) {
      debugPrint('Erro ao limpar dados do usuário: $e');
    }
  }

  String? validateNome(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Por favor, digite seu nome';
    }
    if (value.trim().length < 2) {
      return 'O nome deve ter pelo menos 2 caracteres';
    }
    return null;
  }

  String? validateTelefone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Por favor, digite seu telefone';
    }

    // Se o valor começa com '+', é um número internacional
    if (value.startsWith('+')) {
      // Para números internacionais, ser mais flexível
      final cleanPhone = value.replaceAll(RegExp(r'[^0-9]'), '');
      if (cleanPhone.length < 8) {
        return 'Digite um número de telefone válido';
      }
      return null;
    }

    // Para números nacionais
    final cleanPhone = value.replaceAll(RegExp(r'[^0-9]'), '');
    if (cleanPhone.length < 10 || cleanPhone.length > 11) {
      return 'Digite um número de telefone válido (10 ou 11 dígitos)';
    }

    // Verificação de DDD menos restritiva
    if (cleanPhone.length >= 2 &&
        !RegExp(r'^[1-9][0-9]').hasMatch(cleanPhone.substring(0, 2))) {
      return 'O telefone deve iniciar com um DDD válido';
    }

    return null;
  }

  Future<bool> updateUserData(String nome, String telefone) async {
    // This is basically a convenience method that calls saveUserData with isEditing=true
    return saveUserData(nome, telefone, isEditing: true);
  }

  /// Obtém informações do dispositivo através do DeviceManagerService
  Future<Map<String, dynamic>> getDeviceInfo() async {
    try {
      final deviceManager = DeviceManagerService.instance;
      return await deviceManager.getDeviceInfo();
    } catch (e) {
      debugPrint('Erro ao obter informações do dispositivo: $e');
      return {};
    }
  }

  /// Verifica se existe um paciente associado a este dispositivo
  Future<void> _checkPatientByDevice() async {
    try {
      if (deviceId.value.isEmpty) return;

      // Buscar paciente pelo deviceId
      final query = QueryBuilder<ParseObject>(ParseObject('Paciente'))
        ..whereEqualTo('deviceId', deviceId.value)
        ..orderByDescending('ultimoAcesso')
        ..setLimit(1);

      final response = await query.query();

      if (response.success &&
          response.results != null &&
          response.results!.isNotEmpty) {
        final paciente = response.results!.first;
        final pacienteNome = paciente.get<String>('nome');
        final pacienteTelefone = paciente.get<String>('telefone');
        final pacienteUserId = paciente.get<String>('userId');

        debugPrint('Paciente encontrado para este dispositivo: $pacienteNome');

        // Se encontrou um paciente mas não temos dados locais,
        // ou se os dados são diferentes, atualizar
        final currentData = await getUserData();

        if (currentData == null ||
            currentData['userId'] != pacienteUserId ||
            currentData['nome'] != pacienteNome ||
            currentData['telefone'] != pacienteTelefone) {
          // Atualizar dados locais com os dados do servidor
          await _updateLocalDataFromServer(paciente);
        }
      }
    } catch (e) {
      debugPrint('Erro ao verificar paciente por dispositivo: $e');
    }
  }

  /// Atualiza os dados locais com dados do servidor
  Future<void> _updateLocalDataFromServer(ParseObject paciente) async {
    try {
      debugPrint('Atualizando dados locais com dados do servidor...');

      final prefs = await SharedPreferences.getInstance();

      final userData = {
        'nome': paciente.get<String>('nome') ?? '',
        'telefone': paciente.get<String>('telefone') ?? '',
        'userId': paciente.get<String>('userId') ?? '',
        'dataCadastro':
            paciente.get<DateTime>('dataCadastro')?.toIso8601String() ??
                DateTime.now().toIso8601String(),
        'ultimoAcesso': TimezoneUtils.nowToIso8601StringBrasil(),
        'deviceId': deviceId.value,
      };

      // Salvar dados locais
      await prefs.setString(_userDataKey, json.encode(userData));
      await prefs.setString(_userSessionKey, "active");

      // Atualizar estado do controller
      nomeController.text = userData['nome']!;
      telefoneController.text = userData['telefone']!;
      formattedUserId.value = userData['userId']!;
      userDataExists.value = true;

      debugPrint('Dados locais atualizados com sucesso');
    } catch (e) {
      debugPrint('Erro ao atualizar dados locais: $e');
    }
  }

  /// Recupera dados do usuário manualmente baseado em telefone
  Future<bool> recoverUserDataByPhone(String telefone) async {
    try {
      isLoading.value = true;
      debugPrint('🔍 Tentando recuperar dados por telefone: $telefone');

      final cleanPhone = telefone.replaceAll(RegExp(r'[^0-9]'), '');
      if (cleanPhone.length < 10) {
        debugPrint('❌ Telefone muito curto: $cleanPhone');
        return false;
      }

      // Buscar paciente por telefone
      final existingPatient = await _findPatientByPhone(cleanPhone);

      if (existingPatient != null) {
        debugPrint(
            '✅ Paciente encontrado por telefone: ${existingPatient.objectId}');

        // Atualizar deviceId do paciente encontrado
        final deviceManager = DeviceManagerService.instance;
        final currentDeviceId = await deviceManager.getDeviceFingerprint();

        existingPatient.set('deviceId', currentDeviceId);
        existingPatient.set('ultimoAcesso', TimezoneUtils.getNowBrasil());

        // Atualizar informações do dispositivo
        final deviceInfo = await deviceManager.getDeviceInfo();
        existingPatient.set('deviceInfo', {
          'platform': Platform.isIOS ? 'iOS' : 'Android',
          'model': deviceInfo['model'] ?? 'Desconhecido',
          'version': deviceInfo['systemVersion'] ?? 'Desconhecido',
          'deviceId': currentDeviceId,
          'manualRecovery': TimezoneUtils.getNowBrasil(),
        });

        await existingPatient.save();
        debugPrint('✅ Dispositivo vinculado ao usuário recuperado');

        // Atualizar dados locais
        await _updateLocalDataFromServer(existingPatient);
        deviceId.value = currentDeviceId;

        return true;
      } else {
        debugPrint('❌ Nenhum paciente encontrado com este telefone');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Erro ao recuperar dados por telefone: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// Método para limpar completamente os dados e começar do zero
  Future<void> resetUserData() async {
    try {
      debugPrint('🧹 Limpando todos os dados do usuário...');

      // Limpar dados locais
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userDataKey);
      await prefs.remove(_userSessionKey);

      // Limpar device ID
      final deviceManager = DeviceManagerService.instance;
      await deviceManager.clearDeviceId();

      // Resetar estado
      nomeController.text = '';
      telefoneController.text = '';
      formattedUserId.value = '';
      userDataExists.value = false;
      deviceId.value = '';

      debugPrint('✅ Dados limpos com sucesso');
    } catch (e) {
      debugPrint('❌ Erro ao limpar dados: $e');
    }
  }
}
