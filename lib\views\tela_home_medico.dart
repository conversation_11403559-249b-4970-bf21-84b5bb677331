import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fila_app/views/gradient_background.dart';
import '../controllers/login_controller.dart';
import '../widgets/confirm_dialog.dart';

class TelaMedicoScreen extends StatelessWidget {
  const TelaMedicoScreen({super.key});

  Future<void> _showLogoutConfirmation(BuildContext context) async {
    ConfirmDialog.show(
      context: context,
      title: '<PERSON><PERSON> da sessão médico',
      message: 'Deseja realmente sair da sua conta?',
      confirmButtonText: 'Sim, sair',
      cancelButtonText: 'Cancelar',
      onConfirm: () => _handleLogout(context),
    );
  }

  Future<void> _handleLogout(BuildContext context) async {
    try {
      final loginController = LoginController();
      await loginController.logout();

      if (!context.mounted) return;

      Navigator.of(context).pushNamedAndRemoveUntil(
        '/login',
        (Route<dynamic> route) => false,
      );
    } catch (e) {
      debugPrint('Erro ao fazer logout: $e');
      if (!context.mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Erro ao fazer logout. Tente novamente.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientBackground(
        child: LayoutBuilder(
          builder: (context, constraints) {
            final larguraTela = constraints.maxWidth;            return Column(
              children: [
                const SizedBox(height: 50),
                Row(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: IconButton(
                        icon: Icon(Icons.arrow_back, size: 40),
                        onPressed: () => _showLogoutConfirmation(context),
                      ),
                    ),
                    const Expanded(
                      child: Text(
                        'Médico',
                        style: TextStyle(
                          fontFamily: 'Georgia',
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(width: 40),
                  ],
                ),                Expanded(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        _buildOptionButton(
                          larguraTela: larguraTela,
                          label: 'Perfil',
                          iconPath: 'assets/perfil.png',
                          onTap: () {
                            Get.toNamed('/perfilMedico');
                          },
                        ),
                        const SizedBox(height: 50),
                        _buildOptionButton(
                          larguraTela: larguraTela,
                          label: 'Habilitar Hospitais',
                          iconPath: 'assets/settings.png',
                          onTap: () {
                            Get.toNamed('/hospitais_habilitados');
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildOptionButton({
    required double larguraTela,
    required String label,
    required String iconPath,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: larguraTela * 0.7,
        child: Column(
          children: [
            Image.asset(
              iconPath,
              width: larguraTela * 0.25,
              height: larguraTela * 0.25,
            ),
            const SizedBox(height: 15),
            Text(
              label,
              overflow: TextOverflow.visible,
              softWrap: false,
              style: const TextStyle(
                fontFamily: 'Georgia',
                fontSize: 22,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
