import 'package:flutter/material.dart';

/// Widget de RefreshIndicator simples com throttling básico
/// Implementa pull-to-refresh com proteção contra refresh muito frequente
class ThrottledRefreshIndicator extends StatefulWidget {
  /// ID único da tela (usado para rastrear atualizações)
  final String screenId;

  /// Função a ser executada quando o usuário puxa para atualizar
  final Future<void> Function() onRefresh;

  /// Conteúdo a ser exibido dentro do indicador de atualização
  final Widget child;

  /// Cor do indicador de atualização
  final Color? color;

  /// Cor de fundo do indicador de atualização
  final Color? backgroundColor;

  /// Deslocamento do indicador de atualização
  final double displacement;

  /// Tamanho do indicador de atualização
  final double? edgeOffset;

  const ThrottledRefreshIndicator({
    Key? key,
    required this.screenId,
    required this.onRefresh,
    required this.child,
    this.color,
    this.backgroundColor,
    this.displacement = 40.0,
    this.edgeOffset,
  }) : super(key: key);

  @override
  State<ThrottledRefreshIndicator> createState() =>
      _ThrottledRefreshIndicatorState();
}

class _ThrottledRefreshIndicatorState extends State<ThrottledRefreshIndicator> {
  DateTime? _lastRefresh;
  static const Duration _throttleDuration = Duration(seconds: 2);

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        final now = DateTime.now();

        // Verificar se o último refresh foi muito recente
        if (_lastRefresh != null &&
            now.difference(_lastRefresh!) < _throttleDuration) {
          // Mostrar feedback se a requisição foi muito frequente
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Aguarde um momento antes de atualizar novamente'),
              duration: Duration(seconds: 2),
              behavior: SnackBarBehavior.floating,
            ),
          );
          return;
        }

        _lastRefresh = now;
        await widget.onRefresh();
      },
      color: widget.color,
      backgroundColor: widget.backgroundColor,
      displacement: widget.displacement,
      edgeOffset: widget.edgeOffset ?? 0.0,
      child: widget.child,
    );
  }
}
