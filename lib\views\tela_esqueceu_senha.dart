import 'package:flutter/material.dart';
import 'dart:ui';
import '../controllers/password_recovery_controller.dart';
import 'package:fila_app/views/gradient_background.dart';
import 'package:fila_app/theme/theme.dart';
import 'package:get/get.dart';
import 'package:flutter/services.dart';

class EsqueceuSenha extends StatefulWidget {
  const EsqueceuSenha({super.key});

  @override
  EsqueceuSenhaState createState() => EsqueceuSenhaState();
}

class EsqueceuSenhaState extends State<EsqueceuSenha>
    with TickerProviderStateMixin {
  final TextEditingController emailController = TextEditingController();
  final _passwordController = PasswordRecoveryController();
  final _formKey = GlobalKey<FormState>();
  final FocusNode _emailFocusNode = FocusNode();

  bool _isLoading = false;
  bool _isEmailEmpty = true;

  // Controladores de animação
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _pulseController;

  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();

    // Inicializar controladores de animação
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    // Configurar animações
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Iniciar animações
    _fadeController.forward();
    _slideController.forward();
    _pulseController.repeat(reverse: true);

    // Add listener to email field to update button state
    emailController.addListener(_updateEmailState);
  }

  void _updateEmailState() {
    setState(() {
      _isEmailEmpty = emailController.text.trim().isEmpty;
    });
  }

  @override
  void dispose() {
    emailController.removeListener(_updateEmailState);
    emailController.dispose();
    _emailFocusNode.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Por favor, insira um e-mail';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return 'Por favor, insira um e-mail válido';
    }
    return null;
  }

  Future<void> _handlePasswordReset() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    // Provide haptic feedback
    HapticFeedback.mediumImpact();

    try {
      final result = await _passwordController
          .sendPasswordResetEmail(emailController.text.trim());

      if (!mounted) return;

      if (result['success']) {
        await _showSuccessDialog();
      } else {
        _showErrorDialog(result['error']);
      }
    } catch (e) {
      _showErrorDialog('Ocorreu um erro ao processar sua solicitação');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _showSuccessDialog() {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: const Column(
            children: [
              Icon(
                Icons.check_circle_outline,
                color: Color(0xFF34ECCB),
                size: 64,
              ),
              SizedBox(height: 16),
              Text(
                'E-mail Enviado',
                style: TextStyle(
                  fontFamily: 'Georgia',
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          content: const Text(
            'Um e-mail com um link para redefinir sua senha foi enviado. Por favor, verifique sua caixa de entrada e acesse o link para criar uma nova senha. O link é válido por 24 horas.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontFamily: 'Georgia',
              color: Colors.black87,
            ),
          ),
          actions: [
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF34ECCB), Color(0xFF75CBBB)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(25),
              ),
              child: TextButton(
              onPressed: () {
                Get.back();
                Get.toNamed('/login');
              },
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                ),
              child: const Text(
                'Voltar para Login',
                style: TextStyle(
                    color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                    fontFamily: 'Georgia',
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: const Column(
            children: [
              Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 64,
              ),
              SizedBox(height: 16),
              Text(
                'Erro',
                style: TextStyle(
                  fontFamily: 'Georgia',
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          content: Text(
            message,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontFamily: 'Georgia',
              color: Colors.black87,
            ),
          ),
          actions: [
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF34ECCB), Color(0xFF75CBBB)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(25),
              ),
              child: TextButton(
              onPressed: () => Get.back(),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                ),
              child: const Text(
                'OK',
                style: TextStyle(
                    color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                    fontFamily: 'Georgia',
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: GradientBackground(
        child: SafeArea(
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: SizedBox(
              height: MediaQuery.of(context).size.height -
                  MediaQuery.of(context).padding.top,
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                        child: Column(
                          children: [
                      const SizedBox(height: 60),

                      // Título da tela
                            const Text(
                        'Recuperar Senha',
                              style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                                fontFamily: 'Georgia',
                          shadows: [
                            Shadow(
                              offset: Offset(0, 2),
                              blurRadius: 4,
                              color: Colors.black26,
                              ),
                          ],
                            ),
                      ),

                      const SizedBox(height: 8),

                      // Subtítulo
                      const Text(
                        'Digite seu e-mail para receber instruções',
                        textAlign: TextAlign.center,
                                          style: TextStyle(
                          fontSize: 16,
                          color: Colors.white,
                                            fontFamily: 'Georgia',
                          fontWeight: FontWeight.w500,
                          shadows: [
                            Shadow(
                              offset: Offset(0, 1),
                              blurRadius: 3,
                              color: Colors.black26,
                                          ),
                          ],
                                        ),
                                      ),

                      const SizedBox(height: 50),

                      // Card principal limpo
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          color: Colors.white,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 20,
                              offset: const Offset(0, 10),
                            ),
                          ],
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(32),
                          child: Form(
                            key: _formKey,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                // Ícone e descrição
                                Icon(
                                  Icons.lock_reset,
                                  size: 56,
                                  color: AppTheme.primaryColor,
                                ),

                                const SizedBox(height: 20),

                                Text(
                                  'Insira seu e-mail cadastrado abaixo. Você receberá um link para redefinir sua senha.',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    color: AppTheme.textSecondaryColor,
                                    fontSize: 16,
                                    fontFamily: 'Georgia',
                                    height: 1.5,
                              ),
                                ),

                                const SizedBox(height: 30),

                                // Label do campo
                                Text(
                                  'E-mail',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: AppTheme.textSecondaryColor,
                                  ),
                                ),

                                const SizedBox(height: 8),

                                // Campo de e-mail
                                _buildEmailField(),

                                const SizedBox(height: 30),

                                // Botão de enviar
                                  _buildSubmitButton(),
                                ],
                              ),
                            ),
                        ),
                        ),

                      const Spacer(),

                      // Botão voltar
                      _buildBackButton(),

                      const SizedBox(height: 30),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmailField() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
      controller: emailController,
        focusNode: _emailFocusNode,
      keyboardType: TextInputType.emailAddress,
        textInputAction: TextInputAction.done,
        enabled: !_isLoading,
      style: const TextStyle(
        fontSize: 16,
          color: Color(0xFF2D3748),
          fontWeight: FontWeight.w500,
      ),
      decoration: InputDecoration(
          filled: true,
          fillColor: Colors.grey[50],
          hintText: 'Digite seu email',
          hintStyle: TextStyle(
            color: Colors.grey[400],
            fontWeight: FontWeight.w400,
          ),
          prefixIcon: Container(
            margin: const EdgeInsets.all(12),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.email_outlined,
              color: AppTheme.primaryColor,
              size: 20,
            ),
          ),
        suffixIcon: emailController.text.isNotEmpty
            ? IconButton(
                  icon: Icon(Icons.clear, color: Colors.grey[400]),
                onPressed: () {
                  emailController.clear();
                  setState(() {
                    _isEmailEmpty = true;
                  });
                },
              )
            : null,
          errorStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
        ),
        border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(
              color: AppTheme.primaryColor,
              width: 2,
            ),
        ),
        errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: const BorderSide(
              color: Colors.red,
              width: 2,
            ),
        ),
        focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: const BorderSide(
              color: Colors.red,
              width: 2,
            ),
        ),
          contentPadding: const EdgeInsets.symmetric(
            vertical: 16,
            horizontal: 16,
        ),
      ),
      validator: _validateEmail,
      onFieldSubmitted: (_) {
        if (!_isEmailEmpty && !_isLoading) {
          _handlePasswordReset();
        }
      },
      ),
    );
  }

  Widget _buildSubmitButton() {
    if (_isLoading) {
      return Container(
        height: 56,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              AppTheme.primaryColor.withOpacity(0.7),
              AppTheme.primaryDarkColor.withOpacity(0.7),
            ],
          ),
        ),
        child: const Center(
          child: SizedBox(
            width: 24,
            height: 24,
            child: CircularProgressIndicator(
              strokeWidth: 2.5,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ),
        ),
      );
    }

    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Container(
            height: 56,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                colors: [
                  AppTheme.primaryColor,
                  AppTheme.primaryDarkColor,
                ],
              ),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.primaryColor.withOpacity(0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 6),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(16),
              child: InkWell(
                borderRadius: BorderRadius.circular(16),
                onTap:
                    (_isLoading || _isEmailEmpty) ? null : _handlePasswordReset,
                child: const Center(
                  child: Text(
                    'Enviar Instruções',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      letterSpacing: 0.5,
                    ),
                  ),
                ),
                    ),
                  ),
          ),
        );
      },
    );
  }

  Widget _buildBackButton() {
    return Material(
      color: Colors.transparent,
      borderRadius: BorderRadius.circular(8),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: _isLoading ? null : () => Get.back(),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.arrow_back_ios,
                size: 16,
                color: Colors.white,
                shadows: const [
                  Shadow(
                    offset: Offset(0, 1),
                    blurRadius: 2,
                    color: Colors.black26,
                  ),
                ],
              ),
              const SizedBox(width: 4),
              const Text(
                'Voltar ao Login',
          style: TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  shadows: [
                    Shadow(
                      offset: Offset(0, 1),
                      blurRadius: 2,
                      color: Colors.black26,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
