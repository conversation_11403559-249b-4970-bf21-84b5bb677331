import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class UiUtils {
  /// Configure status bar to be transparent with dark icons
  /// Use this for light backgrounds (white, light gradient)
  static void setLightStatusBar() {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light, // iOS
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
  }

  /// Configure status bar to be transparent with light icons
  /// Use this for dark backgrounds (dark gradients, colored headers)
  static void setDarkStatusBar() {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        statusBarBrightness: Brightness.dark, // iOS
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
  }

  /// ✅ FORÇA ÍCONES PRETOS EM TODAS AS TELAS
  /// Use este método para garantir que hora e bateria sempre fiquem pretos
  static void forcarIconesPretos() {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark, // ✅ SEMPRE PRETO
        statusBarBrightness: Brightness.light, // iOS: indica fundo claro
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarDividerColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.dark, // ✅ SEMPRE PRETO
      ),
    );
  }

  /// Apply a transparent status bar with dark icons for light backgrounds
  /// Perfect for the app's light gradient background
  static void setTranslucentStatusBar() {
    // ✅ USAR A NOVA CONFIGURAÇÃO QUE FORÇA ÍCONES PRETOS
    forcarIconesPretos();
  }

  /// Configure edge-to-edge display (full screen with status bar visible)
  static void setEdgeToEdgeDisplay() {
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.edgeToEdge,
      overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom],
    );
  }
}
