import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/user_data_controller.dart';
import '../services/device_manager_service.dart';

class DeviceStatusIndicator extends StatelessWidget {
  const DeviceStatusIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    final userController = Get.find<UserDataController>();

    return Obx(() {
      if (userController.deviceId.value.isEmpty) {
        return const SizedBox.shrink();
      }

      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.green.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.fingerprint,
              size: 14,
              color: Colors.green[700],
            ),
            const SizedBox(width: 4),
            Text(
              'ID: ${userController.deviceId.value.substring(0, 12)}...',
              style: TextStyle(
                fontSize: 11,
                color: Colors.green[700],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    });
  }
}

class DeviceDebugPanel extends StatefulWidget {
  const DeviceDebugPanel({super.key});

  @override
  State<DeviceDebugPanel> createState() => _DeviceDebugPanelState();
}

class _DeviceDebugPanelState extends State<DeviceDebugPanel> {
  final userController = Get.find<UserDataController>();
  Map<String, dynamic> deviceInfo = {};
  DateTime? lastValidation;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadDeviceInfo();
  }

  Future<void> _loadDeviceInfo() async {
    setState(() => isLoading = true);
    try {
      final deviceManager = DeviceManagerService.instance;
      deviceInfo = await deviceManager.getDeviceInfo();
      lastValidation = await deviceManager.getLastValidation();
    } catch (e) {
      debugPrint('Erro ao carregar info do dispositivo: $e');
    }
    setState(() => isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.fingerprint, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'Debug do Dispositivo Robusto',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (isLoading)
              const Center(child: CircularProgressIndicator())
            else ...[
              Obx(() =>
                  _buildInfoRow('Device ID', userController.deviceId.value)),
              _buildInfoRow('Modelo', deviceInfo['model']?.toString() ?? 'N/A'),
              _buildInfoRow(
                  'Sistema', deviceInfo['systemName']?.toString() ?? 'N/A'),
              _buildInfoRow(
                  'Versão', deviceInfo['systemVersion']?.toString() ?? 'N/A'),
              _buildInfoRow('Última Validação',
                  lastValidation?.toString().substring(0, 19) ?? 'Nunca'),
              Obx(() => _buildInfoRow(
                  'Usuário',
                  userController.userDataExists.value
                      ? userController.nomeController.text
                      : 'Nenhum')),
              const SizedBox(height: 12),
              const Divider(),
              Text(
                'Características Principais:',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 8),
              if (deviceInfo.isNotEmpty) ...[
                ...deviceInfo.entries
                    .where((e) => ![
                          'model',
                          'systemName',
                          'systemVersion',
                          'first_generated'
                        ].contains(e.key))
                    .take(3)
                    .map((e) => _buildInfoRow(
                        e.key.replaceAll('_', ' ').toUpperCase(),
                        e.value?.toString().substring(
                                0,
                                e.value.toString().length > 30
                                    ? 30
                                    : e.value.toString().length) ??
                            'N/A')),
              ],
            ],
            const SizedBox(height: 16),
            Text(
              'Ações:',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: () async {
                    await userController.checkUserData();
                    await _loadDeviceInfo();
                    if (mounted && context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('✅ Dados atualizados!')),
                      );
                    }
                  },
                  icon: const Icon(Icons.refresh, size: 16),
                  label:
                      const Text('Atualizar', style: TextStyle(fontSize: 12)),
                  style: ElevatedButton.styleFrom(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () async {
                    await userController.clearUserData();
                    if (mounted && context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                            content: Text('🗑️ Dados do usuário limpos!')),
                      );
                    }
                  },
                  icon: const Icon(Icons.person_remove, size: 16),
                  label: const Text('Limpar Usuário',
                      style: TextStyle(fontSize: 12)),
                  style: ElevatedButton.styleFrom(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    backgroundColor: Colors.orange[100],
                    foregroundColor: Colors.orange[700],
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () async {
                    final deviceManager = DeviceManagerService.instance;
                    await deviceManager.clearDeviceCache();
                    await _loadDeviceInfo();
                    if (mounted && context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                            content: Text('🔄 Cache do dispositivo resetado!')),
                      );
                    }
                  },
                  icon: const Icon(Icons.delete_forever, size: 16),
                  label: const Text('Reset Device',
                      style: TextStyle(fontSize: 12)),
                  style: ElevatedButton.styleFrom(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    backgroundColor: Colors.red[100],
                    foregroundColor: Colors.red[700],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: SelectableText(
              value.isEmpty ? 'Não definido' : value,
              style: TextStyle(
                fontFamily: 'monospace',
                fontSize: 11,
                color: value.isEmpty ? Colors.grey : Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
