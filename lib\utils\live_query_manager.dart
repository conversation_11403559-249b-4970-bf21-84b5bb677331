// lib/utils/live_query_manager.dart
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:parse_server_sdk_flutter/parse_server_sdk_flutter.dart';

/// ✅ ESTADOS DA CONEXÃO LIVEQUERY
enum LiveQueryConnectionState {
  disconnected,
  connecting,
  connected,
  reconnecting,
  failed
}

/// ✅ GERENCIADOR LIVEQUERY OTIMIZADO PARA BACK4APP
class LiveQueryManager {
  static final LiveQueryManager _instance = LiveQueryManager._internal();
  factory LiveQueryManager() => _instance;
  LiveQueryManager._internal();

  // ✅ ESTADO E CONTROLE
  final ValueNotifier<LiveQueryConnectionState> connectionState =
      ValueNotifier(LiveQueryConnectionState.disconnected);

  int _reconnectAttempts = 0;
  static const int _maxReconnectAttempts = 3; // Reduzido para Back4App
  static const Duration _baseReconnectDelay = Duration(seconds: 5);

  Timer? _reconnectTimer;
  LiveQuery? _liveQuery;

  // ✅ CACHE DE SUBSCRIPTIONS ATIVAS
  final Map<String, Subscription> _activeSubscriptions = {};
  String? _lastErrorMessage;

  // ✅ GETTERS
  LiveQuery? get liveQuery => _liveQuery;
  String? get lastErrorMessage => _lastErrorMessage;
  bool get isConnected =>
      connectionState.value == LiveQueryConnectionState.connected;

  /// ✅ INICIALIZAÇÃO ROBUSTA
  Future<LiveQuery?> initializeLiveQuery() async {
    if (_liveQuery != null && isConnected) {
      return _liveQuery;
    }

    try {
      connectionState.value = LiveQueryConnectionState.connecting;
      debugPrint('🔴 [LIVEQUERY] Inicializando conexão...');

      // ✅ VERIFICAR AUTENTICAÇÃO (MELHORADO)
      final currentUser = await ParseUser.currentUser() as ParseUser?;

      // ✅ VERIFICAR AUTENTICAÇÃO SIMPLIFICADA
      if (currentUser == null) {
        throw Exception('Usuário não autenticado - faça login novamente');
      }

      // ✅ VERIFICAR TOKEN DE SESSÃO
      final sessionToken = currentUser.sessionToken;
      if (sessionToken == null || sessionToken.isEmpty) {
        throw Exception('Token de sessão inválido - faça login novamente');
      }

      // ✅ CRIAR NOVA INSTÂNCIA LIVEQUERY
      _liveQuery = LiveQuery();

      // ✅ AGUARDAR CONEXÃO ESTABELECIDA
      final completer = Completer<bool>();
      Timer timeoutTimer = Timer(const Duration(seconds: 10), () {
        if (!completer.isCompleted) {
          completer.complete(false);
        }
      });

      // ✅ TENTAR CONECTAR COM TIMEOUT
      try {
        // Criar uma subscription de teste para verificar conectividade
        final testQuery = QueryBuilder<ParseObject>(ParseObject('_User'))
          ..whereEqualTo('objectId', currentUser!.objectId!)
          ..setLimit(1);

        final testSubscription = await _liveQuery!.client.subscribe(testQuery);

        // Se chegou aqui, a conexão funcionou
        // Não precisamos cancelar a subscription de teste, ela será gerenciada automaticamente

        connectionState.value = LiveQueryConnectionState.connected;
        _reconnectAttempts = 0;
        _lastErrorMessage = null;

        timeoutTimer.cancel();
        completer.complete(true);

        debugPrint('✅ [LIVEQUERY] Conexão estabelecida com sucesso');
      } catch (e) {
        timeoutTimer.cancel();
        completer.complete(false);
        throw e;
      }

      final success = await completer.future;
      if (!success) {
        throw Exception('Timeout na conexão LiveQuery');
      }

      return _liveQuery;
    } catch (e) {
      _lastErrorMessage = e.toString();
      connectionState.value = LiveQueryConnectionState.failed;

      debugPrint('❌ [LIVEQUERY] Erro na conexão: $_lastErrorMessage');

      // ✅ PROGRAMAR RECONEXÃO INTELIGENTE
      _scheduleReconnect();
      return null;
    }
  }

  /// ✅ RECONEXÃO COM BACKOFF EXPONENCIAL
  void _scheduleReconnect() {
    _reconnectTimer?.cancel();

    if (_reconnectAttempts >= _maxReconnectAttempts) {
      debugPrint(
          '🛑 [LIVEQUERY] Máximo de tentativas atingido. Parando reconexões automáticas.');
      connectionState.value = LiveQueryConnectionState.failed;
      return;
    }

    final waitTime = Duration(
      seconds: _baseReconnectDelay.inSeconds * (_reconnectAttempts + 1),
    );

    _reconnectAttempts++;
    connectionState.value = LiveQueryConnectionState.reconnecting;

    debugPrint(
        '🔄 [LIVEQUERY] Tentativa $_reconnectAttempts em ${waitTime.inSeconds}s');

    _reconnectTimer = Timer(waitTime, () async {
      debugPrint('🔄 [LIVEQUERY] Reconectando...');
      _liveQuery = null; // Forçar nova conexão
      await initializeLiveQuery();
    });
  }

  /// ✅ SUBSCRIÇÃO INTELIGENTE
  Future<Subscription?> subscribe(QueryBuilder query) async {
    try {
      // ✅ GARANTIR CONEXÃO ATIVA
      final lq = await initializeLiveQuery();
      if (lq == null) {
        debugPrint(
            '❌ [LIVEQUERY] Não foi possível estabelecer conexão para subscrição');
        return null;
      }

      final className = query.object.parseClassName;
      final subscriptionKey =
          '${className}_${DateTime.now().millisecondsSinceEpoch}';

      debugPrint('🔔 [LIVEQUERY] Inscrevendo-se: $className');

      // ✅ CRIAR SUBSCRIÇÃO
      final subscription = await lq.client.subscribe(query);

      // ✅ GERENCIAR SUBSCRIÇÕES ATIVAS
      _activeSubscriptions[subscriptionKey] = subscription;

      // ✅ CONFIGURAR HANDLERS DE ERRO
      subscription.on(LiveQueryEvent.error, (error) {
        debugPrint('❌ [LIVEQUERY] Erro na subscrição $className: $error');
        _activeSubscriptions.remove(subscriptionKey);
      });

      debugPrint('✅ [LIVEQUERY] Subscrição ativa para $className');
      return subscription;
    } catch (e) {
      _lastErrorMessage = e.toString();
      debugPrint('❌ [LIVEQUERY] Erro ao subscrever: $_lastErrorMessage');
      return null;
    }
  }

  /// ✅ CANCELAR SUBSCRIÇÃO
  Future<void> unsubscribe(Subscription? subscription) async {
    if (subscription == null || _liveQuery == null) return;

    try {
      // ✅ USAR MÉTODO CORRETO DO PARSE SDK
      _liveQuery!.client.unSubscribe(subscription);

      // ✅ REMOVER DO CACHE
      _activeSubscriptions.removeWhere((key, value) => value == subscription);

      debugPrint('🔕 [LIVEQUERY] Subscrição cancelada');
    } catch (e) {
      debugPrint('⚠️ [LIVEQUERY] Erro ao cancelar subscrição: $e');
    }
  }

  /// ✅ DESCONEXÃO LIMPA
  Future<void> disconnect() async {
    try {
      debugPrint('🔌 [LIVEQUERY] Desconectando...');

      // ✅ CANCELAR TIMER DE RECONEXÃO
      _reconnectTimer?.cancel();
      _reconnectTimer = null;

      // ✅ CANCELAR TODAS AS SUBSCRIÇÕES
      for (final subscription in _activeSubscriptions.values) {
        try {
          _liveQuery?.client.unSubscribe(subscription);
        } catch (e) {
          debugPrint('⚠️ [LIVEQUERY] Erro ao cancelar subscrição: $e');
        }
      }
      _activeSubscriptions.clear();

      // ✅ LIMPAR CONEXÃO
      _liveQuery = null;
      connectionState.value = LiveQueryConnectionState.disconnected;
      _reconnectAttempts = 0;
      _lastErrorMessage = null;

      debugPrint('✅ [LIVEQUERY] Desconectado com sucesso');
    } catch (e) {
      debugPrint('⚠️ [LIVEQUERY] Erro durante desconexão: $e');
    }
  }

  /// ✅ FORÇAR RECONEXÃO
  Future<void> forceReconnect() async {
    debugPrint('🔄 [LIVEQUERY] Forçando reconexão...');
    await disconnect();
    _reconnectAttempts = 0;
    await initializeLiveQuery();
  }

  /// ✅ INFORMAÇÕES DE STATUS
  Map<String, dynamic> getStatus() {
    return {
      'state': connectionState.value.toString(),
      'connected': isConnected,
      'reconnectAttempts': _reconnectAttempts,
      'activeSubscriptions': _activeSubscriptions.length,
      'lastError': _lastErrorMessage,
      'hasLiveQuery': _liveQuery != null,
    };
  }

  /// ✅ LIMPEZA FINAL
  void dispose() {
    _reconnectTimer?.cancel();
    connectionState.dispose();
  }
}
