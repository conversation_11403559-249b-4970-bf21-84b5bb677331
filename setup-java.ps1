# Script para Configuração Automática do Java
# Execute como Administrador no PowerShell

param(
    [string]$JavaVersion = "21",
    [switch]$Help
)

if ($Help) {
    Write-Host @"
🔧 Script de Configuração do Java para Flutter/Android

USO:
    .\setup-java.ps1 [-JavaVersion <versão>] [-Help]

PARÂMETROS:
    -JavaVersion    Versão do Java a configurar (padrão: 21)
    -Help          Mostra esta ajuda

EXEMPLOS:
    .\setup-java.ps1                    # Configura Java 21
    .\setup-java.ps1 -JavaVersion 17    # Configura Java 17
    .\setup-java.ps1 -Help              # Mostra ajuda

REQUISITOS:
    - Execute como Administrador
    - Java JDK deve estar instalado
"@
    exit 0
}

Write-Host "🚀 Iniciando configuração do Java $JavaVersion..." -ForegroundColor Green

# Verificar se está executando como administrador
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ Este script deve ser executado como Administrador!" -ForegroundColor Red
    Write-Host "💡 Clique com botão direito no PowerShell e selecione 'Executar como administrador'" -ForegroundColor Yellow
    exit 1
}

# Possíveis caminhos de instalação do Java
$possiblePaths = @(
    "C:\Program Files\Java\jdk-$JavaVersion",
    "C:\Program Files\Java\jdk$JavaVersion",
    "C:\Program Files\Eclipse Adoptium\jdk-$JavaVersion.0.1-hotspot",
    "C:\Program Files\Eclipse Adoptium\jdk-$JavaVersion.0.2-hotspot",
    "C:\Program Files\Eclipse Foundation\jdk-$JavaVersion",
    "C:\Program Files (x86)\Java\jdk-$JavaVersion",
    "C:\Java\jdk-$JavaVersion"
)

# Procurar instalação do Java
$javaPath = $null
foreach ($path in $possiblePaths) {
    if (Test-Path $path) {
        $javaPath = $path
        break
    }
}

# Se não encontrou, tentar buscar automaticamente
if (-not $javaPath) {
    Write-Host "🔍 Procurando instalações do Java..." -ForegroundColor Yellow
    
    $javaInstalls = Get-ChildItem "C:\Program Files\Java" -Directory -ErrorAction SilentlyContinue | Where-Object { $_.Name -like "*jdk*" }
    $adoptiumInstalls = Get-ChildItem "C:\Program Files\Eclipse Adoptium" -Directory -ErrorAction SilentlyContinue | Where-Object { $_.Name -like "*jdk*" }
    
    $allInstalls = @()
    if ($javaInstalls) { $allInstalls += $javaInstalls }
    if ($adoptiumInstalls) { $allInstalls += $adoptiumInstalls }
    
    if ($allInstalls.Count -gt 0) {
        Write-Host "📋 Instalações do Java encontradas:" -ForegroundColor Cyan
        for ($i = 0; $i -lt $allInstalls.Count; $i++) {
            Write-Host "  [$i] $($allInstalls[$i].FullName)" -ForegroundColor White
        }
        
        $choice = Read-Host "Escolha o número da instalação (0-$($allInstalls.Count-1)) ou pressione Enter para usar a primeira"
        
        if ([string]::IsNullOrEmpty($choice)) {
            $choice = 0
        }
        
        if ($choice -ge 0 -and $choice -lt $allInstalls.Count) {
            $javaPath = $allInstalls[$choice].FullName
        }
    }
}

if (-not $javaPath) {
    Write-Host "❌ Java JDK $JavaVersion não encontrado!" -ForegroundColor Red
    Write-Host "📥 Baixe e instale o JDK primeiro:" -ForegroundColor Yellow
    Write-Host "   • Oracle JDK: https://www.oracle.com/java/technologies/downloads/" -ForegroundColor White
    Write-Host "   • OpenJDK: https://adoptium.net/" -ForegroundColor White
    exit 1
}

Write-Host "✅ Java encontrado em: $javaPath" -ForegroundColor Green

# Verificar se java.exe existe
$javaExe = Join-Path $javaPath "bin\java.exe"
if (-not (Test-Path $javaExe)) {
    Write-Host "❌ java.exe não encontrado em: $javaExe" -ForegroundColor Red
    exit 1
}

# Configurar JAVA_HOME
Write-Host "🔧 Configurando JAVA_HOME..." -ForegroundColor Cyan
try {
    [Environment]::SetEnvironmentVariable("JAVA_HOME", $javaPath, "Machine")
    Write-Host "✅ JAVA_HOME definido: $javaPath" -ForegroundColor Green
} catch {
    Write-Host "❌ Erro ao definir JAVA_HOME: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Configurar PATH
Write-Host "🔧 Configurando PATH..." -ForegroundColor Cyan
try {
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
    $javaBinPath = "%JAVA_HOME%\bin"
    
    # Remover entradas antigas do Java do PATH
    $pathEntries = $currentPath -split ";"
    $cleanedEntries = $pathEntries | Where-Object { 
        $_ -notlike "*java*bin*" -and 
        $_ -notlike "*jdk*bin*" -and 
        $_ -ne $javaBinPath
    }
    
    # Adicionar nova entrada no início
    $newPath = ($javaBinPath, $cleanedEntries) -join ";"
    
    [Environment]::SetEnvironmentVariable("PATH", $newPath, "Machine")
    Write-Host "✅ PATH atualizado com: $javaBinPath" -ForegroundColor Green
} catch {
    Write-Host "❌ Erro ao atualizar PATH: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Atualizar variáveis de ambiente na sessão atual
$env:JAVA_HOME = $javaPath
$env:PATH = "$javaPath\bin;" + $env:PATH

# Verificar configuração
Write-Host "`n🧪 Verificando configuração..." -ForegroundColor Cyan

try {
    $javaVersionOutput = & "$javaPath\bin\java.exe" -version 2>&1
    Write-Host "✅ Java Version:" -ForegroundColor Green
    Write-Host $javaVersionOutput -ForegroundColor White
    
    $javacVersionOutput = & "$javaPath\bin\javac.exe" -version 2>&1
    Write-Host "✅ Javac Version:" -ForegroundColor Green
    Write-Host $javacVersionOutput -ForegroundColor White
    
} catch {
    Write-Host "⚠️  Erro na verificação: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Criar arquivo de configuração para Gradle
$gradlePropsPath = "gradle-java-config.properties"
$gradleConfig = @"
# Configuração do Java para Gradle/Flutter
# Adicione estas linhas ao seu android/gradle.properties

# Caminho do Java (use barras duplas no Windows)
org.gradle.java.home=$($javaPath -replace '\\', '\\')

# Configurações JVM otimizadas
org.gradle.jvmargs=-Xmx4g -XX:MaxMetaspaceSize=1g -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -XX:+UseG1GC -Xlint:-options

# Configurações de performance
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.caching=true
"@

$gradleConfig | Out-File -FilePath $gradlePropsPath -Encoding UTF8
Write-Host "📝 Configuração salva em: $gradlePropsPath" -ForegroundColor Cyan

Write-Host "`n🎉 Configuração concluída com sucesso!" -ForegroundColor Green
Write-Host "📋 Próximos passos:" -ForegroundColor Yellow
Write-Host "   1. Feche todos os terminais/IDEs abertos" -ForegroundColor White
Write-Host "   2. Abra um novo PowerShell/CMD" -ForegroundColor White
Write-Host "   3. Execute: java -version" -ForegroundColor White
Write-Host "   4. Execute: echo `$env:JAVA_HOME" -ForegroundColor White
Write-Host "   5. Copie o conteúdo de '$gradlePropsPath' para android/gradle.properties" -ForegroundColor White
Write-Host "`n🔄 Reinicie o computador se houver problemas" -ForegroundColor Cyan
